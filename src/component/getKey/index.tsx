import { useEffect, useMemo, useState } from 'react'
import { useLocation } from 'react-router-dom'
import { Modal, Input, message } from 'antd'

export const GetKey: React.FC<{
  open: boolean
  onChange: (value: string) => void
  onClose: (open: boolean) => void
}> = ({ open, onChange, onClose }) => {
  const { search } = useLocation()
  const searchParams = useMemo(() => new URLSearchParams(search), [search])
  const pathKey = searchParams.get('key')
  const [visible, setVisible] = useState(false)
  const [key, setKey] = useState('')

  useEffect(() => {
    setKey(pathKey || '')
    if (pathKey) {
      setVisible(false)
      onClose(false)
      onChange(pathKey)
    } else {
      setVisible(true)
    }
  }, [pathKey])

  useEffect(() => {
    if (open) {
      setVisible(true)
    }
  }, [open])

  const handleOk = () => {
    if (!key) {
      message.warning('请输入有效的 Key!')
      return
    }
    onChange(key)

    // 关闭模态框
    setVisible(false)
    onClose(false)
    // 清空输入
    setKey('')
  }

  const handleCancel = () => {
    setVisible(false)
    onClose(false)
    onChange('')
    setKey('')
  }

  return (
    <>
      <Modal title='请输入 Key' open={visible} onOk={handleOk} onCancel={handleCancel} maskClosable={false}>
        <Input value={key} onChange={e => setKey(e.target.value)} placeholder='请输入你的 Key' />
      </Modal>
    </>
  )
}

export default GetKey
