// src/hooks/useAudioRecorder.ts

import { useState, useEffect } from 'react';

interface UseAudioRecorderResult {
  isRecording: boolean;
  recordingStatus: string;
  audioBlob: Blob | null;
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<void>;
}

export const useAudioRecorder = (): UseAudioRecorderResult => {
  const [isRecording, setIsRecording] = useState(false);
  const [recordingStatus, setRecordingStatus] = useState('未开始');
  const [audioContext, setAudioContext] = useState<AudioContext | null>(null);
  const [recordedAudioData, setRecordedAudioData] = useState<Float32Array[]>([]);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);

  const startRecording = async () => {
    console.log('🎤 开始录音函数被调用');

    try {
      // 检查浏览器兼容性
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('您的浏览器不支持录音功能');
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      });

      const context = new (window.AudioContext || window.webkitAudioContext)({
        sampleRate: 16000
      });

      const source = context.createMediaStreamSource(stream);
      const processor = context.createScriptProcessor(4096, 1, 1);

      processor.onaudioprocess = function (event) {
        const inputBuffer = event.inputBuffer;
        const inputData = inputBuffer.getChannelData(0);
        const audioData = new Float32Array(inputData);
        setRecordedAudioData(prevData => [...prevData, audioData]);
      };

      source.connect(processor);
      processor.connect(context.destination);

      // 保存引用以便停止时断开连接
      (window as any).currentProcessor = processor;
      (window as any).currentSource = source;
      (window as any).currentStream = stream;
      
      setAudioContext(context);
      setIsRecording(true);
      setRecordingStatus('正在录音...');

    } catch (error) {
      let errorMessage = '录音失败：';
      
      if (error.name === 'NotAllowedError') {
        errorMessage += '麦克风权限被拒绝';
      } else if (error.name === 'NotFoundError') {
        errorMessage += '未找到麦克风设备';
      } else {
        errorMessage += error.message || '未知错误';
      }
      
      console.error(errorMessage);
      alert(errorMessage);
      setRecordingStatus('录音失败');
    }
  };

  const stopRecording = async () => {
    console.log('⏹️ 停止录音函数被调用');

    if (!isRecording) {
      console.warn('没有正在进行的录音');
      return;
    }

    try {
      // 停止音频处理
      if ((window as any).currentProcessor) {
        (window as any).currentProcessor.disconnect();
        delete (window as any).currentProcessor;
      }

      if ((window as any).currentSource) {
        (window as any).currentSource.disconnect();
        delete (window as any).currentSource;
      }

      if ((window as any).currentStream && (window as any).currentStream.getTracks) {
        (window as any).currentStream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
        delete (window as any).currentStream;
      }

      // 只有当audioContext存在且处于运行状态时才需要关闭
      if (audioContext && audioContext.state !== 'closed') {
        await audioContext.close();
        setAudioContext(null);
      }
      
      // 合并所有录音数据
      if (recordedAudioData.length > 0) {
        let totalLength = 0;
        recordedAudioData.forEach(buffer => {
          totalLength += buffer.length;
        });

        const mergedData = new Float32Array(totalLength);
        let offset = 0;
        recordedAudioData.forEach(buffer => {
          mergedData.set(buffer, offset);
          offset += buffer.length;
        });

        // 创建WAV Blob
        const wavBlob = encodeWAV(mergedData);
        setAudioBlob(wavBlob);
        
        // 清除录音数据
        setRecordedAudioData([]);
      }
      
      // 更新状态
      setIsRecording(false);
      setRecordingStatus('录音已停止');
      
    } catch (error) {
      console.error('停止录音时发生错误:', error);
      alert('停止录音时发生错误: ' + error.message);
    }
  };

  const encodeWAV = (input: Float32Array): Blob => {
    // 确保与原始实现完全一致
    const buffer = new ArrayBuffer(44 + input.length * 2);
    const view = new DataView(buffer);
    
    writeString(view, 0, 'RIFF');
    view.setUint32(4, 36 + input.length * 2, true);
    writeString(view, 8, 'WAVE');
    writeString(view, 12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true); // PCM格式
    view.setUint16(22, 1, true); // 单声道
    view.setUint32(24, 16000, true); // 采样率
    view.setUint32(28, 16000 * 2, true); // 字节率
    view.setUint16(32, 2, true); // 块对齐
    view.setUint16(34, 16, true); // 位深度
    writeString(view, 36, 'data');
    view.setUint32(40, input.length * 2, true);
    
    // 写入PCM数据
    let index = 44;
    for (let i = 0; i < input.length; i++) {
      const s = Math.max(-1, Math.min(1, input[i]));
      const val = s < 0 ? s * 0x8000 : s * 0x7FFF;
      view.setInt16(index, val, true);
      index += 2;
    }
    
    return new Blob([view], { type: 'audio/wav' });
  };
  
  const writeString = (view: DataView, offset: number, str: string) => {
    for (let i = 0; i < str.length; i++) {
      view.setUint8(offset + i, str.charCodeAt(i));
    }
  };

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      // 在组件卸载时清理所有资源
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close();
      }
      if ((window as any).currentProcessor) {
        (window as any).currentProcessor.disconnect();
        delete (window as any).currentProcessor;
      }
      if ((window as any).currentSource) {
        (window as any).currentSource.disconnect();
        delete (window as any).currentSource;
      }
      if ((window as any).currentStream && (window as any).currentStream.getTracks) {
        (window as any).currentStream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
        delete (window as any).currentStream;
      }
    };
  }, []);

  return {
    isRecording,
    recordingStatus,
    audioBlob,
    startRecording,
    stopRecording
  };
};