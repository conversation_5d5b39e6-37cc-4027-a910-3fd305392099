import { matchPath, useLocation } from "react-router-dom";
import { useEffect } from "react";
import { routes } from "@/router";
interface Route {
  path: string
  title?: string
  children?: Route[]
}

function findMatchedRoute(pathname: string, routesPath: Route[]): { route: Route } | null {
  for (const route of routesPath) {
    // 检查当前路由是否匹配
    const match = matchPath(
      { path: route.path, end: !route.children },
      pathname
    )

    if (match) {
      // 如果有子路由，继续递归查找
      if (route.children) {
        const childMatch = findMatchedRoute(pathname, route.children)
        if (childMatch) return childMatch
      }
      return { route }
    }
  }
  return null
}

export default function useDocumentTitle(defaultTitle: string) {
  const location = useLocation();

  useEffect(() => {
    const matched = findMatchedRoute(location.pathname, routes)

    if (matched) {
      const { route } = matched
      // 处理标题，支持函数形式和字符串形式
      const title = route.title

      document.title = title ? `${title} - ${defaultTitle}` : defaultTitle
    } else {
      document.title = defaultTitle
    }
  }, [location, defaultTitle])
}
