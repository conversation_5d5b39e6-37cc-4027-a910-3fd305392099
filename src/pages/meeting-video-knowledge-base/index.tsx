import React, { useEffect, useMemo, useState } from 'react'
import { Button, message, Typography, Spin, Layout, Flex } from 'antd'
import { meetingVideoKnowledge } from '@/api/meetingVideoKnowledge'
import { uploadFile } from '@/api/template'
import { extractContent } from '@/utils/common'
import StreamTypewriter from '@/component/StreamTypewriter'
import { useLocation } from 'react-router-dom'
import MarkdownIt from 'markdown-it'
import {
  Document,
  Packer,
  Paragraph,
  TextRun,
  HeadingLevel,
  TableCell,
  TableRow,
  Table,
  WidthType,
} from 'docx'
import { saveAs } from 'file-saver'
import FunVSRStreamTester from './FunVSRStreamTester'
import './index.less'

const { Title, Text } = Typography
const token = import.meta.env['VITE_MEETING_VIDEO_TOKEN'] || ''

export const MeetingVideoKnowledgeBase: React.FC = () => {
  const { search } = useLocation()
  const searchParams = useMemo(() => new URLSearchParams(search), [search])
  const Tenantid = searchParams.get('tenantid') || ''
  const Token = searchParams.get('token') || ''
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState(false)
  const [mk, setMk] = useState('')
  const [isShowMk, setIsShowMk] = useState(false)
  const [streamTypewriterKey, setStreamTypewriterKey] = useState(1)
  const scrollRef = React.useRef<HTMLDivElement>(null)
  const [personalLibs, setPersonalLibs] = useState<string[]>([])
  const [query, setQuery] = useState<string>('')

  const beforeUpload = (file: File) => {
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf('.') + 1)
      ?.toLowerCase()
    if (
      !['mp3', 'wav', 'm4a', 'webm', 'amr', 'mpga'].includes(originalFileExt)
    ) {
      return false
    }
    messageApi.open({
      key: 'uploading',
      type: 'loading',
      content: '文件上传中',
    })
    setGenerating(true)
    uploadFile(file, token)
      .then((response) => {
        setGenerating(false)
        if (response && response.id) {
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1,
          })
          return false // 阻止自动上传
        } else {
          throw new Error('上传失败：未收到有效的响应')
        }
      })
      .catch((error) => {
        setGenerating(false)
        messageApi.open({
          key: 'uploading',
          type: 'error',
          content: `文件上传失败: ${error.message}`,
          duration: 2,
        })
      })
    return false // 阻止自动上传
  }

  const handleStart = async (libs: string[], query: string) => {
    setQuery(query)
    setPersonalLibs(libs)
    setStreamTypewriterKey(streamTypewriterKey + 1)
    setMk('')
    setIsShowMk(true)
    setGenerating(true)
    messageApi.success('开始校验，请稍候...')
    let res = ''
    try {
      await meetingVideoKnowledge(
        {
          query: query || '生成会议纪要',
          Token,
          tenantid: Tenantid,
          personalLibs: libs.join(','),
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              setMk((p) => p + message || '')
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {
            setGenerating(false)
          },
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  const handleExport = (obj: any) => {
    try {
      const md = new MarkdownIt({ html: true })
      const html = md.render(mk)

      // 解析 HTML 并转换为 docx 格式的段落
      const parser = new DOMParser()
      const docEl = parser.parseFromString(html, 'text/html').body
      const paragraphs: Paragraph[] = []

      // 遍历 HTML 节点并转换为 docx 段落
      docEl.childNodes.forEach((node) => {
        if (node.nodeType === Node.TEXT_NODE && node.textContent) {
          // 纯文本
          paragraphs.push(
            new Paragraph({
              children: [new TextRun(node.textContent)],
            })
          )
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement
          switch (element.tagName.toLowerCase()) {
            case 'html':
              // 如果需要处理整个 HTML 文档，可以在这里添加逻辑
              paragraphs.push(
                new Paragraph({
                  children: [new TextRun('HTML Document Start')],
                })
              )
              break
            case 'body':
              // 如果需要处理 body 标签内容，可以在这里添加逻辑
              paragraphs.push(
                new Paragraph({
                  children: [new TextRun('Body Content Start')],
                })
              )
              break
            case 'h1':
              paragraphs.push(
                new Paragraph({
                  heading: HeadingLevel.HEADING_1,
                  children: [new TextRun(element.textContent || '')],
                })
              )
              break
            case 'h2':
              paragraphs.push(
                new Paragraph({
                  heading: HeadingLevel.HEADING_2,
                  children: [new TextRun(element.textContent || '')],
                })
              )
              break
            case 'p':
              paragraphs.push(
                new Paragraph({
                  children: [new TextRun(element.textContent || '')],
                })
              )
              break
            case 'ul':
              element.querySelectorAll('li').forEach((li) => {
                paragraphs.push(
                  new Paragraph({
                    children: [
                      new TextRun({
                        text: `• ${li.textContent || ''}`,
                        bold: li.innerHTML.includes('<strong>'),
                      }),
                    ],
                    indent: { left: 720 },
                  })
                )
              })
              break
            case 'table':
              const rows = element.querySelectorAll('tr')
              const tableRows: TableRow[] = []

              rows.forEach((row) => {
                const cells = Array.from(row.cells)
                const tableCells: TableCell[] = []

                cells.forEach((cell) => {
                  const colspan = parseInt(
                    cell.getAttribute('colspan') || '1',
                    10
                  )
                  const rowspan = parseInt(
                    cell.getAttribute('rowspan') || '1',
                    10
                  )
                  const cellText = cell.textContent?.trim() || ''

                  // 创建表格单元格
                  tableCells.push(
                    new TableCell({
                      children: [new Paragraph(cellText)],
                      columnSpan: colspan > 1 ? colspan : undefined,
                      rowSpan: rowspan > 1 ? rowspan : undefined,
                    })
                  )
                })

                // 创建表格行
                tableRows.push(new TableRow({ children: tableCells }))
              })

              // 创建表格并添加到文档
              const table = new Table({
                rows: tableRows,
                width: { size: 100, type: WidthType.PERCENTAGE }, // 设置表格宽度为 100%
              })

              paragraphs.push(table as any)
              break
            default:
              paragraphs.push(
                new Paragraph({
                  children: [new TextRun(element.textContent || '')],
                })
              )
          }
        }
      })

      // 创建 Word 文档
      const doc = new Document({
        styles: {
          paragraphStyles: [
            {
              id: 'Heading1',
              name: 'Heading 1',
              run: {
                size: 32,
                bold: true,
                color: '2e6c80',
              },
            },
          ],
        },
        sections: [{ children: paragraphs }],
      })

      // 导出 Word
      Packer.toBlob(doc).then((blob) => {
        saveAs(blob, `会议纪要${new Date().getTime()}.docx`)
      })
    } catch (error) {
      console.error('导出失败:', error)
    }
  }

  return (
    <div className="review-material-container">
      <Spin tip="加载中" spinning={generating} fullscreen size="large" />
      {contextHolder}
      <Layout style={{ backgroundColor: '#f0f2f5', height: '100%' }}>
        <Layout>
          <Layout.Sider
            width={isShowMk ? '600px' : '100%'}
            className="scroll-container"
            style={{ backgroundColor: '#f0f2f5', overflow: 'auto' }}
          >
            <FunVSRStreamTester
              Tenantid={Tenantid}
              Token={Token}
              handleStart={handleStart}
              generating={generating}
            />
          </Layout.Sider>
          {isShowMk && (
            <Layout.Content
              style={{
                background: '#fff',
                height: '100vh',
                borderRadius: 8,
                padding: '20px 0',
              }}
            >
              <Flex align="center" justify="center" style={{ height: '45px' }}>
                <Title level={3}>总结报告</Title>
              </Flex>

              <div
                className="scroll-container"
                ref={scrollRef}
                style={{
                  height: 'calc(100vh - 140px)',
                  overflowY: 'auto',
                  padding: '0 20px',
                }}
              >
                <StreamTypewriter
                  key={streamTypewriterKey}
                  text={mk}
                  end={!generating}
                  onchange={() => {
                    scrollRef.current?.scrollTo({
                      top: scrollRef.current.scrollHeight,
                      behavior: 'smooth',
                    })
                  }}
                  components={{
                    p: ({ node, ...props }: any) => (
                      <p
                        style={{ whiteSpace: 'pre-line', marginTop: 10 }}
                        {...props}
                      />
                    ),
                  }}
                />
              </div>

              <Flex
                justify="center"
                style={{ height: '45px', marginTop: 20 }}
                gap={20}
              >
                <Button onClick={() => handleStart(personalLibs, query)}>
                  重新生成
                </Button>
                <Button type="primary" onClick={handleExport}>
                  导出word
                </Button>
              </Flex>
            </Layout.Content>
          )}
        </Layout>
      </Layout>
    </div>
  )
}

export default MeetingVideoKnowledgeBase
