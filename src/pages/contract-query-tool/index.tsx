/** 模板生成工具 */
import {
  <PERSON><PERSON>,
  Card,
  Flex,
  message,
  Modal,
  Spin,
  Table,
  Typography,
  Upload,
  Tabs,
  Popconfirm,
} from "antd";
import { useCallback, useMemo, useState } from "react";
import { useLocation } from "react-router-dom";
import {
  contractQueryTool,
  selectContract,
  addByFile,
  addNote,
} from "@/api/contractQueryTool";
import {
  CheckCircleFilled,
  InboxOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { extractJSONFromString } from "@/utils/json-extractor";
import type { TableColumnsType, TableProps, TabsProps } from "antd";
import { extractContent } from "@/utils/common";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import RemarkBreaks from "remark-breaks";
import RemarkMath from "remark-math";
import StreamTypewriter from "@/component/StreamTypewriter";
import "./index.less";

interface DataType {
  eossContId: string;
  contNo: React.Key;
  contName?: string;
  contAmt?: string;
  eossContType?: string;
}

const columns: TableColumnsType<DataType> = [
  {
    title: "eoss合同id",
    dataIndex: "eossContId",
  },
  {
    title: "合同编号",
    dataIndex: "contNo",
  },
  {
    title: "合同名称",
    dataIndex: "contName",
  },
  {
    title: "合同金额",
    dataIndex: "contAmt",
  },
  {
    title: "Eoss合同类型",
    dataIndex: "eossContType",
  },
];

export const TemplateRenderer = () => {
  const { search } = useLocation();
  const searchParams = useMemo(() => new URLSearchParams(search), [search]);
  const Tenantid = searchParams.get("tenantid") || "";
  const Token = searchParams.get("token") || "";
  const [messageApi, contextHolder] = message.useMessage();
  const [selectedRows, setSelectedRows] = useState<DataType[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [globalLoading, setGlobalLoading] = useState<boolean>(false);
  const [disabledTag2, setDisabledTag2] = useState<boolean>(true);
  const [selectedKey, setSelectedKey] = useState<string>("1");
  const [tableData, setTableData] = useState<DataType[]>([]);
  // 点击【开始生成】后，状态变更
  const [startSending, setStartSending] = useState<boolean>(false);
  // 生成状态
  const [generating, setGenerating] = useState<boolean>(false);
  // 生成消息
  const [messages1, setMessages1] = useState<string>("");
  const [messages2, setMessages2] = useState<string>("");
  // 生成异常
  const [error, setError] = useState<string | null>(null);

  const [uploadedFile, setUploadedFile] = useState<any>({});

  const handleGeneration = useCallback(
    async (type: string) => {
      setGenerating(true);
      setGlobalLoading(true);
      setError(null);
      if (type === "1") {
        setDisabledTag2(true);
        setMessages1("");
      } else if (type === "2") {
        setDisabledTag2(false);
        setMessages2("");
      }
      let str = "";
      try {
        let obj: any = {
          type,
          content: uploadedFile.fileContent,
          eoss: "",
        };
        if (type === "2") {
          obj.eoss = JSON.stringify(selectedRows[0]);
        }
        await contractQueryTool(obj, {
          onMessage: (text, finished) => {
            setGlobalLoading(false);
            if (text) {
              if (type === "1") {
                setMessages1((prev) => prev + text);
              } else if (type === "2") {
                setMessages2((prev) => prev + text);
              }
              str += text;
            }
            if (finished) {
              setGenerating(false);
            }
          },
          onError: (error) => {
            setError(error.message);
            setGenerating(false);
            setGlobalLoading(false);
          },
          onFinish: () => {},
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : "生成过程中发生错误");
        setGenerating(false);
        setGlobalLoading(false);
      }
    },
    [uploadedFile, selectedRows, generating]
  );

  const getContract = (obj: {
    contName?: string;
    customerFullName?: string;
    contNo?: string;
    contAmt?: string | number | undefined;
  }) => {
    console.log(obj, 213);
    selectContract(obj).then((response) => {
      if (response.code === 200) {
        setTableData(response.data);
        showModal();
      }
    });
  };

  const handleAddNote = (str: string) => {
    let obj = { ...selectedRows[0] };
    const url = new URL(
      `https://eoss.sino-bridge.com/eoss/contract/detail?id=${obj.eossContId}&type=2&tagContractName=${obj.contName}`
    );
    addNote(
      {
        title: "合同查询结果",
        content: str,
        type: "PLAIN",
        url: url.toString(),
        noteStyle: {
          noteType: "absolute",
          noteTop: 0,
          noteLeft: 0,
          noteWidth: 345,
          noteHeight: 240,
        },
      },
      Tenantid,
      Token
    ).then((response) => {
      if (response.code === 200) {
        messageApi.open({
          key: "uploading",
          type: "success",
          content: "添加成功",
          duration: 1,
        });
      }
    });
  };

  const beforeUpload = (file: File) => {
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf(".") + 1)
      .toLocaleLowerCase();
    if (["pdf"].includes(originalFileExt)) {
      messageApi.open({
        key: "uploading",
        type: "loading",
        content: "文件上传中",
      });
      setGlobalLoading(true);
      addByFile(
        { file, dirId: "1901939995839451137", libName: file.name },
        Tenantid,
        Token
      )
        .then(async (response) => {
          setGlobalLoading(false);
          if (response.code === 200) {
            setUploadedFile({ ...response.data, name: file.name });
            messageApi.open({
              key: "uploading",
              type: "success",
              content: "文件上传成功",
              duration: 1,
            });
          } else {
            messageApi.open({
              key: "uploading",
              type: "error",
              content: "文件上传失败",
              duration: 1,
            });
          }
        })
        .catch(() => {
          setGlobalLoading(false);
        });
    } else {
      messageApi.error(
        "目前仅支持.pdf类型的文件，请您将文件转成这些格式后再次进行上传"
      );
    }
    return false;
  };

  const handleGenerationStart = () => {
    if (uploadedFile && uploadedFile.fileContent) {
      setStartSending(true);
      setSelectedKey("1");
      handleGeneration("1");
    } else {
      messageApi.error("请先上传文件");
    }
  };

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    if (!selectedRows.length) {
      messageApi.error("请选择一条合同信息");
      return;
    }
    setSelectedKey("2");
    setIsModalOpen(false);
    setStartSending(true);
    handleGeneration("2");
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const rowSelection: TableProps<DataType>["rowSelection"] = {
    onChange: (_selectedRowKeys: React.Key[], selectedRows: DataType[]) => {
      setSelectedRows(selectedRows);
    },
  };

  const onChange = (key: string) => {
    setSelectedKey(key);
  };

  const createNote = () => {
    if (generating) {
      return message.error("请先等结果完整返回后再点击");
    }
    handleAddNote(messages2);
  };
  const openEoss = () => {
    let obj = { ...selectedRows[0] };
    window.open(
      `https://eoss.sino-bridge.com/eoss/contract/detail?id=${obj.eossContId}&type=2&tagContractName=${obj.contName}`
    );
  };

  const contrast = () => {
    let contName = "";
    let contNo = "";
    let contAmt = "";
    // const pattern = /\*\*合同编号\*\*\s*：\s*([^\n]+)/
    // const match = messages1.match(pattern)

    // const pattern1 = /\*\*合同名称\*\*\s*：\s*([^\n]+)/
    // const match1 = messages1.match(pattern1)
    // if (match) {
    //   contNo = match[1].trim()
    // }
    // if (match1) {
    //   contName = match1[1].trim()
    // }

    try {
      const jsonStr = extractContent(messages1, "json");
      if (jsonStr) {
        let obj = JSON.parse(jsonStr);
        contName = obj["合同名称"];
        contAmt = String(obj["合同金额"])?.match(/\d+(\.\d+)?/)?.[0];
        contNo = obj["合同编号"];
      }
      let jsonStr1 = extractJSONFromString(messages1);
      if (jsonStr1) {
        let obj = JSON.parse(jsonStr1);
        contName = obj["合同名称"];
        contAmt = String(obj["合同金额"])?.match(/\d+(\.\d+)?/)?.[0];
        contNo = obj["合同编号"];
      }
    } catch (error) {
      console.log(error);
    }

    if (!contName && !contNo) {
      // && !obj['客户名称']
      messageApi.open({
        key: "uploading",
        type: "error",
        content: "未提取到合同名称或客户名称或合同编号，请重新上传文件尝试",
        duration: 1,
      });
      return;
    }
    getContract({
      contName: contName,
      // customerFullName: obj['客户名称'],
      contNo: contNo,
      contAmt: contAmt,
    });
  };

  const components = {
    json() {
      return <></>;
    },
    code({ node, inline, className, children, ...props }: any) {
      const match = /language-(\w+)/.exec(className || "");
      return !inline && match && ["json"].includes(match[1]) ? null : (
        <ReactMarkdown
          remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}
          rehypePlugins={[rehypeRaw]}
          components={
            {
              json() {
                return <></>;
              },
            } as any
          }
        >
          {children}
        </ReactMarkdown>
      );
    },
  };

  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "合同信息",
      children: (
        <Card className="preview-content custom-scrollbar">
          {messages1 ? (
            <StreamTypewriter
              text={messages1}
              end={!generating}
              components={components}
            />
          ) : (
            <>
              <LoadingOutlined style={{ fontSize: 48, marginBottom: 24 }} />
              <Typography.Text>
                正在提取文件信息，请不要关闭或刷新页面
              </Typography.Text>
            </>
          )}
        </Card>
      ),
    },
    {
      key: "2",
      label: "对比结果",
      disabled: disabledTag2,
      children: (
        <Card className="preview-content custom-scrollbar">
          {messages2 ? (
            <StreamTypewriter text={messages2} end={!generating} />
          ) : (
            <>
              <LoadingOutlined style={{ fontSize: 48, marginBottom: 24 }} />
              <Typography.Text>
                正在提取文件信息，请不要关闭或刷新页面
              </Typography.Text>
            </>
          )}
        </Card>
      ),
    },
  ];

  return (
    <>
      {contextHolder}
      <Spin tip="加载中" spinning={globalLoading} fullscreen size="large" />
      <Flex className="toolbar" justify="space-between">
        <Typography.Text className="title-text">合同解析</Typography.Text>
      </Flex>
      <Flex className="contract-tool-content" gap="large">
        <Card className="template-form">
          <Flex vertical gap="middle">
            <Upload.Dragger
              showUploadList={false}
              multiple={false}
              beforeUpload={beforeUpload}
            >
              <span className="contract-tool-upload-icon">
                {uploadedFile && uploadedFile.name ? (
                  <CheckCircleFilled />
                ) : (
                  <InboxOutlined />
                )}
              </span>
              <br />
              <span className="contract-tool-upload-text">
                {(uploadedFile && uploadedFile.name) ||
                  "点击或者将文件拖拽到这里进行上传"}
              </span>
              <br />
              <span className="contract-tool-upload-hint">
                {uploadedFile && uploadedFile.name ? (
                  "点击或者将文件拖拽到这里重新上传"
                ) : (
                  <>
                    <span>在这里上传您的文件，让AI帮您进行解析</span>
                    <br />
                    <span>目前仅支持上传一个文件，支持.pdf类型</span>
                  </>
                )}
              </span>
            </Upload.Dragger>
            <Button
              size="large"
              type="primary"
              disabled={!uploadedFile || generating}
              onClick={handleGenerationStart}
            >
              开 始 解 析
            </Button>
          </Flex>
        </Card>

        {startSending && (
          <Flex className="preview-panel" vertical gap="middle">
            {selectedKey === "1" ? (
              <Button
                onClick={contrast}
                style={{
                  position: "absolute",
                  right: "0",
                  top: "8px",
                  zIndex: "1",
                }}
              >
                与eoss合同数据对比
              </Button>
            ) : (
              <>
                <Button
                  onClick={openEoss}
                  style={{
                    position: "absolute",
                    right: "100px",
                    top: "8px",
                    zIndex: "1",
                  }}
                >
                  跳转到eoss系统
                </Button>
                <Popconfirm
                  title="提示"
                  description="确认要创建便签吗？"
                  onConfirm={createNote}
                  okText="确认"
                  cancelText="取消"
                >
                  <Button
                    style={{
                      position: "absolute",
                      right: "0",
                      top: "8px",
                      zIndex: "1",
                    }}
                  >
                    创建便签
                  </Button>
                </Popconfirm>
              </>
            )}
            <Tabs
              defaultActiveKey="1"
              activeKey={selectedKey}
              items={items}
              onChange={onChange}
            />
          </Flex>
        )}
      </Flex>

      <Modal
        title="请选择"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        maskClosable={false}
        destroyOnClose
        width={800}
      >
        <Table<DataType>
          rowSelection={{ type: "radio", ...rowSelection }}
          pagination={false}
          columns={columns}
          dataSource={tableData.map((item) => ({ ...item, key: item.contNo }))}
        />
      </Modal>
    </>
  );
};

export default TemplateRenderer;
