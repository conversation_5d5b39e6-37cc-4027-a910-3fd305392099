.contract-scene-set-v2-content {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-shrink: 0;

  .template-form {
    margin: 50px auto 0;
    width: 40%;

    .ant-upload-wrapper {
      height: 150px;
    }
  }

  .contract-scene-set-v2-upload-icon {
    margin-bottom: 16px;
    font-size: 48px;
    color: #1890ff;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  .markdown-body ol,
  .markdown-body ul {
    padding-left: 20px;
  }

  .file-list-contract {
    margin-top: 16px;
    max-height: 200px;
    overflow-y: auto;
    padding-right: 4px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f5f5;
      border-radius: 3px;
    }

    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: #f8f9fa;
      border-radius: 6px;
      margin-bottom: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: #f0f2f5;
      }

      span {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
.contract-scene-set-v2-toolbar {
  background: linear-gradient(180deg, rgba(189, 225, 255, 0.4) 0%, rgba(224, 242, 255, 0) 100%);
  border-radius: 0.5rem 0.5rem 0 0;
  padding: 12px 24px;

  .title-text {
    color: transparent;
    background: linear-gradient(116deg, #1888ff 16%, #2f54eb 88%);
    background-clip: text;
    -webkit-background-clip: text;
    user-select: none;
    font-size: 30px;
    font-weight: bold;
  }
}
