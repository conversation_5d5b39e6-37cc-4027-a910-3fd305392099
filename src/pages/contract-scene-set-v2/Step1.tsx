/** 模板生成工具 */
import { Button, Card, Flex, Form, Input, message, Upload } from 'antd'
import { useState } from 'react'
import { DeleteOutlined, InboxOutlined } from '@ant-design/icons'
import { convertFileToPDF, uploadFile } from '@/api/template'
import { MessageInstance } from 'antd/es/message/interface'
import jsPDF from 'jspdf'
import './index.less'

const { TextArea } = Input
const appKey = import.meta.env['VITE_CONTRACT_SCENE_SET_V2_TOKEN'] || ''
export interface FileItem {
  id: string
  name: string
  url: string
}
interface props {
  onSubmit: (uploadedFiles: FileItem[], keyPoints: string) => void
  messageApi: MessageInstance
  setGlobalLoading: (loading: boolean) => void
}

export const Step1: React.FC<props> = ({ onSubmit, messageApi, setGlobalLoading }) => {
  const [keyPoints, setKeyPoints] = useState<string>(`1.合同基本信息
2.合同标的信息
3.合同付款计划
4.交付与验收
5.合同其他核心条款`)
  const [uploadedFiles, setUploadedFiles] = useState<FileItem[]>([])
  const convertToPDF = async (file: File) => {
    try {
      const doc = new jsPDF()
      let positionY = 10 // 初始Y坐标
      const margin = 10 // 页边距
      const maxWidth = doc.internal.pageSize.getWidth() - 2 * margin

      // 创建图片对象
      const img = new Image()
      img.src = URL.createObjectURL(file)

      // 等待图片加载
      await new Promise(resolve => {
        img.onload = resolve
      })

      // 计算图片在PDF中的高度
      const imgHeight = (img.height * maxWidth) / img.width

      // 检查是否需要添加新页
      if (positionY + imgHeight > doc.internal.pageSize.getHeight() - margin) {
        doc.addPage()
        positionY = margin
      }

      // 添加图片到PDF
      doc.addImage(img, 'JPEG', margin, positionY, maxWidth, imgHeight)

      // 更新Y坐标
      positionY += imgHeight + 10

      // 释放对象URL
      URL.revokeObjectURL(img.src)

      // doc.save('converted.pdf')

      // 获取doc文件对象
      const pdfBlob = await doc.output('blob')

      return pdfBlob
    } catch (error) {
      console.error('转换失败:', error)
    }
  }
  const handleUploadFile = async (blob: any, originalFilename: string) => {
    const pdfFile = new File([blob], `${originalFilename}.pdf`, {
      type: 'application/pdf'
    })
    uploadFile(pdfFile, appKey).then(async response => {
      setGlobalLoading(false)
      if (response.id) {
        setUploadedFiles(prevFiles => [...prevFiles, { url: URL.createObjectURL(pdfFile), ...response }])
        messageApi.open({
          key: 'uploading',
          type: 'success',
          content: '文件上传成功',
          duration: 1
        })
      } else {
        messageApi.open({
          key: 'uploading',
          type: 'error',
          content: '文件上传失败',
          duration: 1
        })
      }
    })
  }

  const beforeUpload = async (file: File) => {
    const originalFilename = file.name.substring(0, file.name.lastIndexOf('.'))
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)?.toLowerCase()
    setGlobalLoading(true)
    if (['png', 'jpg', 'jpeg'].includes(originalFileExt)) {
      let blob = await convertToPDF(file)
      handleUploadFile(blob, originalFilename)
    } else if (['docx', 'doc'].includes(originalFileExt)) {
      convertFileToPDF(file).then(async response => {
        if (response['status'] && response['status'] !== 200) {
          setGlobalLoading(false)
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件处理异常，请稍后重试',
            duration: 1
          })
        } else if ('blob' in response) {
          const blob = await response.blob()
          const pdfFile = new File([blob], `${originalFilename}.pdf`, {
            type: 'application/pdf'
          })
          uploadFile(file, appKey).then(async response => {
            setGlobalLoading(false)
            if (response.id) {
              setUploadedFiles(prevFiles => [...prevFiles, { url: URL.createObjectURL(pdfFile), ...response }])
              messageApi.open({
                key: 'uploading',
                type: 'success',
                content: '文件上传成功',
                duration: 1
              })
            } else {
              messageApi.open({
                key: 'uploading',
                type: 'error',
                content: '文件上传失败',
                duration: 1
              })
            }
          })
        }
      })
    } else if (['pdf'].includes(originalFileExt)) {
      uploadFile(file, appKey).then(async response => {
        setGlobalLoading(false)
        if (response.id) {
          setUploadedFiles(prevFiles => [...prevFiles, { url: URL.createObjectURL(file), ...response }])
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
        } else {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件上传失败',
            duration: 1
          })
        }
      })
    }
    return false // 阻止自动上传
  }

  const onChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setKeyPoints(e.target.value)
  }
  const handleDelete = (fileId: string) => {
    setUploadedFiles(prevFiles => prevFiles.filter(file => file.id !== fileId))
  }
  return (
    <div className='contract-scene-set-v2-content'>
      <Card className='template-form'>
        <Flex vertical gap='middle'>
          <Form layout='vertical'>
            <Form.Item>
              <Upload.Dragger
                showUploadList={false}
                multiple={false}
                beforeUpload={beforeUpload}
                accept='.doc,.docx,.pdf'
              >
                <span className='contract-scene-set-v2-upload-icon'>
                  <InboxOutlined />
                </span>
                <br />
                <span className='contract-scene-set-v2-upload-text'>点击或将文档拖拽到这里上传</span>
                <br />
                <span className='contract-scene-set-v2-upload-hint'>
                  单个合同文件的字数不超过5千字，大约20页，格式支持: doc/docx/pdf
                </span>
              </Upload.Dragger>

              {uploadedFiles.length > 0 && (
                <div className='file-list-contract'>
                  {uploadedFiles.map(file => (
                    <div key={file.id} className='file-item'>
                      <span>{file.name}</span>
                      <DeleteOutlined
                        onClick={() => handleDelete(file.id)}
                        style={{ cursor: 'pointer', flex: '0 0 20px' }}
                      />
                    </div>
                  ))}
                </div>
              )}
            </Form.Item>

            <Form.Item
              label={
                <>
                  <span style={{ color: '#000', fontWeight: 'bold' }}>提取要点</span>
                  <span style={{ color: '#999', marginLeft: 10 }}>
                    注：可在下方输入合同解析要点，如不修改，则按下面默认规则提取
                  </span>
                </>
              }
            >
              <TextArea
                showCount
                maxLength={20000}
                value={keyPoints}
                placeholder='请输入提取要点'
                style={{ height: 120, resize: 'none' }}
                onChange={onChange}
              />
            </Form.Item>
          </Form>
          <Button
            size='large'
            type='primary'
            onClick={() => {
              if (uploadedFiles.length === 0) {
                messageApi.error('请先上传文件')
              } else if (!keyPoints) {
                messageApi.error('请输入提取要点')
              } else {
                onSubmit(uploadedFiles, keyPoints)
              }
            }}
          >
            合 同 解 析
          </Button>
        </Flex>
      </Card>
    </div>
  )
}

export default Step1
