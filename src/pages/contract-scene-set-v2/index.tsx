/** 模板生成工具 */
import { Flex, message, Modal, Spin, Typography } from 'antd'
import { useRef, useState } from 'react'
import { contractSceneSet } from '@/api/contractSceneSet'
import { FullPageScroll, FullPageScrollHandle } from './FullPageScroll'
import Step1, { FileItem } from './Step1'
import Step2 from './Step2'
import './index.less'

const appKey = import.meta.env['VITE_CONTRACT_SCENE_SET_V2_TOKEN'] || ''

export const ContractSceneSet = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [uploadedFiles, setUploadedFiles] = useState<FileItem[]>([])
  const [currentSection, setCurrentSection] = useState<number>(0)
  const [step2Key, setStep2Key] = useState(1)
  const childRef = useRef<FullPageScrollHandle>(null)
  const [globalLoading, setGlobalLoading] = useState<boolean>(false)
  // 生成状态
  const [generating, setGenerating] = useState<boolean>(false)
  // 生成消息
  const [messages, setMessages] = useState<string>('')
  const [info, setInfo] = useState<string>('')

  const handleGeneration = async (
    keyPoints: string,
    files: FileItem[],
    info: string
  ) => {
    setGenerating(true)
    setGlobalLoading(true)
    // setMessages(
    //   '<think>是到付哈封建士大夫撒旦啊是大家的沙发上的客服都是发货速度放缓士大夫</think>'
    // )
    setMessages('')
    let str = ''
    try {
      await contractSceneSet(
        {
          keyPoints,
          appKey,
          info,
          type: '提取',
          compType: '销售商务',
          files: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: files[0].id,
            },
          ],
        },
        {
          onMessage: (text) => {
            if (text) {
              setMessages((prev) => prev + text)
              str += text
            }
          },
          onError: () => {
            setGenerating(false)
            setGlobalLoading(false)
          },
          onFinish: () => {
            setGlobalLoading(false)
            setGenerating(false)
          },
        }
      )
    } catch (err) {
      setGenerating(false)
      setGlobalLoading(false)
    }
  }

  const getFileInfo = async (files: FileItem[], keyPoints: string) => {
    setGlobalLoading(true)
    let str = ''
    try {
      await contractSceneSet(
        {
          appKey,
          type: '合同内容',
          compType: '销售商务',
          files: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: files[0].id,
            },
          ],
        },
        {
          onMessage: (text) => {
            if (text) {
              str += text
            }
          },
          onError: () => {
            setGlobalLoading(false)
          },
          onFinish: () => {
            setInfo(str)
            handleGeneration(keyPoints, files, str)
          },
        }
      )
    } catch (err) {
      setGenerating(false)
      setGlobalLoading(false)
    }
  }

  const Step1OnSubmit = (files: FileItem[], keyPoints: string) => {
    setUploadedFiles(files)
    childRef.current?.handleNavClick(1)
    getFileInfo(files, keyPoints)
    setStep2Key((prev) => prev + 1)
  }

  const back = () => {
    Modal.confirm({
      title: '提示',
      content: '确定要返回吗？数据不会被保存',
      okText: '返回',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        setStep2Key((prev) => prev + 1)
        childRef.current?.handleNavClick(0)
      },
      onCancel() {
        console.log('取消')
      },
    })
  }

  interface SectionItem {
    name: string
    children: React.ReactNode
  }

  const sections: SectionItem[] = [
    {
      name: '1',
      children: (
        <Step1
          onSubmit={Step1OnSubmit}
          messageApi={messageApi}
          setGlobalLoading={setGlobalLoading}
        />
      ),
    },
    {
      name: '2',
      children: (
        <Step2
          key={step2Key}
          info={info}
          messages={messages}
          messagesEnd={!generating}
          back={back}
          fileList={uploadedFiles}
          currentSection={currentSection}
          setGlobalLoading={setGlobalLoading}
        />
      ),
    },
  ]

  return (
    <>
      {contextHolder}
      <Spin tip="加载中" spinning={globalLoading} fullscreen size="large" />
      <Flex
        vertical
        className="contract-scene-set-v2-toolbar"
        justify="center"
        align="center"
      >
        <Typography.Title className="title-text">
          智能合同场景集
        </Typography.Title>
        <Typography.Text style={{ fontSize: 14, color: '#8F91A6' }}>
          合同解析，提取关键信息，法审，对比，批注
        </Typography.Text>
      </Flex>
      <FullPageScroll
        sections={sections}
        ref={childRef}
        setCurrentSection={setCurrentSection}
      />
    </>
  )
}

export default ContractSceneSet
