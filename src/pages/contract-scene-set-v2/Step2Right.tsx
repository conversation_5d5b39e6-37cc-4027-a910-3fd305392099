import {
  But<PERSON>,
  Card,
  Checkbox,
  Divider,
  Flex,
  Form,
  List,
  message,
  Modal,
  Radio,
  Select,
  Space,
  Steps,
  Tabs,
  Tag,
  Typography,
} from 'antd'
import { useEffect, useMemo, useRef, useState } from 'react'
import {
  CheckCircleFilled,
  DownloadOutlined,
  DownOutlined,
  ExclamationCircleFilled,
  InfoCircleFilled,
  PlusOutlined,
  RedoOutlined,
  UpOutlined,
  WarningFilled,
} from '@ant-design/icons'
import StreamTypewriter from '@/component/StreamTypewriter'
import { FileItem } from './Step1'
import {
  getKnowledgeData,
  contractSceneSet,
  uploadKnowledgeFile,
} from '@/api/contractSceneSet'
import { useLocation } from 'react-router-dom'
import Knowledge from '../contract-scene-set/knowledge'
import { extractContent, extractFirstCompleteJSON } from '@/utils/common'
import ThinkBlock from '@/component/think'

const { Title, Text } = Typography
const appKey = import.meta.env['VITE_CONTRACT_SCENE_SET_V2_TOKEN'] || ''

interface props {
  messages: string
  info: string
  messagesEnd: boolean
  fileList: FileItem[]
  setGlobalLoading: (type: boolean) => void
}

interface rule {
  id: number
  title: string
  riskLevel: string
}

let options = [
  { value: '买卖合同', label: '买卖合同' },
  { value: '赠与合同', label: '赠与合同' },
  { value: '借款合同', label: '借款合同' },
  { value: '保证合同', label: '保证合同' },
  { value: '租赁合同', label: '租赁合同' },
  { value: '融资租赁合同', label: '融资租赁合同' },
  { value: '保理合同', label: '保理合同' },
  { value: '承揽合同', label: '承揽合同' },
  { value: '建设工程合同', label: '建设工程合同' },
  { value: '客运合同', label: '客运合同' },
  { value: '货运合同', label: '货运合同' },
  { value: '多式联运合同', label: '多式联运合同' },
  { value: '技术合同一般规定', label: '技术合同一般规定' },
  { value: '技术开发合同', label: '技术开发合同' },
  { value: '技术咨询合同', label: '技术咨询合同' },
  { value: '技术服务合同', label: '技术服务合同' },
  { value: '保管合同', label: '保管合同' },
  { value: '仓储合同', label: '仓储合同' },
  { value: '物业服务合同', label: '物业服务合同' },
  { value: '行纪合同', label: '行纪合同' },
  { value: '劳动合同', label: '劳动合同' },
  { value: '中介合同', label: '中介合同' },
  { value: '信托合同', label: '信托合同' },
  { value: '合伙合同', label: '合伙合同' },
  { value: '其他合同', label: '其他合同' },
]

// 风险等级标签颜色
const riskLevelColors: Record<string, string> = {
  高风险: 'red',
  中风险: 'orange',
  低风险: 'blue',
}

export const Step2Right: React.FC<props> = ({
  info,
  messages,
  messagesEnd,
  fileList,
  setGlobalLoading,
}) => {
  const { search } = useLocation()
  const searchParams = useMemo(() => new URLSearchParams(search), [search])
  const Tenantid = searchParams.get('tenantid') || ''
  const Token = searchParams.get('token') || ''
  const [knowledgeDataData, setKnowledgeDataData] = useState<any[]>([])
  const [messageStr, setMessageStr] = useState<string>('')
  const [end, setEnd] = useState<boolean>(messagesEnd)
  const [messageStr2, setMessageStr2] = useState<string>('')
  const [end2, setEnd2] = useState<boolean>(true)
  const [ruleDbType, setRuleDbType] = useState('')
  const [currntAffairs, setCurrntAffairs] = useState('')
  const [stepsCurrent, setStepsCurrent] = useState(1)
  const [libSelectValue, setLibSelectValue] = useState<string[]>([])
  const [reviewResults, setReviewResults] = useState<any[]>([])
  const [selectedRules, setSelectedRules] = useState<number[]>([])
  const [rules, setRules] = useState<rule[]>([])
  const [params, setParams] = useState<any>({})
  const [message2Key, setMessage2Key] = useState(0)
  const [adjustmentType, setAdjustmentType] = useState<string>('')
  const [knowledModel, setknowledModel] = useState<boolean>(false)
  const [cardShowData, setCardShowData] = useState<any[]>([])
  const [cardData, setCardData] = useState<any[]>([])
  const [showType, setShowType] = useState('全部')
  const [workflowType, setWorkflowType] = useState('')
  const scrollRef = useRef<HTMLDivElement>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  useEffect(() => {
    setStepsCurrent(0)
    if (messages.includes('</h1>')) {
      setMessageStr(messages.replace(/<h1>.*?<\/h1>/g, ''))
    }
  }, [messages])

  useEffect(() => {
    setEnd(messagesEnd)
  }, [messagesEnd])

  // 节流函数
  const throttle = (func: Function, delay: number) => {
    let lastTime = 0
    return (...args: any[]) => {
      const now = Date.now()
      if (now - lastTime >= delay) {
        func(...args)
        lastTime = now
      }
    }
  }

  const scroll = throttle(() => {
    scrollRef.current?.scrollTo({
      top: scrollRef.current.scrollHeight + 1000,
      behavior: 'smooth',
    })
  }, 100)

  const showModal = (type: string) => {
    let defaultValue = ''
    if (type === 'ContractLawReview') {
      const H1Str = extractContent(messages, 'h1')
      let result = H1Str.split('：')
      if (result && result[1]) {
        let value = result[1].replace(/[^\w\u4e00-\u9fa5]/g, '')
        if (options.find((item) => item.value === value)) defaultValue = value
      }
    }

    setWorkflowType(type)
    getKnowledgeData(
      {
        pageNum: 1,
        pageSize: 999999,
        entity: {
          libName: '',
        },
      },
      Tenantid,
      Token
    ).then((res) => {
      if (res.code === 200) {
        setAdjustmentType('1')
        setIsModalOpen(true)
        setLibSelectValue(params?.libIds?.split(',') || [])
        form.setFieldsValue({
          contractType: defaultValue,
        })
        setKnowledgeDataData(res.data.records)
      }
    })
  }

  const handleOk = () => {
    form.validateFields().then(() => {
      const values = form.getFieldsValue()
      if (values.ruleDbType === '内部规则' && libSelectValue.length === 0) {
        message.error('请选择知识库')
        return
      }
      setIsModalOpen(false)
      setParams({
        appKey,
        info,
        type: workflowType === 'ContractLawReview' ? '法审' : '敏感词',
        compType: '销售商务',
        libIds: libSelectValue.join(','),
        ruleType: '规则匹配',
        token: Token,
        tenantid: Tenantid,
        ...values,
      })
      setCurrntAffairs('legalReview')
    })
    if (adjustmentType === '2') {
      matchReviewChecklist()
    }
  }

  const handleCancel = () => {
    form.resetFields()
    setIsModalOpen(false)
  }

  const handleSelectChange = (value: string[]) => {
    setLibSelectValue(value)
  }

  const [form] = Form.useForm()
  const onChange = (value: number) => {
    setStepsCurrent(value)
  }

  const matchReviewChecklist = () => {
    setStepsCurrent(1)
    setGlobalLoading(true)
    setSelectedRules([])
    setRules([])
    let str = ''
    const obj = {
      appKey,
      info,
      type: '敏感词',
      compType: '销售商务',
      libIds: libSelectValue.join(','),
      ruleType: '规则匹配',
      token: Token,
      tenantid: Tenantid,
    }
    try {
      contractSceneSet(workflowType === 'ContractLawReview' ? params : obj, {
        onMessage: (text) => {
          if (text) {
            str += text
          }
        },
        onError: () => {
          setGlobalLoading(false)
        },
        onFinish: () => {
          let rules: rule[] = []
          try {
            if (params.ruleDbType === '民法典') {
              let result = JSON.parse(str).result
              ;(typeof result === 'string'
                ? JSON.parse(result)
                : result
              ).forEach((item: any, index: number) => {
                rules.push({
                  id: index + 1,
                  title: item.rule_desc,
                  riskLevel: item.rule_level,
                })
              })
            } else {
              JSON.parse(str).result.forEach((item: any, index: number) => {
                rules.push({
                  id: index + 1,
                  title: item.rule_desc,
                  riskLevel: item.rule_level,
                })
              })
            }
          } catch (error) {
            console.log('json解析失败')
          }
          setRules(rules)
          setGlobalLoading(false)
        },
      })
    } catch (err) {
      setGlobalLoading(false)
    }
  }

  const ruleVerification = () => {
    setReviewResults([])
    setMessageStr2('')
    setStepsCurrent(2)
    setMessage2Key((prev) => prev + 1)
    setShowType('全部')
    setGlobalLoading(true)
    setEnd2(false)
    let buffer = ''
    let jsonObjects: any[] = []
    try {
      contractSceneSet(
        {
          type: workflowType === 'ContractLawReview' ? '法审' : '敏感词',
          compType: '销售商务',
          stance: params.stance,
          rules: JSON.stringify(
            rules
              .filter((rule) => selectedRules.includes(rule.id))
              .map((x) => ({ rule_desc: x.title, rule_level: x.riskLevel }))
          ),
          appKey,
          info,
          ruleDbType: params.ruleDbType,
          ruleType: '规则校验',
          token: Token,
          tenantid: Tenantid,
          files: fileList.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x.id,
          })),
        },
        {
          onMessage: (text) => {
            if (text) {
              buffer += text

              // 实时尝试提取完整JSON对象
              while (true) {
                // 匹配最接近的完整JSON对象
                const jsonMatch = extractFirstCompleteJSON(buffer)
                if (!jsonMatch) break

                const [fullMatch, jsonStr] = jsonMatch
                buffer = buffer.slice(fullMatch.length) // 从缓冲区移除已处理部分

                try {
                  let jsonObject = JSON.parse(jsonStr.replace(/\s+/g, ''))
                  Object.keys(jsonObject).forEach((key) => {
                    jsonObject[key] = jsonObject[key].replace(/"/g, "'")
                  })
                  jsonObjects.push(jsonObject)
                  setMessageStr2(
                    (prev) =>
                      prev + `<listing>${JSON.stringify(jsonObject)}</listing>`
                  )
                  setReviewResults(jsonObjects)
                } catch (err) {
                  console.error('JSON解析失败:', jsonStr, err)
                }
              }
            }
          },
          onError: () => {
            setGlobalLoading(false)
            setEnd2(true)
          },
          onFinish: () => {
            setGlobalLoading(false)
            setEnd2(true)
          },
        }
      )
    } catch (err) {
      setGlobalLoading(false)
      setEnd2(true)
    }
  }

  const adjustRules = () => {
    setAdjustmentType('2')
    setIsModalOpen(true)
    setLibSelectValue(params.libIds.split(','))
    form.setFieldsValue({
      contractType: params.contractType,
      ruleDbType: params.ruleDbType,
      stance: params.stance,
    })
  }

  const exportAnnotationVersion = () => {
    let str = ''
    try {
      contractSceneSet(
        {
          type: '批注',
          compType: '销售商务',
          query: messageStr2,
          appKey,
          info,
          files: fileList.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x.id,
          })),
        },
        {
          onMessage: (text) => {
            if (text) {
              str += text
            }
          },
          onError: () => {},
          onFinish: () => {
            // 提取()中的内容
            const parenthesesContent = str.match(/\((.*?)\)/)
            const parenthesesResult = parenthesesContent
              ? parenthesesContent[1]
              : null

            // 提取[]中的内容
            const squareBracketsContent = str.match(/\[(.*?)\]/)
            const squareBracketsResult = squareBracketsContent
              ? squareBracketsContent[1]
              : null

            if (parenthesesResult && squareBracketsResult) {
              const link = document.createElement('a')
              link.href = parenthesesResult
              link.download = `批注文件${squareBracketsResult}`
              document.body.appendChild(link)
              link.click()
              link.remove()
            }
          },
        }
      )
    } catch (err) {}
  }

  const getAllListData = () => {
    getKnowledgeData(
      {
        pageNum: 1,
        pageSize: 999999,
        entity: {
          libName: '',
        },
      },
      Tenantid,
      Token
    ).then((res) => {
      if (res.code === 200) {
        const data = res.data.records
        for (let i = 0; i < data.length; i++) {
          data[i].checked = false
        }
        setCardData(data)
        setCardShowData(data)
        setknowledModel(true)
      }
    })
  }
  const keywordSearch = (value: string) => {
    setCardShowData(cardData.filter((item) => item.libName.includes(value)))
  }

  const closeKnowledModelFalg = () => {
    setknowledModel(false)
  }
  const closeKnowledModel = (modalFalg: boolean, selectId: string) => {
    const selectIdArr: any[] = selectId.split(',')
    setknowledModel(modalFalg)

    const selectIdFilterArr: any[] = cardData
      .filter((itemArr: any) => selectIdArr.includes(itemArr.id)) // 过滤出符合条件的元素
      .map((itemArr) => ({
        id: itemArr.id,
        flag: 'knowledge',
        libName: itemArr.libName,
        libDesc: itemArr.libDesc,
      }))
    const [obj, ..._arr] = selectIdFilterArr
    handleExport(obj)
  }

  const handleExport = async (obj: any) => {
    try {
      // 1. 创建 Blob 对象
      const textBlob = new Blob([messages], { type: 'text/plain' })

      const fileName = fileList[0].name
      const lastDotIndex = fileName.lastIndexOf('.')
      const nameWithoutExtension2 = fileName.substring(0, lastDotIndex)
      // 2. 创建 File 对象
      const file = new File(
        [textBlob],
        `${nameWithoutExtension2}${new Date().getTime()}.txt`,
        { type: 'text/plain' }
      )

      const formData = new FormData()
      formData.append('file', file)
      formData.append('baseId', obj.id)

      uploadKnowledgeFile(formData, Tenantid, Token).then((res) => {
        if (res.code === 200) {
          message.success('存入成功')
        }
      })
    } catch (error) {
      console.error('导出失败:', error)
    }
  }

  const handleKnowledgeCheckboxChange = (id: string, newChecked: boolean) => {
    const updatedItems = cardShowData.map((item) =>
      item.id === id ? { ...item, checked: newChecked } : item
    )
    setCardShowData(updatedItems)
  }

  const items = [
    {
      key: '全部',
      label: <span> 全部（{reviewResults.length}） </span>,
      children: null,
      // 可以在这里添加具体的高风险项目列表
      style: { backgroundColor: '#fff0f0' }, // 浅粉色背景
    },
    {
      key: '高风险',
      label: (
        <span>
          <ExclamationCircleFilled
            style={{ color: '#ff4d4f', marginRight: 8 }}
          />
          高风险（
          {reviewResults.filter((x) => x['风险级别'] === '高风险').length}）
        </span>
      ),
      children: null,
      // 可以在这里添加具体的高风险项目列表
      style: { backgroundColor: '#fff0f0' }, // 浅粉色背景
    },
    {
      key: '中风险',
      label: (
        <span>
          <WarningFilled style={{ color: '#faad14', marginRight: 8 }} />
          中风险（
          {reviewResults.filter((x) => x['风险级别'] === '中风险').length}）
        </span>
      ),
      children: null,
      style: { backgroundColor: '#f5f5f5' }, // 浅灰色背景
    },
    {
      key: '低风险',
      label: (
        <span>
          <InfoCircleFilled style={{ color: '#fadb14', marginRight: 8 }} />
          低风险（
          {reviewResults.filter((x) => x['风险级别'] === '低风险').length}）
        </span>
      ),
      children: null,
      style: { backgroundColor: '#f5f5f5' }, // 浅灰色背景
    },
    {
      key: '已通过',
      label: (
        <span>
          <CheckCircleFilled style={{ color: '#52c41a', marginRight: 8 }} />
          已通过（
          {reviewResults.filter((x) => x['风险级别'] === '已通过').length}）
        </span>
      ),
      children: null,
      style: { backgroundColor: '#f5f5f5' }, // 浅灰色背景
    },
  ]

  return (
    <>
      <div>
        {currntAffairs === 'legalReview' && (
          <Steps
            style={{
              position: 'sticky',
              left: 20,
              top: 0,
              zIndex: 100,
              background: '#fff',
              padding: '0 20px',
              marginBottom: 20,
            }}
            onChange={onChange}
            current={stepsCurrent}
            items={[
              {
                title: '合同概览',
              },
              {
                title: '审查清单',
              },
              {
                title: '审查结果',
              },
            ]}
          />
        )}
        <div
          style={{
            overflowY: 'auto',
            position: 'relative',
            visibility: stepsCurrent === 0 ? 'visible' : 'hidden',
            height:
              stepsCurrent === 0
                ? currntAffairs === 'legalReview'
                  ? 'calc(100vh - 240px)'
                  : 'calc(100vh - 180px)'
                : 0,
            opacity: stepsCurrent === 0 ? 1 : 0,
            overflow: stepsCurrent === 0 ? 'auto' : 'hididen',
          }}
          className="scroll-container"
        >
          <StreamTypewriter
            text={messageStr}
            end={end}
            components={{
              think: (props: any) => {
                return (
                  <ThinkBlock finished={false}>{props.children}</ThinkBlock>
                )
              },
            }}
          />
        </div>
        <div
          style={{
            visibility: stepsCurrent === 1 ? 'visible' : 'hidden',
            height: stepsCurrent === 1 ? 'calc(100vh - 240px)' : 0,
            opacity: stepsCurrent === 1 ? 1 : 0,
            overflow: stepsCurrent === 1 ? 'auto' : 'hididen',
          }}
          className="scroll-container"
        >
          <Card
            style={{ marginTop: '20px' }}
            title={
              params.ruleDbType === '民法典'
                ? '民法典'
                : `内部规则-${knowledgeDataData
                    .filter((x) => libSelectValue.includes(x.id))
                    .map((x) => x.libName)
                    .join(', ')}`
            }
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={adjustRules}
              >
                知识库新增规则
              </Button>
            }
          >
            <div style={{ marginBottom: '16px' }}>
              <Space>
                {/* 全部规则复选框 */}
                <Checkbox
                  indeterminate={
                    selectedRules.length > 0 &&
                    selectedRules.length < rules.length
                  }
                  checked={
                    selectedRules.length === rules.length && rules.length > 0
                  }
                  onChange={(e) => {
                    if (e.target.checked) {
                      // 全选所有规则
                      setSelectedRules(rules.map((rule) => rule.id))
                    } else {
                      // 取消全选
                      setSelectedRules([])
                    }
                  }}
                >
                  全部规则 ({rules.length}条)
                </Checkbox>

                {/* 高风险复选框 */}
                <Checkbox
                  indeterminate={
                    selectedRules.filter((id) =>
                      rules.find(
                        (rule) => rule.id === id && rule.riskLevel === '高风险'
                      )
                    ).length > 0 &&
                    selectedRules.filter((id) =>
                      rules.find(
                        (rule) => rule.id === id && rule.riskLevel === '高风险'
                      )
                    ).length <
                      rules.filter((rule) => rule.riskLevel === '高风险').length
                  }
                  checked={
                    selectedRules.filter((id) =>
                      rules.find(
                        (rule) => rule.id === id && rule.riskLevel === '高风险'
                      )
                    ).length > 0 &&
                    selectedRules.filter((id) =>
                      rules.find(
                        (rule) => rule.id === id && rule.riskLevel === '高风险'
                      )
                    ).length ===
                      rules.filter((rule) => rule.riskLevel === '高风险').length
                  }
                  onChange={(e) => {
                    const highRiskRules = rules
                      .filter((rule) => rule.riskLevel === '高风险')
                      .map((rule) => rule.id)
                    if (e.target.checked) {
                      // 全选高风险
                      setSelectedRules([
                        ...new Set([...selectedRules, ...highRiskRules]),
                      ])
                    } else {
                      // 取消全选高风险
                      setSelectedRules(
                        selectedRules.filter(
                          (id) => !highRiskRules.includes(id)
                        )
                      )
                    }
                  }}
                >
                  高风险
                </Checkbox>

                {/* 中风险复选框 */}
                <Checkbox
                  indeterminate={
                    selectedRules.filter((id) =>
                      rules.find(
                        (rule) => rule.id === id && rule.riskLevel === '中风险'
                      )
                    ).length > 0 &&
                    selectedRules.filter((id) =>
                      rules.find(
                        (rule) => rule.id === id && rule.riskLevel === '中风险'
                      )
                    ).length <
                      rules.filter((rule) => rule.riskLevel === '中风险').length
                  }
                  checked={
                    selectedRules.filter((id) =>
                      rules.find(
                        (rule) => rule.id === id && rule.riskLevel === '中风险'
                      )
                    ).length > 0 &&
                    selectedRules.filter((id) =>
                      rules.find(
                        (rule) => rule.id === id && rule.riskLevel === '中风险'
                      )
                    ).length ===
                      rules.filter((rule) => rule.riskLevel === '中风险').length
                  }
                  onChange={(e) => {
                    const midRiskRules = rules
                      .filter((rule) => rule.riskLevel === '中风险')
                      .map((rule) => rule.id)
                    if (e.target.checked) {
                      // 全选中风险
                      setSelectedRules([
                        ...new Set([...selectedRules, ...midRiskRules]),
                      ])
                    } else {
                      // 取消全选中风险
                      setSelectedRules(
                        selectedRules.filter((id) => !midRiskRules.includes(id))
                      )
                    }
                  }}
                >
                  中风险
                </Checkbox>

                {/* 低风险复选框 */}
                <Checkbox
                  indeterminate={
                    selectedRules.filter((id) =>
                      rules.find(
                        (rule) => rule.id === id && rule.riskLevel === '低风险'
                      )
                    ).length > 0 &&
                    selectedRules.filter((id) =>
                      rules.find(
                        (rule) => rule.id === id && rule.riskLevel === '低风险'
                      )
                    ).length <
                      rules.filter((rule) => rule.riskLevel === '低风险').length
                  }
                  checked={
                    selectedRules.filter((id) =>
                      rules.find(
                        (rule) => rule.id === id && rule.riskLevel === '低风险'
                      )
                    ).length > 0 &&
                    selectedRules.filter((id) =>
                      rules.find(
                        (rule) => rule.id === id && rule.riskLevel === '低风险'
                      )
                    ).length ===
                      rules.filter((rule) => rule.riskLevel === '低风险').length
                  }
                  onChange={(e) => {
                    const lowRiskRules = rules
                      .filter((rule) => rule.riskLevel === '低风险')
                      .map((rule) => rule.id)
                    if (e.target.checked) {
                      // 全选低风险
                      setSelectedRules([
                        ...new Set([...selectedRules, ...lowRiskRules]),
                      ])
                    } else {
                      // 取消全选低风险
                      setSelectedRules(
                        selectedRules.filter((id) => !lowRiskRules.includes(id))
                      )
                    }
                  }}
                >
                  低风险
                </Checkbox>
              </Space>
            </div>

            {/* 规则列表 */}
            <List
              itemLayout="horizontal"
              dataSource={rules}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <Checkbox
                          checked={selectedRules.includes(item.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedRules([...selectedRules, item.id])
                            } else {
                              setSelectedRules(
                                selectedRules.filter((id) => id !== item.id)
                              )
                            }
                          }}
                          style={{ marginRight: '8px' }}
                        />
                        <Tag color={riskLevelColors[item.riskLevel]}>
                          {item.riskLevel}
                        </Tag>
                      </div>
                    }
                    title={`${item.id}. ${item.title}`}
                  />
                </List.Item>
              )}
            />
          </Card>
        </div>
        <div
          style={{
            visibility: stepsCurrent === 2 ? 'visible' : 'hidden',
            height: stepsCurrent === 2 ? 'calc(100vh - 240px)' : 0,
            opacity: stepsCurrent === 2 ? 1 : 0,
            overflow: stepsCurrent === 2 ? 'auto' : 'hididen',
          }}
          ref={scrollRef}
          className="scroll-container"
        >
          <Tabs
            defaultActiveKey="全部"
            activeKey={showType}
            onChange={(activeKey: string) => {
              setShowType(activeKey)
            }}
            items={items}
            tabBarStyle={{ marginBottom: 0 }}
            renderTabBar={(props, DefaultTabBar) => (
              <DefaultTabBar
                {...props}
                style={{ backgroundColor: 'transparent' }}
              />
            )}
          />
          <StreamTypewriter
            key={message2Key}
            text={messageStr2}
            end={end2}
            onchange={() => {
              scroll()
            }}
            charsPerUpdate={5}
            components={{
              listing({ children, className, ...props }: any) {
                let isJSON = false
                let obj = null

                try {
                  obj = JSON.parse(children)
                  isJSON = true
                } catch (error) {
                  isJSON = false
                }

                const [expanded, setExpanded] = useState(true)

                return isJSON ? (
                  showType === obj['风险级别'] || showType === '全部' ? (
                    <Card
                      style={{ marginTop: '16px' }}
                      title={
                        <Flex justify="space-between" align="center">
                          <div
                            style={{
                              maxWidth: 'calc(100% - 50px)',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                            }}
                          >
                            <Tag color={riskLevelColors[obj['风险级别']]}>
                              {obj['风险级别']}
                            </Tag>
                            <Text strong>
                              {workflowType === 'ContractLawReview'
                                ? obj['规则条款']
                                : obj['敏感词']}
                            </Text>
                          </div>
                          {expanded ? (
                            <UpOutlined
                              onClick={() => {
                                setExpanded(false)
                              }}
                            />
                          ) : (
                            <DownOutlined
                              onClick={() => {
                                setExpanded(true)
                              }}
                            />
                          )}
                        </Flex>
                      }
                    >
                      {workflowType === 'ContractLawReview' && (
                        <div
                          style={{
                            padding: '10px',
                            marginBottom: '10px',
                            background: '#EFEFEF',
                            wordWrap: 'break-word',
                            wordBreak: 'break-all',
                            overflowWrap: 'break-word',
                            whiteSpace: 'pre-wrap',
                          }}
                        >
                          <Text>合同原文：{obj['合同原文']}</Text>
                        </div>
                      )}

                      {expanded && (
                        <>
                          {workflowType === 'ContractLawReview' ? (
                            <>
                              <div>
                                <Title level={5}>风险提示：</Title>
                                <Text>{obj['风险提示']}</Text>
                              </div>
                              <Divider style={{ margin: '10px 0' }} />
                              <div>
                                <Title level={5}>修改意见：</Title>
                                <Text>{obj['修改意见']}</Text>
                              </div>
                            </>
                          ) : (
                            <div>
                              <Title level={5}>位置：</Title>
                              <Text>{obj['位置']}</Text>
                            </div>
                          )}
                        </>
                      )}
                    </Card>
                  ) : (
                    ''
                  )
                ) : (
                  <code
                    {...props}
                    className={className}
                    style={{
                      wordWrap: 'break-word',
                      wordBreak: 'break-all',
                      overflowWrap: 'break-word',
                      whiteSpace: 'pre-wrap',
                    }}
                  >
                    {children}
                  </code>
                )
              },
            }}
          />
        </div>
      </div>
      <Flex
        align="center"
        justify="center"
        gap={16}
        style={{ background: '#fff', height: '60px' }}
      >
        {currntAffairs === 'legalReview' ? (
          <>
            {stepsCurrent === 0 ? (
              <Button
                type="primary"
                onClick={() => {
                  matchReviewChecklist()
                }}
              >
                匹配审查清单
              </Button>
            ) : stepsCurrent === 1 ? (
              <Button
                type="primary"
                onClick={() => {
                  ruleVerification()
                }}
              >
                规则校验
              </Button>
            ) : stepsCurrent === 2 ? (
              <>
                <Button
                  type="primary"
                  onClick={exportAnnotationVersion}
                  icon={<DownloadOutlined />}
                >
                  导出批注版本
                </Button>
                <Button
                  type="primary"
                  onClick={ruleVerification}
                  icon={<RedoOutlined />}
                >
                  重新审查
                </Button>
              </>
            ) : null}
          </>
        ) : (
          <>
            <Button
              type="primary"
              onClick={() => {
                showModal('ContractLawReview')
              }}
            >
              合同法审
            </Button>
            <Button
              type="primary"
              onClick={() => {
                showModal('SensitiveWordVerification')
              }}
            >
              敏感词校验
            </Button>
            <Button type="primary" onClick={getAllListData}>
              存入知识库
            </Button>
          </>
        )}
      </Flex>
      <Modal
        title="审查方式"
        maskClosable={false}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form
          layout="vertical"
          form={form}
          initialValues={{ layout: 'vertical' }}
          style={{ maxWidth: 600 }}
        >
          {workflowType === 'ContractLawReview' ? (
            <>
              <Form.Item
                label="合同类型"
                name="contractType"
                rules={[{ required: true, message: '请选择合同类型' }]}
              >
                <Select
                  placeholder="请选择合同类型"
                  disabled={adjustmentType === '2'}
                  style={{ width: '100%' }}
                  options={options}
                />
              </Form.Item>
              <Form.Item
                label="审查立场"
                name="stance"
                rules={[{ required: true, message: '请选择审查立场' }]}
              >
                <Radio.Group
                  disabled={adjustmentType === '2'}
                  options={[
                    { value: '甲方', label: '甲方立场' },
                    { value: '乙方', label: '乙方立场' },
                  ]}
                />
              </Form.Item>
              <Form.Item
                label="规则库类型"
                name="ruleDbType"
                rules={[{ required: true, message: '请选择规则库类型' }]}
              >
                <Radio.Group
                  options={[
                    { value: '内部规则', label: '内部规则' },
                    { value: '民法典', label: '民法典' },
                  ]}
                  onChange={(e) => {
                    setRuleDbType(e.target.value)
                    setLibSelectValue([])
                  }}
                />
              </Form.Item>
              {ruleDbType === '内部规则' && (
                <div>
                  <Select
                    mode="multiple"
                    style={{ width: '100%', marginTop: -20 }}
                    placeholder="请输入关键词筛选内部规则"
                    value={libSelectValue}
                    onChange={handleSelectChange}
                    showSearch
                    filterOption={(input, option) =>
                      option?.label.toLowerCase().includes(input.toLowerCase())
                    }
                    options={knowledgeDataData.map((item) => ({
                      value: item.id,
                      label: item.libName,
                    }))}
                  />
                </div>
              )}
            </>
          ) : (
            <Form.Item label="敏感词规则库">
              <Select
                mode="multiple"
                style={{ width: '100%', marginTop: -20 }}
                placeholder="请选择内部规则"
                value={libSelectValue}
                onChange={handleSelectChange}
                options={knowledgeDataData.map((item) => ({
                  value: item.id,
                  label: item.libName,
                }))}
              />
            </Form.Item>
          )}
        </Form>
      </Modal>

      {knowledModel && (
        <Knowledge
          cardData={cardShowData}
          knowledModel={knowledModel}
          keywordSearch={keywordSearch}
          closeKnowledModel={closeKnowledModel}
          closeKnowledModelFalg={closeKnowledModelFalg}
          onCheckboxChange={handleKnowledgeCheckboxChange}
        ></Knowledge>
      )}
    </>
  )
}

export default Step2Right
