.gujing-english-training {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f9fafb;

  // 导航栏样式
  .nav-bar {
    background-color: #3949AB;
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

    .container {
      max-width: 1280px;
      margin: 0 auto;
      padding: 0.75rem 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .logo {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .icon {
          height: 2rem;
          width: 2rem;
        }

        span {
          font-size: 1.25rem;
          font-weight: bold;
        }
      }

      .nav-links {
        display: flex;
        align-items: center;
        gap: 1rem;

        .desktop-links {
          display: none;
          gap: 1rem;

          @media (min-width: 768px) {
            display: flex;
          }

          a {
            color: white;
            text-decoration: none;
            &:hover {
              color: #bfdbfe;
            }
          }
        }

        .user-info {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          background-color: #3949AB;
          border-radius: 9999px;
          padding: 0.25rem 0.75rem;

          .avatar {
            height: 1.5rem;
            width: 1.5rem;
            background-color: white;
            color: #1e40af;
            border-radius: 9999px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
          }

          span {
            font-size: 0.875rem;
          }
        }
      }
    }
  }

  // 主内容区样式
  .main-content {
    flex-grow: 1;
    padding: 1.5rem 1rem;

    .container {
      max-width: 1280px;
      margin: 0 auto;

      .content-wrapper {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;

        @media (min-width: 768px) {
          flex-direction: row;
        }

        // 侧边栏
        .sidebar {
          width: 100%;

          @media (min-width: 768px) {
            width: 25%;
          }

          .course-menu {
            background-color: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            position: sticky;
            top: 1rem;

            .menu-header {
              font-size: 1.125rem;
              font-weight: bold;
              color: #1f2937;
              margin-bottom: 1rem;
              display: flex;
              align-items: center;
              gap: 0.5rem;

              .icon {
                height: 1.25rem;
                width: 1.25rem;
                color: #059669;
              }
            }

            .terms-list {
              .term-item {
                font-size: 0.9rem;
                padding: 0.5rem;
                padding-left: 1.75rem;
                position: relative;
                cursor: pointer;
                border-radius: 0.5rem;
                transition: all 0.2s ease;

                &:hover {
                  background-color: rgba(59, 130, 246, 0.05);
                }

                &.active {
                  background-color: rgba(59, 130, 246, 0.1);
                  color: #2563eb;
                  font-weight: 500;
                }

                .term-indicator {
                  position: absolute;
                  left: 0.5rem;
                  top: 50%;
                  transform: translateY(-50%);

                  .indicator {
                    height: 0.75rem;
                    width: 0.75rem;
                    color: #2563eb;
                  }
                }
              }
            }
          }
        }

        // 主要内容区
        .main-section {
          width: 100%;

          @media (min-width: 768px) {
            width: 75%;
          }

          .section-header {
            margin-bottom: 1.5rem;

            h1 {
              font-size: 1.875rem;
              font-weight: bold;
              color: #1f2937;
              margin-bottom: 0.5rem;
            }

            p {
              color: #6b7280;
            }
          }

          // 术语卡片
          .term-card {
            background-color: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;

            .term-header {
              .term-title {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                margin-bottom: 1rem;

                .term-number {
                  background-color: #2563eb;
                  color: white;
                  width: 1.5rem;
                  height: 1.5rem;
                  border-radius: 9999px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 0.875rem;
                }

                h2 {
                  font-size: 1.25rem;
                  font-weight: bold;
                  color: #1f2937;
                }
              }

              .term-info {
                margin-bottom: 1rem;
                &-title {
                  display: flex;
                  justify-content: space-between;
                }

                h3 {
                  font-size: 1.5rem;
                  font-weight: bold;
                  color: #1e40af;
                  margin-bottom: 0.25rem;
                }

                .pronunciation {
                  color: #6b7280;
                  font-size: 0.875rem;
                }

                .translation {
                  font-size: 1.125rem;
                  color: #4b5563;
                  margin: 0.5rem 0;
                }

                .tags {
                  display: flex;
                  gap: 0.5rem;
                  margin-bottom: 1rem;

                  .tag {
                    font-size: 0.75rem;
                    padding: 0.25rem 0.5rem;
                    border-radius: 0.25rem;

                    &.blue {
                      background-color: #dbeafe;
                      color: #1e40af;
                    }

                    &.green {
                      background-color: #d1fae5;
                      color: #065f46;
                    }

                    &.yellow {
                      background-color: #fef3c7;
                      color: #92400e;
                    }
                  }
                }
              }

              .description {
                color: #4b5563;
                margin-bottom: 1rem;
              }

              .examples {
                border-top: 1px solid #e5e7eb;
                padding-top: 1rem;
                margin-bottom: 1rem;

                h4 {
                  font-weight: bold;
                  color: #4b5563;
                  margin-bottom: 0.5rem;
                }

                .example {
                  background-color: #f9fafb;
                  padding: 0.75rem;
                  border-radius: 0.5rem;
                  margin-bottom: 0.5rem;

                  .english {
                    color: #1f2937;
                    margin-bottom: 0.25rem;
                  }

                  .chinese {
                    color: #6b7280;
                    font-size: 0.875rem;
                  }
                }
              }

              .actions {
                display: flex;
                gap: 0.5rem;

                button {
                  display: flex;
                  align-items: center;
                  gap: 0.5rem;
                  padding: 0.5rem 1rem;
                  border-radius: 0.5rem;
                  font-weight: 500;
                  transition: all 0.2s ease;

                  .icon {
                    height: 1.25rem;
                    width: 1.25rem;
                  }

                  &.listen-btn {
                    background-color: #2563eb;
                    color: white;

                    &:hover {
                      background-color: #1d4ed8;
                    }
                  }

                  &.practice-btn {
                    background-color: #059669;
                    color: white;

                    &:hover {
                      background-color: #047857;
                    }
                    &.recording {
                      background-color: #ff4d4f;
                    }
                    &.recording:hover {
                      background-color: #ea3a3d;
                      border-color: #ea3a3d;
                    }
                  }
                }
              }
            }
          }

          // 相关术语
          .related-terms {
            background-color: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;

            h2 {
              font-size: 1.25rem;
              font-weight: bold;
              color: #1f2937;
              display: flex;
              align-items: center;
              gap: 0.5rem;
              margin-bottom: 1rem;

              .icon {
                height: 1.25rem;
                width: 1.25rem;
                color: #2563eb;
              }
            }

            .terms-grid {
              display: grid;
              grid-template-columns: repeat(1, 1fr);
              gap: 1rem;

              @media (min-width: 768px) {
                grid-template-columns: repeat(2, 1fr);
              }

              .related-term {
                border: 1px solid #e5e7eb;
                border-radius: 0.5rem;
                padding: 0.75rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                  background-color: #f9fafb;
                }

                .term-content {
                  h3 {
                    font-weight: bold;
                    color: #2563eb;
                    margin-bottom: 0.25rem;
                  }

                  p {
                    color: #4b5563;
                  }
                }

                .arrow {
                  height: 1.25rem;
                  width: 1.25rem;
                  color: #9ca3af;
                }
              }
            }
          }

          // 练习部分
          .exercise-section {
            background-color: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;

            h2 {
              font-size: 1.25rem;
              font-weight: bold;
              color: #1f2937;
              display: flex;
              align-items: center;
              gap: 0.5rem;
              margin-bottom: 1rem;

              .icon {
                height: 1.25rem;
                width: 1.25rem;
                color: #f59e0b;
              }
            }

            .fill-blanks {
              margin-bottom: 2rem;

              h3 {
                font-weight: 500;
                color: #4b5563;
                margin-bottom: 0.5rem;
              }

              p {
                color: #6b7280;
                margin-bottom: 1rem;
              }

              .exercise {
                margin-bottom: 1rem;
                padding: 0.75rem;
                border: 1px solid #e5e7eb;
                border-radius: 0.5rem;

                p {
                  margin-bottom: 0.5rem;
                }

                .options {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 0.5rem;

                  .option {
                    border: 1px solid #d1d5db;
                    border-radius: 0.25rem;
                    padding: 0.25rem 0.75rem;
                    transition: all 0.2s ease;

                    &:hover {
                      background-color: #f3f4f6;
                      border-color: #9ca3af;
                    }

                    &.correct {
                      background-color: #d1fae5;
                      border-color: #059669;
                      color: #065f46;
                      font-weight: 500;
                    }

                    &.incorrect {
                      background-color: #fee2e2;
                      border-color: #dc2626;
                      color: #991b1b;
                      font-weight: 500;
                    }
                  }
                }
              }
            }

            .dialogue-practice {
              h3 {
                font-weight: 500;
                color: #4b5563;
                margin-bottom: 0.5rem;
              }

              p {
                color: #6b7280;
                margin-bottom: 1rem;
              }

              .dialogue {
                border: 1px solid #e5e7eb;
                border-radius: 0.5rem;
                padding: 1rem;

                .message {
                  display: flex;
                  gap: 0.5rem;
                  margin-bottom: 1rem;

                  .avatar {
                    width: 2rem;
                    height: 2rem;
                    border-radius: 9999px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                  }

                  &.supervisor .avatar {
                    background-color: #dbeafe;
                    color: #2563eb;
                  }

                  &.engineer .avatar {
                    background-color: #d1fae5;
                    color: #059669;
                  }

                  .content {
                    .role {
                      font-size: 0.875rem;
                      color: #6b7280;
                      margin-bottom: 0.25rem;
                    }

                    .record-btn {
                      margin-top: 0.5rem;
                      background-color: #059669;
                      color: white;
                      border-radius: 9999px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      transition: all 0.2s ease;
                      border-radius: 50% 50%;
                      width: 46px;
                      height: 46px;

                      &:hover {
                        background-color: #047857;
                      }
                      &.recording {
                        background-color: #ea3a3d;
                      }
                      &.recording:hover {
                        background-color: #ea3a3d;
                        border-color: #ea3a3d;
                      }

                      .icon {
                        height: 1.25rem;
                        width: 1.25rem;
                      }
                    }
                  }
                }

                .reference {
                  background-color: #f9fafb;
                  padding: 0.75rem;
                  border-radius: 0.5rem;

                  p {
                    font-size: 0.875rem;
                    color: #6b7280;
                    margin: 0;
                  }
                  +.reference {
                    margin-top: 10px;
                  }
                }
              }
            }
          }

          // 导航按钮
          .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;

            button {
              display: flex;
              align-items: center;
              gap: 0.25rem;
              transition: all 0.2s ease;

              .icon {
                height: 1.25rem;
                width: 1.25rem;
              }

              &.prev-btn {
                color: #6b7280;
                background-color: transparent;

                &:hover:not(:disabled) {
                  color: #2563eb;
                }
              }

              &.next-btn {
                background-color: #2563eb;
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 0.5rem;

                &:hover:not(:disabled) {
                  background-color: #1d4ed8;
                }
              }

              &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
              }
            }

            .pagination {
              color: #6b7280;
            }
          }
        }
      }
    }
  }

  // 练习发音弹窗
  .practice-modal {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;

    .modal-content {
      background-color: white;
      border-radius: 0.75rem;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 32rem;
      margin: 1rem;
      overflow: hidden;

      .modal-header {
        background-color: #2563eb;
        color: white;
        padding: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h2 {
          font-weight: bold;
          font-size: 1.25rem;
        }

        .close-btn {
          color: white;

          &:hover {
            color: #bfdbfe;
          }

          .icon {
            height: 1.5rem;
            width: 1.5rem;
          }
        }
      }

      .practice-content {
        padding: 1.5rem;

        .term-info {
          text-align: center;
          margin-bottom: 1.5rem;

          h3 {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 0.25rem;
          }

          p {
            color: #6b7280;
          }
        }

        .play-btn {
          margin: 0 auto 1.5rem;
          background-color: #2563eb;
          color: white;
          width: 4rem;
          height: 4rem;
          border-radius: 9999px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;

          &:hover {
            background-color: #1d4ed8;
          }

          .icon {
            height: 2rem;
            width: 2rem;
          }
        }

        .instruction {
          text-align: center;
          color: #4b5563;
          margin-bottom: 1.5rem;
        }

        .record-btn {
          margin: 0 auto 1.5rem;
          background-color: #dc2626;
          color: white;
          width: 4rem;
          height: 4rem;
          border-radius: 9999px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;

          &:hover {
            background-color: #b91c1c;
          }

          .icon {
            height: 2rem;
            width: 2rem;
          }
        }

        .feedback {
          background-color: #f9fafb;
          border-radius: 0.5rem;
          padding: 1rem;
          margin-bottom: 1.5rem;

          h4 {
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 0.5rem;
          }

          .accuracy {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.25rem;

            span {
              font-size: 0.875rem;
              color: #6b7280;

              &.score {
                color: #059669;
                font-weight: 500;
              }
            }
          }

          .progress-bar {
            width: 100%;
            height: 0.5rem;
            background-color: #d1d5db;
            border-radius: 9999px;
            margin-bottom: 1rem;

            .progress {
              height: 100%;
              background-color: #059669;
              border-radius: 9999px;
            }
          }

          .feedback-text {
            font-size: 0.875rem;
            color: #4b5563;

            p {
              margin-bottom: 0.5rem;

              span {
                font-weight: 500;
              }
            }
          }
        }

        .modal-actions {
          display: flex;
          justify-content: flex-end;
          gap: 0.5rem;

          button {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.2s ease;

            &.retry-btn {
              background-color: #f3f4f6;
              color: #1f2937;

              &:hover {
                background-color: #e5e7eb;
              }
            }

            &.complete-btn {
              background-color: #2563eb;
              color: white;

              &:hover {
                background-color: #1d4ed8;
              }
            }
          }
        }
      }
    }
  }
}

.recording-status {
  font-size: 12px;
  margin-top: 8px;
}

.evaluation-result {
  margin-top: 1rem;
  padding: 1rem;
  &__title {
    // text-align: center;
    // width: 100%;
  }
}

.scores {
  display: flex;
  justify-content: space-around;
  margin-bottom: 1rem;
}

.score-item {
  text-align: center;
  font-size: 0.9rem;
  color: #999;
}

.score {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: #1890ff;
}

.score-overall {
  color: #ff4d4f;
}

.feedback-details {
  border-radius: 4px;
  background-color: #f8f9fa;
  margin-top: 1rem;
  padding: 0.5rem;
}

.overall-feedback {
  font-size: 14px;
  color: #666;
  margin-top: 10px;
  line-height: 26px;
}

.loading-indicator {
  text-align: center;
  margin-top: 1rem;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #f3f3f3;
  bordertop: 3px solid #1890ff;
  borderradius: "50%";
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.transcription-progress {
  margin-top: 1rem;
  width: 100%;
}

.progress-bar {
  height: 8px;
  background-color: #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  margin: 0.5rem 0;
}

.progress {
  height: 100%;
  background-color: #1890ff;
  transition: width 0.3s ease;
}

.stream-results {
  margin-top: 1rem;
  width: 100%;
}

.results-display {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 1rem;
  min-height: 2em;
  background-color: #ffffff;
  margin-top: 0.5rem;
  font-style: italic;
  color: #333;
}

.transcription-error {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #fff0f0;
  border-left: 4px solid #ff4d4f;
  color: #ff4d4f;
}

.retry-btn {
  margin-top: 0.5rem;
}
