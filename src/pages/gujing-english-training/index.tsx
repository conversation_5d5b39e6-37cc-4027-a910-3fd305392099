import React, { useState, useEffect } from 'react'
import terminologyData from './termData.json'
import { Button, Flex } from 'antd'
import './index.less'
import { wordPronunciation, getEvaluatePronunciation, getVoiceRecognize } from '@/api/voiceFetch'
import { useAudioRecorder } from '@/hooks/useAudioRecorder'
// import { useSpeechToText } from "@/hooks/useSpeechToText";

// TypeScript interfaces
interface Tag {
  text: string
  color: string
}

interface Example {
  english: string
  chinese: string
}

interface RelatedTerm {
  term: string
  translation: string
}

interface FillBlankExercise {
  question: string
  options: string[]
  answer: string
}

interface Dialogue {
  context: string
  question: string
  reference: string
}

interface Term {
  id: number
  term: string
  pronunciation: string
  translation: string
  tags: Tag[]
  description: string
  examples: Example[]
  related: RelatedTerm[]
  exercises: {
    fillBlanks: FillBlankExercise[]
    dialogue: Dialogue
  }
}

// Components
const NavBar: React.FC = () => {
  return (
    <nav className='nav-bar'>
      <div className='container'>
        <div className='logo'>
          <svg
            xmlns='http://www.w3.org/2000/svg'
            className='icon'
            viewBox='0 0 24 24'
            fill='none'
            stroke='currentColor'
            strokeWidth='2'
          >
            <path d='M12 2v6m0 0a4 4 0 1 0 0 8 4 4 0 0 0 0-8zm0 12v2m-6-8h-2m16 0h-2m-5-9l-3 3m11 11l-3-3m-12 0l-3 3m11-17l3 3' />
          </svg>
          <span>固井英语训练营</span>
        </div>
        <div className='nav-links'>
          <div className='desktop-links'>
            <a href='#/gujing-english-training'>固井基础术语</a>
            <a href='#/gujing-command-dialogue'>设备操作指令对话</a>
          </div>
          <div className='user-info'>
            <div className='avatar'>张</div>
            <span>张工程师</span>
          </div>
        </div>
      </div>
    </nav>
  )
}

const CourseMenu: React.FC<{
  currentIndex: number
  onTermSelect: (index: number) => void
}> = ({ currentIndex, onTermSelect }) => {
  return (
    <div className='course-menu'>
      <div className='menu-header'>
        <svg
          xmlns='http://www.w3.org/2000/svg'
          className='icon'
          viewBox='0 0 24 24'
          fill='none'
          stroke='currentColor'
          strokeWidth='2'
        >
          <path d='M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253' />
        </svg>
        课程目录
      </div>
      <div className='terms-list'>
        {terminologyData.terms.map((term, index) => (
          <div
            key={term.id}
            className={`term-item ${index === currentIndex ? 'active' : ''}`}
            onClick={() => onTermSelect(index)}
          >
            <div className='term-indicator'>
              {index === currentIndex && (
                <svg xmlns='http://www.w3.org/2000/svg' className='indicator' viewBox='0 0 20 20' fill='currentColor'>
                  <circle cx='10' cy='10' r='6' />
                </svg>
              )}
            </div>
            {term.term}
          </div>
        ))}
      </div>
    </div>
  )
}

const TermCard: React.FC<{
  term: Term
  onPractice: () => void
}> = ({ term, onPractice }) => {
  const [isLoading, setIsLoading] = useState(false)
  const [evaluationResult, setEvaluationResult] = useState<any>(null)
  const [isEvaluating, setIsEvaluating] = useState(false)

  // 使用自定义Hook来处理录音功能
  const { isRecording, recordingStatus, audioBlob, startRecording, stopRecording } = useAudioRecorder()

  // 当录音停止时自动调用评估
  useEffect(() => {
    if (!isRecording && audioBlob) {
      evaluatePronunciation()
    }
  }, [isRecording, audioBlob])

  // 修改这个函数以接受term参数
  const evaluatePronunciation = async () => {
    if (!audioBlob) {
      console.error('没有可用的音频数据')
      return
    }

    try {
      setIsEvaluating(true)

      // 构建请求数据
      const referenceText = term.term
      const language = 'en'

      const formData = new FormData()
      formData.append('file', audioBlob, 'recording.wav')
      formData.append('reference_text', referenceText)
      formData.append('language', language)
      formData.append('detailed', 'true')

      // 调用API进行发音评估
      const response = await getEvaluatePronunciation(formData)

      // 检查响应结果
      if (response.success) {
        console.log('发音评估成功:', response.data)
        setEvaluationResult(response.data)
      } else {
        throw new Error(response.message || '评估失败')
      }
    } catch (error) {
      console.error('发音评估过程中发生错误:', error)
      alert(error instanceof Error ? error.message : '发音评估失败')
    } finally {
      setIsEvaluating(false)
    }
  }

  const playPronunciation = async (term: Term) => {
    console.log('Playing pronunciation')
    const keyword = {
      text: term.term,
      accent: 'us',
      speed: 1,
      voice_type: 'female'
    }

    try {
      setIsLoading(true)
      const res = await wordPronunciation(keyword)
      if (res.ok) {
        const audioBlob = await res.blob()
        const audioUrl = URL.createObjectURL(audioBlob)

        const audio = new Audio(audioUrl)
        audio.play().catch(error => {
          console.error('音频播放失败:', error)
        })

        audio.onended = () => {
          URL.revokeObjectURL(audioUrl)
        }
      } else {
        throw new Error(res.message || '生成失败')
      }
    } catch (error) {
      console.error('发音播放错误:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className='term-card'>
      <div className='term-header'>
        <div className='term-title'>
          <span className='term-number'>{term.id}</span>
          <h2>当前学习术语</h2>
        </div>
        <div className='term-info'>
          <div className='term-info-title'>
            <h3>{term.term}</h3>
            <span className='pronunciation'>{term.pronunciation}</span>
          </div>
          <p className='translation'>{term.translation}</p>
          <div className='tags'>
            {term.tags.map((tag, index) => (
              <span key={index} className={`tag ${tag.color}`}>
                {tag.text}
              </span>
            ))}
          </div>
        </div>
        <p className='description'>{term.description}</p>
        <div className='examples'>
          <h4>例句</h4>
          {term.examples.map((example, index) => (
            <div key={index} className='example'>
              <p className='english'>{example.english}</p>
              <p className='chinese'>{example.chinese}</p>
            </div>
          ))}
        </div>
        <div className='actions'>
          <Button className='listen-btn' onClick={() => playPronunciation(term)} disabled={isLoading}>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              className='icon'
              viewBox='0 0 24 24'
              fill='none'
              stroke='currentColor'
              strokeWidth='2'
            >
              <path d='M11 5L6 9H2v6h4l5 4V5z' />
              <path d='M15.54 8.46a5 5 0 1 0 0 7.07' />
              <path d='M19.07 4.93a10 10 0 1 1 0 14.14' />
            </svg>
            {isLoading ? '播放中...' : '听发音'}
          </Button>

          <Button
            className={`practice-btn ${isRecording ? 'recording' : ''}`}
            onClick={isRecording ? stopRecording : startRecording}
            disabled={isEvaluating}
          >
            <svg
              xmlns='http://www.w3.org/2000/svg'
              className='icon'
              viewBox='0 0 24 24'
              fill='none'
              stroke='currentColor'
              strokeWidth='2'
            >
              {isRecording ? (
                // 停止按钮图标
                <rect x='6' y='6' width='12' height='12' />
              ) : (
                // 录音按钮图标
                <>
                  <path d='M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z' />
                  <path d='M19 10v2a7 7 0 0 1-14 0v-2' />
                  <line x1='12' y1='19' x2='12' y2='23' />
                  <line x1='8' y1='23' x2='16' y2='23' />
                </>
              )}
            </svg>
            {isRecording ? '停止录音' : '开始录音'}
          </Button>
          <span className='recording-status'>{recordingStatus}</span>
        </div>
      </div>

      {/* 发音评估结果显示 */}
      {evaluationResult && (
        <div className='evaluation-result'>
          <h3 className='evaluation-result__title'>发音评估</h3>

          <div className='scores'>
            <div className='score-item'>
              <span className='score score-overall'>{evaluationResult.overall_score}</span>
              <span>综合评分</span>
            </div>
            <div className='score-item'>
              <span className='score'>{evaluationResult.level}</span>
              <span>评级</span>
            </div>
          </div>
          <div className='scores'>
            <div className='score-item'>
              <span className='score'>{evaluationResult.word_accuracy}%</span>
              <span>单词准确率</span>
            </div>
            <div className='score-item'>
              <span className='score'>{evaluationResult.phoneme_similarity}%</span>
              <span>音素相似度</span>
            </div>
            <div className='score-item'>
              <span className='score'>{evaluationResult.fluency_score}%</span>
              <span>流畅度</span>
            </div>
          </div>

          <div className='feedback-details'>
            <h5>对比结果:</h5>
            <div className='overall-feedback'>
              <p>参考文本: {evaluationResult.reference_text}</p>
              <p>识别结果: {evaluationResult.transcription}</p>
            </div>
          </div>

          <div className='feedback-details'>
            <h5>详细反馈:</h5>

            <div className='overall-feedback'>
              <p>{evaluationResult.feedback}</p>
            </div>
          </div>

          <div className='feedback-details'>
            <h5>性能信息:</h5>

            <div className='overall-feedback'>
              <p>处理时间: {evaluationResult.total_processing_time || evaluationResult.inference_time}秒</p>
              <p>语言: {evaluationResult.language === 'en' ? '英语' : '中文'}</p>
            </div>
          </div>
        </div>
      )}

      {/* 加载状态指示器 */}
      {isEvaluating && (
        <div className='loading-indicator'>
          <div className='spinner'></div>
          <p>正在评估发音，请稍候...</p>
        </div>
      )}
    </div>
  )
}

const RelatedTerms: React.FC<{
  terms: RelatedTerm[]
}> = ({ terms }) => {
  return (
    <div className='related-terms'>
      <h2>
        <svg
          xmlns='http://www.w3.org/2000/svg'
          className='icon'
          viewBox='0 0 24 24'
          fill='none'
          stroke='currentColor'
          strokeWidth='2'
        >
          <path d='M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3' />
        </svg>
        相关术语
      </h2>
      <div className='terms-grid'>
        {terms.map((term, index) => (
          <div key={index} className='related-term'>
            <div className='term-content'>
              <h3>{term.term}</h3>
              <p>{term.translation}</p>
            </div>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              className='arrow'
              viewBox='0 0 24 24'
              fill='none'
              stroke='currentColor'
              strokeWidth='2'
            >
              <path d='M9 18l6-6-6-6' />
            </svg>
          </div>
        ))}
      </div>
    </div>
  )
}

// src/pages/gujing-english-training/index.tsx

const ExerciseSection: React.FC<{
  exercises: Term['exercises']
}> = ({ exercises }) => {
  const [selectedAnswers, setSelectedAnswers] = useState<{ [key: string]: string }>({})
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState<number | null>(null)

  // 录音相关状态
  const { isRecording, recordingStatus, audioBlob, startRecording, stopRecording } = useAudioRecorder()

  // 流式识别相关状态
  const [isTranscribing, setIsTranscribing] = useState<boolean>(false)
  const [transcriptionError, setTranscriptionError] = useState<string | null>(null)
  const [progress, setProgress] = useState<number>(0)
  const [streamResults, setStreamResults] = useState<string>('')

  // 当录音停止时自动调用语音转文字
  useEffect(() => {
    if (!isRecording && audioBlob && currentQuestionIndex !== null) {
      handleTranscribe(currentQuestionIndex)
    }
  }, [isRecording, audioBlob, currentQuestionIndex])

  const handleOptionClick = (questionIndex: number, option: string, answer: string) => {
    setSelectedAnswers(prev => ({
      ...prev,
      [questionIndex]: option
    }))
  }

  // 处理录音按钮点击
  const handleRecordClick = (questionIndex: number) => {
    setCurrentQuestionIndex(questionIndex)

    if (isRecording) {
      stopRecording() // 停止录音
    } else {
      startRecording() // 开始录音
    }
  }

  // 更新状态的方法
  const updateStatus = (message: string, type: 'info' | 'success' | 'error') => {
    console.log(`${type}: ${message}`)
    // 这里可以添加Toast或其他UI反馈机制
  }

  // 添加识别结果
  const addResult = (text: string) => {
    setStreamResults(prev => (prev ? `${prev} ${text}` : text))

    // 如果需要实时更新答案
    if (currentQuestionIndex !== null) {
      setSelectedAnswers(prev => ({
        ...prev,
        [currentQuestionIndex]: text
      }))
    }
  }

  // 处理语音转文字
  const handleTranscribe = async (questionIndex: number) => {
    if (!audioBlob) {
      console.error('没有可用的音频数据')
      alert('请先录音')
      return
    }

    setIsTranscribing(true)
    setTranscriptionError(null)
    setProgress(0)
    setStreamResults('')

    try {
      // 创建FormData对象
      const formData = new FormData()
      formData.append('file', audioBlob, 'recording.wav')

      // 显示开始识别
      updateStatus('开始上传和识别...', 'info')

      const response = await getVoiceRecognize(formData)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // 初始化流式处理所需变量
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      // 处理流式响应
      while (true) {
        const { done, value } = await reader.read()

        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')

        // 保留最后一行（可能不完整）
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.trim()) {
            try {
              if (line.startsWith('data: ')) {
                const jsonStr = line.substring(6)

                if (jsonStr.trim() === '[DONE]') {
                  continue
                }

                const result = JSON.parse(jsonStr)

                // 处理进度更新
                if (result.type === 'progress' && result.data) {
                  const progressValue = result.data.progress || 0
                  setProgress(progressValue)
                  updateStatus(`处理进度: ${progressValue.toFixed(1)}%`, 'info')

                  // 处理识别结果
                } else if (result.type === 'result' && result.data) {
                  const text = result.data.text || ''

                  if (text.trim()) {
                    addResult(text)
                  }
                } else if (result.type === 'complete' && result.data) {
                  updateStatus(result.data.message || '处理完成', 'success')
                }
              }
            } catch (e) {
              console.warn('解析响应行时出错:', line, e)
            }
          }
        }
      }
    } catch (error) {
      console.error('语音识别过程中发生错误:', error)
      setTranscriptionError(error instanceof Error ? error.message : '语音识别失败')
      updateStatus(`识别失败: ${error.message}`, 'error')
    } finally {
      setIsTranscribing(false)
      setTimeout(() => {
        setProgress(0)
      }, 2000)
    }
  }

  return (
    <div className='exercise-section'>
      <h2>
        <svg
          xmlns='http://www.w3.org/2000/svg'
          className='icon'
          viewBox='0 0 24 24'
          fill='none'
          stroke='currentColor'
          strokeWidth='2'
        >
          <path d='M12 10v6m0 0v6m0-6h6m-6 0H6' />
        </svg>
        互动练习
      </h2>

      <div className='fill-blanks'>
        <h3>完成句子</h3>
        <p>使用本课学到的术语填空</p>
        {exercises.fillBlanks.map((exercise, index) => (
          <div key={index} className='exercise'>
            <p>{exercise.question}</p>
            <div className='options'>
              {exercise.options.map(option => (
                <Button
                  key={option}
                  className={`option ${
                    selectedAnswers[index] === option ? (option === exercise.answer ? 'correct' : 'incorrect') : ''
                  }`}
                  onClick={() => handleOptionClick(index, option, exercise.answer)}
                >
                  {option}
                </Button>
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className='dialogue-practice'>
        <h3>情景对话练习</h3>
        <p>模拟实际工作中的对话场景</p>
        <div className='dialogue'>
          <div className='message supervisor'>
            <div className='avatar'>A</div>
            <div className='content'>
              <p className='role'>Supervisor:</p>
              <p>{exercises.dialogue.question}</p>
            </div>
          </div>
          <div className='message engineer'>
            <div className='avatar'>B</div>
            <div className='content'>
              <p className='role'>You (Engineer):</p>

              {/* 替换原有的录音按钮 */}
              <div className='record-answer-container'>
                <Button className={`record-btn ${isRecording ? 'recording' : ''}`} onClick={() => handleRecordClick(0)}>
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    className='icon'
                    viewBox='0 0 24 24'
                    fill='none'
                    stroke='currentColor'
                    strokeWidth='2'
                  >
                    {isRecording ? (
                      // 停止按钮图标
                      <rect x='6' y='6' width='12' height='12' />
                    ) : (
                      // 录音按钮图标
                      <>
                        <path d='M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z' />
                        <path d='M19 10v2a7 7 0 0 1-14 0v-2' />
                        <line x1='12' y1='19' x2='12' y2='23' />
                        <line x1='8' y1='23' x2='16' y2='23' />
                      </>
                    )}
                  </svg>
                  {/* {isRecording ? "停止录音" : "点击录音回答"} */}
                </Button>

                {/* 进度条 */}
                {/* {isTranscribing && (
                  <div className="transcription-progress">
                    <div className="progress-bar">
                      <div className="progress" style={{ width: `${progress}%` }} />
                    </div>
                    <p>处理进度: {progress.toFixed(1)}%</p>
                  </div>
                )} */}

                {/* 流式识别结果 */}
                {/* {(isTranscribing || streamResults) && (
                  <div className="stream-results">
                    <h4>实时识别:</h4>
                    <div className="results-display">
                      <p>{streamResults || "等待识别结果..."}</p>
                    </div>
                  </div>
                )} */}

                {/* 错误信息 */}
                {transcriptionError && (
                  <div className='transcription-error'>
                    <p>⚠️ 语音识别失败: {transcriptionError}</p>
                    <Button className='retry-btn' onClick={() => handleRecordClick(0)}>
                      重试
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
          {(isTranscribing || streamResults) && (
            <>
              <div className='reference'>
                <p>识别内容: "{streamResults || '等待识别结果...'}"</p>
              </div>
              <div className='reference'>
                <p>参考回答: "{exercises.dialogue.reference}"</p>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

// const PracticeModal: React.FC<{
//   term: Term;
//   isOpen: boolean;
//   onClose: () => void;
// }> = ({ term, isOpen, onClose }) => {
//   const [showFeedback, setShowFeedback] = useState(false);

//   if (!isOpen) return null;

//   return (
//     <div className="practice-modal">
//       <div className="modal-content">
//         <div className="modal-header">
//           <h2>发音练习</h2>
//           <Button className="close-btn" onClick={onClose}>
//             <svg
//               xmlns="http://www.w3.org/2000/svg"
//               className="icon"
//               viewBox="0 0 24 24"
//               fill="none"
//               stroke="currentColor"
//               strokeWidth="2"
//             >
//               <path d="M6 18L18 6M6 6l12 12" />
//             </svg>
//           </Button>
//         </div>

//         <div className="practice-content">
//           <div className="term-info">
//             <h3>{term.term}</h3>
//             <p>{term.pronunciation}</p>
//           </div>

//           <Button className="play-btn">
//             <svg
//               xmlns="http://www.w3.org/2000/svg"
//               className="icon"
//               viewBox="0 0 24 24"
//               fill="none"
//               stroke="currentColor"
//               strokeWidth="2"
//             >
//               <polygon points="5 3 19 12 5 21 5 3" />
//             </svg>
//           </Button>

//           <p className="instruction">先听标准发音，然后点击下方按钮开始录音</p>

//           <Button className="record-btn" onClick={() => setShowFeedback(true)}>
//             <svg
//               xmlns="http://www.w3.org/2000/svg"
//               className="icon"
//               viewBox="0 0 24 24"
//               fill="none"
//               stroke="currentColor"
//               strokeWidth="2"
//             >
//               <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" />
//               <path d="M19 10v2a7 7 0 0 1-14 0v-2" />
//               <line x1="12" y1="19" x2="12" y2="23" />
//               <line x1="8" y1="23" x2="16" y2="23" />
//             </svg>
//           </Button>

//           {showFeedback && (
//             <div className="feedback">
//               <h4>发音评估</h4>
//               <div className="accuracy">
//                 <span>准确度</span>
//                 <span className="score">85%</span>
//               </div>
//               <div className="progress-bar">
//                 <div className="progress" style={{ width: "85%" }} />
//               </div>

//               <div className="feedback-text">
//                 <p>
//                   <span>优点：</span>元音发音清晰，语速适中
//                 </p>
//                 <p>
//                   <span>建议：</span>注意"{term.term}"中的关键音节发音，尝试更准确地模仿标准发音
//                 </p>
//               </div>
//             </div>
//           )}

//           <div className="modal-actions">
//             <Button className="retry-btn" onClick={() => setShowFeedback(false)}>
//               重新练习
//             </Button>
//             <Button className="complete-btn" onClick={onClose}>
//               完成练习
//             </Button>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

const GujingEnglishTraining: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const currentTerm = terminologyData.terms[currentIndex]
  const totalTerms = terminologyData.terms.length

  const handlePrevTerm = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }

  const handleNextTerm = () => {
    if (currentIndex < totalTerms - 1) {
      setCurrentIndex(currentIndex + 1)
    }
  }

  return (
    <div className='gujing-english-training'>
      <NavBar />
      <div className='main-content'>
        <div className='container'>
          <div className='content-wrapper'>
            <aside className='sidebar'>
              <CourseMenu currentIndex={currentIndex} onTermSelect={setCurrentIndex} />
            </aside>

            <main className='main-section'>
              <div className='section-header'>
                <h1>固井基础术语</h1>
                <p>掌握固井行业的核心术语，为高效沟通打下基础</p>
              </div>

              <TermCard term={currentTerm} onPractice={() => setIsModalOpen(true)} />

              <RelatedTerms terms={currentTerm.related} />

              <ExerciseSection exercises={currentTerm.exercises} />

              <div className='navigation'>
                <Button className='prev-btn' onClick={handlePrevTerm} disabled={currentIndex === 0}>
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    className='icon'
                    viewBox='0 0 24 24'
                    fill='none'
                    stroke='currentColor'
                    strokeWidth='2'
                  >
                    <path d='M15 18l-6-6 6-6' />
                  </svg>
                  上一个术语
                </Button>
                <div className='pagination'>
                  <span>{currentIndex + 1}</span> / <span>{totalTerms}</span>
                </div>
                <Button className='next-btn' onClick={handleNextTerm} disabled={currentIndex === totalTerms - 1}>
                  下一个术语
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    className='icon'
                    viewBox='0 0 24 24'
                    fill='none'
                    stroke='currentColor'
                    strokeWidth='2'
                  >
                    <path d='M9 18l6-6-6-6' />
                  </svg>
                </Button>
              </div>
            </main>
          </div>
        </div>
      </div>

      {/* <PracticeModal term={currentTerm} isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} /> */}
    </div>
  )
}

export default GujingEnglishTraining
