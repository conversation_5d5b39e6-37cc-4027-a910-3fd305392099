import React, { useCallback, useState } from 'react'
import { Input, Typography, Layout, Flex, List, Space, Spin, Card, Table, ConfigProvider, message } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import { FileWordOutlined } from '@ant-design/icons'
import { regulatoryVerification } from '@/api/regulatoryVerification'
import { extractJSONFromString } from '@/utils/json-extractor'
import { getFileType } from '@/utils/file'
import './index.less'

const { Content, Header } = Layout

const columns = [
  {
    title: '项目',
    dataIndex: 'key',
    key: 'key',
    width: 150
  },
  {
    title: '内容描述',
    dataIndex: 'value',
    key: 'value'
  }
]

export const RegulatoryVerification: React.FC = () => {
  const [generating, setGenerating] = useState<boolean>(false)
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [data, setData] = useState<any[]>([])
  const [dataSource, setDataSource] = useState<any[]>([])

  const handleGeneration = useCallback(
    async (query: string) => {
      if (!query) {
        message.warning('请输入查询内容')
        return
      }
      setGenerating(true)
      setSearchQuery(query)
      let str = ''

      try {
        await regulatoryVerification(searchQuery, {
          onMessage: (text, finished) => {
            setGenerating(false)
            if (text) {
              str += text
            }
            if (finished) {
              const result = extractJSONFromString(str)
              try {
                let obj = JSON.parse(result || '{}')
                const dataSource = Object.keys(obj).map(key => ({
                  key,
                  value: obj[key]
                }))
                setDataSource(dataSource)
              } catch (error) {
                setDataSource([])
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: (data: any) => {
            setData(data.metadata?.retriever_resources || [])
          }
        })
      } catch (err) {
        setGenerating(false)
      }
    },
    [searchQuery]
  )

  const downloadFile = useCallback((text: string, filename: string) => {
    const fileType = getFileType(filename)
    const blob = new Blob([text], { type: fileType })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }, [])

  const handleSearch = () => {
    handleGeneration(searchQuery)
  }

  const locale = {
    ...zhCN,
    Table: {
      ...zhCN.Table,
      emptyText: '暂无数据'
    }
  }

  return (
    <ConfigProvider locale={locale}>
      <div className='regulatory-verification'>
        <Spin tip='加载中' spinning={generating} fullscreen size='large' />
        <Layout className='bg-transparent'>
          <Header className='bg-transparent'>
            <Flex className='regulatory-verification-toolbar' justify='center'>
              <Typography.Text className='title-text'>规章制度校验</Typography.Text>
            </Flex>
          </Header>
          <Content className='regulatory-verification-content'>
            <Flex style={{ marginBottom: '15px' }}>
              <Input.Search
                size='large'
                style={{ backgroundColor: '#fff', borderRadius: '8px' }}
                placeholder='请输入查询内容，例如:安全制度是否满足规则?'
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                onSearch={handleSearch}
                enterButton='查询'
              />
            </Flex>
            <Card title='校验内容:' style={{ marginBottom: 20 }}>
              <List
                dataSource={[
                  '运维目的: 是否说明该运维工作的核心目标',
                  '运维主体: 是否明确指定负责部门/单位(如技术部/第三方服务商)',
                  '运维人员: 列举具体岗位角色(工程师/管理员等)',
                  '运维工具: 是否明确使用的软件/硬件工具名称及版本',
                  '运维要求: 是否明确技术标准/响应时间等硬性指标',
                  '运维办法: 分步骤描述标准操作流程',
                  '考核方法: 量化考核指标和评估方式'
                ]}
                renderItem={item => (
                  <List.Item>
                    <Typography.Text> - {item}</Typography.Text>
                  </List.Item>
                )}
              />
            </Card>
            <Card title='校验结果:'>
              <Table bordered columns={columns} dataSource={dataSource} pagination={false} />

              {data.length > 0 && (
                <List
                  size='small'
                  style={{ width: '650px', marginTop: 20 }}
                  header={<div>引用</div>}
                  bordered
                  dataSource={data}
                  renderItem={item => (
                    <List.Item>
                      <Space>
                        <FileWordOutlined style={{ color: '#1890ff' }} />
                        <Typography.Link
                          style={{ cursor: 'pointer' }}
                          onClick={() => downloadFile(item.content, item.document_name)}
                        >
                          {item.document_name}
                        </Typography.Link>
                      </Space>
                    </List.Item>
                  )}
                />
              )}
            </Card>
          </Content>
        </Layout>
      </div>
    </ConfigProvider>
  )
}

export default RegulatoryVerification