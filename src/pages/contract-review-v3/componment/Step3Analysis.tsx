import { Card } from 'antd'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import RemarkBreaks from 'remark-breaks'
import RemarkMath from 'remark-math'
import './Step3Analysis.less'

export const Step3Analysis: React.FC<{
  data: string
  isEnd: boolean
}> = ({ data, isEnd }) => {
  return (
    <Card title='校验结果' className='contract-step-card'>
      <ReactMarkdown
        className='markdown-body contract-analysis-container'
        remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}
      >
        {data}
      </ReactMarkdown>
    </Card>
  )
}

export default Step3Analysis
