import React, { useState } from 'react'
import { Spin, message, Flex, Typography, <PERSON>, Button } from 'antd'
import { Template } from './componment/template'
import Step2BasicInfo from './componment/Step2BasicInfo'
import Step3Analysis from './componment/Step3Analysis'
import { reviewContrat } from '@/api/reviewContratV2'
import './index.less'

const { Step } = Steps

export const ContratReviewV2: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [step3message, setStep3message] = useState<string>('')
  const [generating, setGenerating] = useState<boolean>(false)
  const [isEnd, setIsEnd] = useState<boolean>(false)
  const [templateData, setTemplateData] = useState<string>('')
  const [jsonObj, setJsonObj] = useState<any>(null)
  const [current, setCurrent] = useState(0)
  const [uploadedFile, setUploadedFile] = useState<{ name: string; id: string }>()
  const [step2UploadedFile, setStep2UploadedFile] = useState<{ name: string; id: string }>()

  const steps = [
    {
      title: '模板解析',
      content: (
        <Template
          onUpload={setTemplateData}
          setCurrent={setCurrent}
          generating={generating}
          file={uploadedFile}
          setGenerating={setGenerating}
          onUploadFile={setUploadedFile}
        />
      )
    },
    {
      title: '内容校验',
      content: (
        <Step2BasicInfo
          data={templateData}
          onUpload={setJsonObj}
          file={uploadedFile}
          onUploadFile={setStep2UploadedFile}
          generating={generating}
        />
      )
    },
    { title: '校验结果', content: <Step3Analysis data={step3message} isEnd={isEnd} /> }
  ]

  const handleGenerationStart = async () => {
    setStep3message('')
    setIsEnd(false)
    setGenerating(true)
    let res = ''
    try {
      await reviewContrat(
        {
          type: '内容校验',
          files: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: step2UploadedFile?.id
            }
          ],
          query: jsonObj
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              setStep3message(p => p + message)
            }
            if (finished) {
              setGenerating(false)
              setIsEnd(true)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  const nextStep = () => {
    if (current === 1) {
      if (!step2UploadedFile?.id) {
        messageApi.error('请先上传文件')
        return
      }
      setCurrent(current + 1)
      handleGenerationStart()
    } else {
      setCurrent(current + 1)
    }
  }

  return (
    <div className='contract-report-container'>
      {contextHolder}
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      <Flex className='toolbar' justify='center'>
        <Typography.Text className='title-text'>合同审查</Typography.Text>
      </Flex>
      <div className='steps-container'>
        <Steps current={current} className='custom-steps'>
          {steps.map(item => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <div className='steps-content'>
          {steps.map((step, index) => (
            <div key={index} className={`step-content ${index === current ? 'active' : 'hidden'}`}>
              {step.content}
            </div>
          ))}
        </div>

        <div className='steps-action'>
          {current > 0 && (
            <Button style={{ marginRight: 8 }} onClick={() => setCurrent(current - 1)}>
              上一步
            </Button>
          )}
          {current < steps.length - 1 && current !== 0 && (
            <Button type='primary' onClick={nextStep}>
              下一步
            </Button>
          )}
          {current === steps.length - 1 && (
            <>
              <Button
                style={{ marginRight: 8 }}
                onClick={() => {
                  setUploadedFile(undefined)
                  setStep2UploadedFile(undefined)
                  setCurrent(0)
                }}
              >
                重新开始
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default ContratReviewV2
