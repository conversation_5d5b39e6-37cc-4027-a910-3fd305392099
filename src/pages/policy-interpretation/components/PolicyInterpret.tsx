import React from 'react'
import { Typography, Row, Col, Card, Divider } from 'antd'
import './PolicyInterpret.less'

const { Title, Paragraph, Text } = Typography

interface PolicyInterpretationProps {
  interpretList: any
}

const PolicyInterpretation: React.FC<PolicyInterpretationProps> = ({
  interpretList,
}) => (
  <div>
    <div
      style={{
        width: '100%',
        margin: '0 auto',
        background: '#fff',
        borderRadius: 8,
        // padding: 32,
        paddingBottom: 0,
      }}
    >
      {interpretList.ent && (
        <Card style={{ marginBottom: 32 }}>
          {/* 企业分析标题加蓝色边框 */}
          <div className="pi-title-bar">
            <div className="pi-title-bar-line" />
            <Title level={5} className="pi-title-bar-text">
              企业分析
            </Title>
          </div>
          <Paragraph style={{ margin: 0, fontSize: 15 }}>
            {interpretList.ent?.value || ''}
          </Paragraph>
        </Card>
      )}
      <Row gutter={24}>
        {interpretList.jiedu?.map((item: any, idx: number) => {
          const count = interpretList.jiedu?.length || 0
          let span = 8
          if (count === 1) span = 24
          else if (count === 2) span = 12
          else span = 8
          return (
            <Col
              key={idx}
              span={span}
              xs={24}
              sm={24}
              md={span}
              lg={span}
              xl={span}
              style={{ marginBottom: 24 }}
            >
              <Card
                style={{
                  minHeight: 500,
                  borderRadius: 8,
                  boxShadow: '0 2px 8px #f0f1f2',
                }}
              >
                <div style={{ marginBottom: 12 }}>
                  <Title
                    level={4}
                    style={{ textAlign: 'left', margin: 0, fontSize: 18 }}
                  >
                    {item.title}
                  </Title>
                </div>
                {/* 政策解读分组 */}
                <div style={{ marginBottom: 18 }}>
                  <div className="pi-title-bar">
                    <div className="pi-title-bar-line" />
                    <Title level={5} className="pi-title-bar-text">
                      政策解读
                    </Title>
                  </div>
                  {item.政策解读?.map((group: any, i: number) => (
                    <div key={i} style={{ marginBottom: 10 }}>
                      <Text>【{group.key}】</Text>
                      {group.value?.map((v: string, vi: number) => (
                        <Paragraph
                          style={{ margin: '4px 0 0 0', fontSize: 14 }}
                          key={vi}
                        >
                          {v}
                        </Paragraph>
                      ))}
                    </div>
                  ))}
                </div>
                <Divider style={{ margin: '12px 0' }} />
                {/* 影响分析分组 */}
                <div style={{ marginBottom: 18 }}>
                  <div className="pi-title-bar">
                    <div className="pi-title-bar-line" />
                    <Title level={5} className="pi-title-bar-text">
                      企业影响分析
                    </Title>
                  </div>
                  {item.影响分析?.map((group: any, i: number) => (
                    <div key={i} style={{ marginBottom: 10 }}>
                      <Text>【{group.key}】</Text>
                      {group.value?.map((v: string, vi: number) => (
                        <Paragraph
                          style={{ margin: '4px 0 0 0', fontSize: 14 }}
                          key={vi}
                        >
                          {v}
                        </Paragraph>
                      ))}
                    </div>
                  ))}
                </div>
                <Divider style={{ margin: '12px 0' }} />
                {/* 应对策略分组 */}
                <div>
                  <div className="pi-title-bar">
                    <div className="pi-title-bar-line" />
                    <Title level={5} className="pi-title-bar-text">
                      企业应对策略
                    </Title>
                  </div>
                  {item.应对策略?.map((group: any, i: number) => (
                    <div key={i} style={{ marginBottom: 10 }}>
                      <Text>【{group.key}】</Text>
                      {group.value?.map((v: string, vi: number) => (
                        <Paragraph
                          style={{ margin: '4px 0 0 0', fontSize: 14 }}
                          key={vi}
                        >
                          {v}
                        </Paragraph>
                      ))}
                    </div>
                  ))}
                </div>
              </Card>
            </Col>
          )
        })}
      </Row>
    </div>
  </div>
)

export default PolicyInterpretation
