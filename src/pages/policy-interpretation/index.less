.work-report-container {
  width: 100vw;
  // height: 100vh;
  .custom-steps {
    padding: 20px 10vw 40px;
  }
  .ant-tag {
    background-color: #fff!important;
    color: #333!important;
    .anticon.anticon-fire {
      color: #ff523b!important;
    }
  }
  .ant-radio-button-wrapper {
    border-radius: 6px;
    margin-right: 10px;
    flex: none;

  }
  .report-type-item {
    border-radius: 6px;
    margin-right: 10px;
    padding: 8px 15px;
    border: 1px solid #d9d9d9;
    cursor: pointer;
  }
  .report-type-item-active {
    border-color: #2156f3;
    color: #2156f3;
  }
  .ant-upload-drag-icon {
    margin-bottom: 16px;
    font-size: 48px;
    color: #1890ff;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }
}

.custom-steps-text {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 32px 0 24px 0;
  width: 80vw;
  min-width: 320px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;

  .step-active {
    color: #333;
    font-weight: bold;
    font-size: 18px;
    transition: color 0.2s;
  }
  .step-inactive {
    // color: #666;
    color: #7F7F7F;
    font-size: 18px;
    font-weight: normal;
    transition: color 0.2s;
  }
  .step-divider {
    flex: 1;
    height: 1px;
    background: #e0e0e0;
    margin: 0 32px;
    vertical-align: middle;
    min-width: 24px;
  }
}

