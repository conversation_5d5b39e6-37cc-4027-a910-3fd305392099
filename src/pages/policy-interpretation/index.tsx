import React, { useState, useEffect } from 'react'
import {
  Steps,
  Button,
  Card,
  Input,
  Typography,
  Flex,
  Tag,
  Radio,
  message,
  Spin,
  Upload,
  Modal,
  Layout,
} from 'antd'
import {
  CloseOutlined,
  ArrowRightOutlined,
  FireFilled,
  CheckCircleFilled,
  InboxOutlined,
  DeleteOutlined,
} from '@ant-design/icons'
import './index.less'
import { policyInterpretation } from '@/api/policyInterpretation'
import { uploadFile, convertFileToPDF } from '@/api/template'
import StreamTypewriter from '@/component/StreamTypewriter'
import PolicyInterpret from './components/PolicyInterpret'

const { Step } = Steps
const VITE_POLICY_INTERPRETATION =
  import.meta.env['VITE_POLICY_INTERPRETATION'] || ''

const titleOptions = ['政策解析', '政策解读', '制度校验']
export const PolicyInterpretation: React.FC = () => {
  const [current, setCurrent] = useState(0)
  const [title, setTitle] = useState('')
  const [company, setCompany] = useState('')
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState<boolean>(false)
  const [infoMessage, setInfoMessage] = useState('')
  const [interpretList, setInterpretList] = useState<any>({})
  const [interpretJson, setInterpretJson] = useState<any>('')
  const [step3Message, setStep3Message] = useState('')
  const [step4Message, setStep4Message] = useState('')
  const [modalOpen, setModalOpen] = useState(false)
  const [systemInfo, setSystemInfo] = useState('')
  const [regenerateCount, setRegenerateCount] = useState(0)
  const [isShowMk, setIsShowMk] = useState(false)
  const [isShowSys, setIsShowSys] = useState(false)

  const [uploadedFiles, setUploadedFiles] = useState<
    { id: string; name: string }[]
  >([])
  const [uploadedSystemFiles, setUploadedSystemFiles] = useState<
    { id: string; name: string }[]
  >([])
  const scrollRef = React.useRef<HTMLDivElement>(null)
  const scrollRefSys = React.useRef<HTMLDivElement>(null)
  const [parseKey, setParseKey] = useState(0) // 政策解析
  const [interpretKey, setInterpretKey] = useState(0) // 政策解读
  const [checkKey, setCheckKey] = useState(0) // 制度校验

  const handleNext = async (type: string, outline?: string) => {
    if (current < 3) {
      setCurrent(current + 1)
    }
    if (type) {
      await handleGenerationStart(type)
    }
  }
  const handleStep = (value: number) => {
    console.log('onChange:', value)
    console.log(infoMessage, 'infomessage')
    setCurrent(value)
  }
  const handleConfirm = () => {
    setIsShowMk(true)
    handleGenerationStart('政策解析')
  }
  const handlePrev = () => {
    setCurrent(current - 1)
  }

  useEffect(() => {
    if (uploadedFiles.length > 0) {
      // messageApi.open({
      //   key: 'uploading',
      //   type: 'success',
      //   content: '文件上传成功',
      //   duration: 1,
      // })
    }
  }, [uploadedFiles, messageApi])

  const beforeUpload = (file: File, type: 'policy' | 'system' = 'policy') => {
    const originalFilename = file.name.substring(0, file.name.lastIndexOf('.'))
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf('.') + 1)
      ?.toLowerCase()
    if (['pdf', 'docx'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中',
      })
      // convertFileToPDF(file).then(async (response) => {
      //   if (response['status'] && response['status'] !== 200) {
      //     messageApi.open({
      //       key: 'uploading',
      //       type: 'error',
      //       content: '文件处理异常，请稍后重试',
      //       duration: 1,
      //     })
      //   } else if ('blob' in response) {
      //     const blob = await response.blob()
      //     const pdfFile = new File([blob], `${originalFilename}.pdf`, {
      //       type: 'application/pdf',
      //     })
      uploadFile(file, VITE_POLICY_INTERPRETATION).then(async (response) => {
        if (response.id) {
          if (type === 'policy') {
            setUploadedFiles((prevFiles) => [...prevFiles, response])
          } else {
            setUploadedSystemFiles([response])
          }
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1,
          })
        } else {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件上传失败',
            duration: 1,
          })
        }
      })
      //   }
      // })
    } else {
      messageApi.error('目前仅支持.docx, .pdf类型的文件')
    }
    return false
  }

  const handleDelete = (
    fileId: string,
    type: 'policy' | 'system' = 'policy'
  ) => {
    if (type === 'policy') {
      setUploadedFiles((prevFiles) =>
        prevFiles.filter((file) => file.id !== fileId)
      )
    } else {
      setUploadedSystemFiles((prevFiles) =>
        prevFiles.filter((file) => file.id !== fileId)
      )
    }
  }
  const openModal = () => {
    setModalOpen(true)
    setUploadedSystemFiles([])
    // setSelectVerification(data)
  }

  const handleVerification = () => {
    // setModalOpen(false)
    setIsShowSys(true)
    // handleNext('制度校验')
    handleGenerationStart('制度校验')
  }

  const handleGenerationStart = async (type: string) => {
    setGenerating(true)
    setRegenerateCount((prev) => prev + 1)
    if (type === '政策解析') setParseKey((k) => k + 1)
    if (type === '政策解读') setInterpretKey((k) => k + 1)
    if (type === '制度校验') setCheckKey((k) => k + 1)
    let inputs = {}
    const fileIds = uploadedFiles.map((file) => file.id)
    const systemFileIds = uploadedSystemFiles.map((file) => file.id)
    switch (type) {
      case '政策解析':
        inputs = {
          company: company,
          files: fileIds.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x,
          })),
        }
        setInfoMessage('')
        break
      case '政策解读':
        inputs = {
          company,
          jieduzhengce: JSON.stringify([{ title: '', content: infoMessage }]),
        }
        setInterpretList({})
        setInterpretJson('')
        break
      case '制度校验':
        inputs = {
          heguizhidu: systemFileIds.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x,
          })),
          jiaoyanguizw: systemInfo,
        }
        setStep4Message('')
        break
      default:
        break
    }

    let res = ''
    try {
      await policyInterpretation(
        {
          type,
          ...inputs,
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              console.log(res, 'res')
              if (type === '政策解析') {
                setInfoMessage(res)
              } else if (type === '政策解读') {
                const cleanedData = res.replace(/^```markdown\s*|```$/g, '')
                setInterpretJson(cleanedData)
              } else if (type === '制度校验') {
                const cleanedData = res.replace(/^```markdown\s*|```$/g, '')
                setStep4Message(cleanedData)
              }
            }
            if (finished) {
              if (type === '政策解析') {
                const extractPolicyInfo = (text: string) => {
                  const regex = /监管规则\s*([\s\S]*)/
                  const match = text.match(regex)
                  return match ? match[1].trim() : ''
                }
                const extractedInfo = extractPolicyInfo(res)
                console.log(extractedInfo, 'extractedInfo')
                setSystemInfo(extractedInfo)
              }
              if (type === '政策解读') {
                setInterpretList(JSON.parse(res))
              }
              setGenerating(false)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {},
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  return (
    <div className="work-report-container">
      {contextHolder}
      <Spin tip="加载中" fullscreen spinning={generating} size="large" />
      <Typography.Title
        level={2}
        style={{
          textAlign: 'center',
          color: '#2156f3',
          margin: '30px 0 24px',
        }}
      >
        {titleOptions[current]}
      </Typography.Title>
      {/* 自定义纯文字步骤条 */}
      <div className="custom-steps-text">
        {['政策解析', '政策解读', '制度校验'].map((label, idx) => (
          <React.Fragment key={label}>
            <span
              className={current === idx ? 'step-active' : 'step-inactive'}
              onClick={() => {
                // 只允许跳转到已解锁的步骤
                if (
                  idx === 0 ||
                  (idx === 1 && infoMessage) ||
                  (idx === 2 && infoMessage)
                ) {
                  setCurrent(idx)
                }
              }}
              style={{
                cursor:
                  idx === 0 ||
                  (idx === 1 && infoMessage) ||
                  (idx === 2 && infoMessage)
                    ? 'pointer'
                    : 'not-allowed',
              }}
            >
              {label}
            </span>
            {idx < 2 && <span className="step-divider" />}
          </React.Fragment>
        ))}
      </div>
      <div className="steps-content">
        <Flex
          justify="center"
          style={{ display: current === 0 ? 'flex' : 'none' }}
        >
          <div style={{ width: '80vw' }}>
            <Layout style={{ height: 'calc(100vh - 190px)', width: '80vw' }}>
              <Layout.Sider
                width={isShowMk ? '50%' : '100%'}
                style={{ backgroundColor: '#fff', paddingRight: '20px' }}
              >
                <Card
                  style={{
                    width: isShowMk ? '100%' : '50%',
                    margin: '0 auto',
                  }}
                >
                  <div style={{ marginBottom: 16, marginTop: 10 }}>
                    <span style={{ fontWeight: 500 }}>上传政策文件</span>
                    <span style={{ color: 'red', marginLeft: 4 }}>*</span>
                    <Upload.Dragger
                      multiple={false}
                      accept=".doc,.docx,.pdf"
                      showUploadList={false}
                      beforeUpload={(file) => beforeUpload(file, 'policy')}
                      style={{ marginTop: '16px' }}
                    >
                      <div className="ant-upload-drag-icon">
                        {uploadedFiles.length > 0 ? (
                          <CheckCircleFilled />
                        ) : (
                          <InboxOutlined />
                        )}
                      </div>
                      <div className="ant-upload-hint">
                        <p>请拖拽文件到此处或点击上传文件按钮</p>
                        <span>支持docx,pdf格式</span>
                        <br />
                      </div>
                    </Upload.Dragger>

                    {/* 文件列表 */}
                    {uploadedFiles.length > 0 && (
                      <div className="file-list">
                        <Typography.Text
                          className="section-title"
                          style={{ marginTop: '24px', display: 'block' }}
                        >
                          文档列表：
                        </Typography.Text>
                        {uploadedFiles.map((x) => (
                          <p key={x.id}>
                            <Tag
                              closeIcon
                              style={{
                                marginTop: 4,
                                maxWidth: '100%',
                                whiteSpace: 'normal',
                                height: 'auto',
                                wordBreak: 'break-all',
                              }}
                              onClose={() => {
                                setUploadedFiles((prevList) =>
                                  prevList.filter((y) => y.id !== x.id)
                                )
                                return false
                              }}
                            >
                              {x.name}
                            </Tag>
                          </p>
                        ))}
                      </div>
                    )}
                  </div>
                  <Button
                    type="primary"
                    block
                    size="large"
                    style={{ marginTop: 16 }}
                    onClick={() => handleConfirm()}
                    disabled={uploadedFiles.length === 0}
                  >
                    政策解析
                  </Button>
                </Card>
              </Layout.Sider>
              {isShowMk && (
                <Layout.Content style={{ background: '#fff' }}>
                  <Card
                    ref={scrollRef}
                    style={{
                      width: '100%',
                      height: '100%',
                      // height: 'calc(100vh - 255px)',
                      overflow: 'auto',
                    }}
                  >
                    <StreamTypewriter
                      key={parseKey}
                      text={infoMessage}
                      end={!generating}
                      onchange={() => {
                        scrollRef.current?.scrollTo({
                          top: scrollRef.current.scrollHeight,
                          behavior: 'smooth',
                        })
                      }}
                    />
                  </Card>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-around',
                      marginTop: 24,
                      marginBottom: 24,
                    }}
                  >
                    <Button onClick={() => handleGenerationStart('政策解析')}>
                      重新生成
                    </Button>
                  </div>
                </Layout.Content>
              )}
            </Layout>
          </div>
        </Flex>
        <Flex
          justify="center"
          style={{ display: current === 1 ? 'flex' : 'none' }}
        >
          <div style={{ width: '80%' }}>
            <div style={{ display: 'flex', marginBottom: 20 }}>
              <Input
                placeholder="请输入企业名称，为您生成专属政策解读报告"
                value={company}
                onChange={(e) => setCompany(e.target.value)}
                allowClear
                style={{ marginRight: 20 }}
              />
              <Button
                type="primary"
                onClick={() => handleGenerationStart('政策解读')}
              >
                智能解读
              </Button>
            </div>
            {/* {generating ? (
              <Card
                style={{
                  width: '100%',
                  overflow: 'auto',
                }}
              >
                <StreamTypewriter
                  key={interpretKey}
                  text={interpretJson}
                  end={!generating}
                />
              </Card>
            ) : (
              <PolicyInterpret interpretList={interpretList} />
            )} */}
            {interpretList && Object.keys(interpretList).length > 0 ? (
              <>
                <PolicyInterpret interpretList={interpretList} />
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-around',
                    marginTop: 24,
                    marginBottom: 24,
                  }}
                >
                  <Button onClick={() => handleGenerationStart('政策解读')}>
                    重新生成
                  </Button>
                </div>
              </>
            ) : (
              <></>
            )}
          </div>
        </Flex>
        <Flex
          justify="center"
          style={{ display: current === 2 ? 'flex' : 'none' }}
        >
          <div style={{ width: '80vw' }}>
            <Layout style={{ height: 'calc(100vh - 190px)', width: '100%' }}>
              <Layout.Sider
                width={isShowSys ? '50%' : '100%'}
                style={{ backgroundColor: '#fff', paddingRight: '20px' }}
              >
                <Card
                  style={{
                    width: isShowSys ? '100%' : '50%',
                    margin: '0 auto',
                  }}
                >
                  <div style={{ marginBottom: 16, marginTop: 10 }}>
                    <span style={{ fontWeight: 500 }}>上传制度文件</span>
                    <span style={{ color: 'red', marginLeft: 4 }}>*</span>
                    <Upload.Dragger
                      multiple={false}
                      accept=".doc,.docx,.pdf"
                      maxCount={1}
                      showUploadList={false}
                      beforeUpload={(file) => beforeUpload(file, 'system')}
                      style={{ marginTop: '16px' }}
                    >
                      <div className="ant-upload-drag-icon">
                        {uploadedFiles.length > 0 ? (
                          <CheckCircleFilled />
                        ) : (
                          <InboxOutlined />
                        )}
                      </div>
                      <div className="ant-upload-hint">
                        <p>请拖拽文件到此处或点击上传文件按钮</p>
                        <span>支持docx,pdf格式</span>
                        <br />
                      </div>
                    </Upload.Dragger>

                    {/* 文件列表 */}
                    {uploadedSystemFiles.length > 0 && (
                      <div className="file-list">
                        <Typography.Text
                          className="section-title"
                          style={{ marginTop: '24px', display: 'block' }}
                        >
                          文档列表：
                        </Typography.Text>
                        {uploadedSystemFiles.map((x) => (
                          <p key={x.id}>
                            <Tag
                              closeIcon
                              style={{
                                marginTop: 4,
                                maxWidth: '100%',
                                whiteSpace: 'normal',
                                height: 'auto',
                                wordBreak: 'break-all',
                              }}
                              onClose={() => handleDelete(x.id, 'system')}
                            >
                              {x.name}
                            </Tag>
                          </p>
                        ))}
                      </div>
                    )}
                    <Button
                      type="primary"
                      block
                      size="large"
                      disabled={uploadedSystemFiles.length === 0}
                      style={{ marginTop: 16 }}
                      onClick={handleVerification}
                    >
                      智能校验
                    </Button>
                  </div>
                </Card>
              </Layout.Sider>
              {isShowSys && (
                <Layout.Content style={{ background: '#fff' }}>
                  <Card
                    ref={scrollRefSys}
                    style={{
                      width: '100%',
                      height: '100%',
                      overflow: 'auto',
                    }}
                  >
                    <StreamTypewriter
                      key={checkKey}
                      text={step4Message}
                      end={!generating}
                      onchange={() => {
                        scrollRefSys.current?.scrollTo({
                          top: scrollRefSys.current.scrollHeight,
                          behavior: 'smooth',
                        })
                      }}
                    />
                  </Card>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-around',
                      marginTop: 24,
                      marginBottom: 24,
                    }}
                  >
                    <Button onClick={() => handleGenerationStart('制度校验')}>
                      重新生成
                    </Button>
                  </div>
                </Layout.Content>
              )}
            </Layout>
          </div>
        </Flex>
        {/* 弹窗 */}
        <Modal
          title={
            <span style={{ fontWeight: 600, fontSize: 20 }}>上传管理制度</span>
          }
          open={modalOpen}
          onCancel={() => setModalOpen(false)}
          footer={null}
          width={500}
          centered
          destroyOnClose
        >
          <Upload.Dragger
            showUploadList={false}
            beforeUpload={(file) => beforeUpload(file, 'system')}
            multiple={false}
            accept=".pdf,.docx"
            style={{ margin: '32px 0' }}
          >
            <div className="ant-upload-drag-icon">
              {uploadedSystemFiles.length > 0 ? (
                <CheckCircleFilled />
              ) : (
                <InboxOutlined />
              )}
            </div>
            <div className="ant-upload-hint">
              <span>拖拽文件到此处上传</span>
              <br />
              <span style={{ fontSize: '12px', color: '#999' }}>
                或点击选择文件
              </span>
            </div>
          </Upload.Dragger>
          {uploadedSystemFiles.length > 0 && (
            <div className="file-list-contract" style={{ margin: '12px 0' }}>
              {uploadedSystemFiles.map((file) => (
                <div
                  key={file.id}
                  className="file-item"
                  style={{ display: 'flex', alignItems: 'center', gap: 8 }}
                >
                  <span>{file.name}</span>
                  <DeleteOutlined
                    onClick={() => handleDelete(file.id, 'system')}
                    style={{ cursor: 'pointer' }}
                  />
                </div>
              ))}
            </div>
          )}
          <Button
            type="primary"
            block
            size="large"
            disabled={uploadedSystemFiles.length === 0}
            style={{ marginTop: 16 }}
            onClick={handleVerification}
          >
            智能校验
          </Button>
        </Modal>
      </div>
    </div>
  )
}
export default PolicyInterpretation
