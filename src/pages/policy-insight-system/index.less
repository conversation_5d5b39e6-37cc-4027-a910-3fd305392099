.toolbar {
  display: flex;
  justify-content: center;
  margin: 0;

  .title-text {
    font-size: 18px;
    font-weight: 600;
  }
}
.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin-top: 10px;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #ddd;
  padding: 8px;
}

.markdown-content th {
  background-color: #f2f2f2;
  text-align: left;
}

.preview-content {
  max-width: 800px;
  margin: auto;
  padding: 20px;
}

.custom-scrollbar {
  max-height: 500px;
  overflow-y: auto;
}

.ant-upload-drag-icon {
  color: #1677ff;
  font-size: 48px;
}

@media (max-width: 768px) {
  .template-form,
  .preview-content {
    padding: 10px;
  }

  .toolbar .title-text {
    font-size: 16px;
  }

  .ant-upload-drag-icon {
    font-size: 24px;
  }

  .ant-upload-text,
  .ant-upload-hint {
    font-size: 14px;
  }

  .ant-btn {
    width: 100%;
  }
}

.feasibility-report-container {
  height: 100vh;
  background-color: #f0f2f5;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
}

.feasibility-report-card {
  width: 100%;
  max-width: 600px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  transition: all 0.3s ease;
  margin: 20vh auto;
  flex: 1;

  .page-title {
    margin-bottom: 16px;
    color: #1f1f1f;
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      font-size: 24px;
      color: #1890ff;
    }
  }

  .page-description {
    display: block;
    margin: 16px 0;
    font-size: 14px;
  }

  .upload-form {
    .ant-form-item {
      margin-bottom: 24px;
    }

    .ant-upload {
      width: 100%;
    }

    .file-name {
      display: block;
      margin-top: 8px;
      font-size: 14px;
    }
  }

  .ant-upload-list {
    margin-top: 8px;
  }
}

.pl-6 {
  padding-left: 6px;
}
.highlighted-messages-card {
  margin-bottom: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 0;
  .ant-card-body {
    padding: 6px 12px;
  }
}
