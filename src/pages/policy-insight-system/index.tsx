import { useCallback, useState, useEffect, useMemo, useRef } from 'react'
import StreamTypewriter from '@/component/StreamTypewriter'
import type { GetProp } from 'antd'
import remarkGfm from 'remark-gfm'
import ReactMarkdown from 'react-markdown'
import { Upload, Button, Typography, Card, Spin, Flex, Checkbox, message, Input, Layout, Form, Empty, Tabs } from 'antd'
import { DeleteOutlined, UploadOutlined } from '@ant-design/icons'
import { policyInsightSystem } from '@/api/policyInsightSystem'
import { uploadFile } from '@/api/template'
import { extractContent } from '@/utils/common'
import './index.less'

const { Title, Text } = Typography
const POLICY_INSIGHT_SYSTEM_TOKEN = import.meta.env['VITE_POLICY_INSIGHT_SYSTEM_TOKEN'] || ''

export const PolicyInsightSystem = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [uploadedFiles, setUploadedFiles] = useState<{ id: string; name: string }[]>([])
  const [checkList, setCheckList] = useState<any[]>([])
  const [isShowMk, setIsShowMk] = useState(false)
  const [generating, setGenerating] = useState<boolean>(false)
  const [loading, setLoading] = useState<boolean>(false)
  const [type1Value, setType1Value] = useState<any[] | null>([])
  const [type2Value, setType2Value] = useState<string>('')
  const [type3Value, setType3Value] = useState<string>('')
  const [markdownTable, setMarkdownTable] = useState('')
  const [policyContent, setPolicyContent] = useState<string>('')
  const scrollRef = useRef<HTMLDivElement>(null)
  const [autoScroll, setAutoScroll] = useState(true)
  useEffect(() => {
    const container = scrollRef.current
    console.log(container)
    // 当 str 变化且 autoScroll 为 true 时滚动到顶部
    if (autoScroll && container) {
      console.log('ontainer.scrollTop = 0')
      container.scrollTop = 0
    }
  }, [markdownTable, autoScroll])

  useEffect(() => {
    const container = scrollRef.current
    if (!container) return

    const handleScroll = () => {
      // 检测用户手动滚动（只要不是正好在顶部）
      if (container.scrollTop > 0) {
        setAutoScroll(false)
      }
    }

    container.addEventListener('scroll', handleScroll)

    return () => {
      container.removeEventListener('scroll', handleScroll)
    }
  }, [])

  useEffect(() => {
    if (uploadedFiles.length > 0) {
      messageApi.open({
        key: 'uploading',
        type: 'success',
        content: '文件上传成功',
        duration: 1
      })
    }
  }, [uploadedFiles, messageApi])

  const handleGenerationStart = () => {
    setAutoScroll(true)
    const fileIds = uploadedFiles.map(file => file.id)
    if (checkList.includes('4')) {
      if (fileIds.length === 0) {
        messageApi.open({
          key: 'uploading',
          type: 'error',
          content: `还未上传制度校验规则`,
          duration: 2
        })
        return
      }
    }
    handleGeneration(fileIds)
  }

  const highlightedMessages = useMemo(() => {
    return (type1Value || []).map((item, index) => (
      <Card hoverable key={index} onClick={() => window.open(item.url)} className='highlighted-messages-card'>
        <Title
          level={4}
          style={{
            fontSize: '16px',
            wordBreak: 'break-all'
          }}
        >
          <span dangerouslySetInnerHTML={{ __html: item.title }} />
        </Title>

        {item.content && (
          <div
            style={{
              marginBottom: '10px',
              color: 'rgba(0,0,0,0.65)',
              fontSize: '14px',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical'
            }}
          >
            <span
              dangerouslySetInnerHTML={{
                __html: item.content
              }}
            />
          </div>
        )}

        <Text
          type='secondary'
          style={{
            fontSize: '12px',
            marginBottom: '8px',
            display: 'block',
            color: 'rgba(0,0,0,0.45)'
          }}
        >
          <span dangerouslySetInnerHTML={{ __html: item.summary }} />
        </Text>

        <Flex justify='space-between' align='center' style={{ marginTop: '10px' }}>
          <Text
            type='secondary'
            style={{
              fontSize: '12px',
              color: 'rgba(0,0,0,0.45)'
            }}
          >
            {item.pubtimeStr}
          </Text>

          <Typography.Link href={item.url} target='_blank' style={{ fontSize: '12px' }}>
            查看原文
          </Typography.Link>
        </Flex>
      </Card>
    ))
  }, [type1Value])

  const handleGeneration = useCallback(
    async (fileIds: string[]) => {
      setGenerating(true)
      let accumulatedMessages = ''
      let allStr = ''
      setMarkdownTable('')
      setType1Value(null)
      setType2Value('')
      setType3Value('')
      const node = checkList.reduce((accumulator, currentValue) => Number(accumulator) + Number(currentValue), 0)
      try {
        await policyInsightSystem(
          {
            query: policyContent,
            node: node,
            files: checkList.includes('4')
              ? fileIds.map(x => ({
                  type: 'document',
                  transfer_method: 'local_file',
                  upload_file_id: x
                }))
              : []
          },
          {
            onMessage: (text: string | null, finished: boolean) => {
              if (text) {
                setIsShowMk(true)
                allStr += text || ''
                accumulatedMessages += (text || '').replace(/<\/?type[123]?>/g, '')
                setMarkdownTable(accumulatedMessages)
              }
              if (finished) {
                console.log(allStr)
                if (allStr.includes('<type1>')) {
                  const type1Value = extractContent(allStr, 'type1')
                  try {
                    const list = JSON.parse(type1Value)
                    setType1Value(list)
                  } catch (e) {
                    console.error('Error parsing message:', e)
                  }
                }
                if (allStr.includes('<type2>')) {
                  const type2Value = extractContent(allStr, 'type2')
                  setType2Value(type2Value)
                }
                if (allStr.includes('<type3>')) {
                  let type3Value = extractContent(allStr, 'type3')
                  if (type3Value.includes('```markdown')) {
                    type3Value = type3Value.replace(/^```markdown\s*|```$/g, '')
                  }
                  setType3Value(type3Value)
                }
              }
            },
            onError: () => {
              setGenerating(false)
            },
            onFinish: () => {
              setGenerating(false)
            }
          }
        )
      } catch (err) {
        setGenerating(false)
      }
    },
    [checkList, policyContent]
  )

  // 校验规则数据源
  const rules = [
    {
      id: 1,
      title: '运行数据收集'
    },
    {
      id: 2,
      title: '运行报告解读'
    },
    {
      id: 4,
      title: '运行制度校验'
    }
  ]

  // 处理复选框变化
  const handleCheck: GetProp<typeof Checkbox.Group, 'onChange'> = checkedValues => {
    setCheckList(checkedValues)
  }

  const beforeUpload = (file: File) => {
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)?.toLowerCase()
    if (['pdf', 'docx', 'xlsx'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中'
      })
      setLoading(true)
      uploadFile(file, POLICY_INSIGHT_SYSTEM_TOKEN)
        .then(response => {
          setLoading(false)
          if (response && response.id) {
            messageApi.open({
              key: 'uploading',
              type: 'success',
              content: '文件上传成功',
              duration: 1
            })
            setUploadedFiles([response])
            return false // 阻止自动上传
          } else {
            throw new Error('上传失败：未收到有效的响应')
          }
        })
        .catch(error => {
          setLoading(false)
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: `文件上传失败: ${error.message}`,
            duration: 2
          })
        })
    } else {
      messageApi.error('目前支持.pdf,.docx,.xlsx类型的文件，请您将文件转成这些格式后再次进行上传')
    }
    return false // 阻止自动上传
  }

  return (
    <div className='policy-insight-system' style={{ height: '100vh' }}>
      {contextHolder}
      <Spin tip='加载中' spinning={loading || generating} fullscreen size='large' />

      <Flex className='toolbar' justify='center'>
        <Typography.Text className='title-text'>政策查询、解读、制度校验</Typography.Text>
      </Flex>

      <Layout style={{ height: 'calc(100vh - 72px)' }}>
        <Layout.Sider width={isShowMk ? '600px' : '100%'} style={{ backgroundColor: '#fff' }}>
          <Card className='feasibility-report-card'>
            <Form layout='vertical' className='upload-form'>
              <Form.Item label='任务事项'>
                <Checkbox.Group
                  value={checkList}
                  options={rules.map(x => ({ label: x.title, value: x.id + '' }))}
                  defaultValue={[]}
                  onChange={handleCheck}
                />
              </Form.Item>
              <Form.Item label='内容'>
                <Input
                  placeholder='请输入您想了解的内容'
                  value={policyContent}
                  onChange={e => setPolicyContent(e.target.value)}
                />
              </Form.Item>

              {checkList.includes('4') && (
                <Form.Item label='制度校验规则' required tooltip='请上传Word格式的制度校验规则'>
                  <Upload
                    showUploadList={false}
                    beforeUpload={file => beforeUpload(file)}
                    accept='.docx, .pdf, .xlsx, .pptx, .xls, .csv, .txt'
                  >
                    <Button icon={<UploadOutlined />}>上传制度校验规则</Button>
                  </Upload>

                  <div className='ant-upload-text'>
                    {uploadedFiles.length > 0 &&
                      uploadedFiles.map(file => (
                        <div
                          key={file.id}
                          style={{ display: 'flex', alignItems: 'center' }}
                          onClick={e => e.stopPropagation()}
                        >
                          <Button type='link'>
                            {file.name}
                            <DeleteOutlined onClick={() => setUploadedFiles([])} />
                          </Button>
                        </div>
                      ))}
                  </div>
                </Form.Item>
              )}

              <Form.Item>
                <Button
                  size='large'
                  type='primary'
                  style={{ width: '100%' }}
                  disabled={checkList.length === 0 || generating || !policyContent.trim()}
                  onClick={handleGenerationStart}
                >
                  开 始
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Layout.Sider>
        {isShowMk && (
          <Layout.Content style={{ padding: 24, background: '#fff' }}>
            <Flex align='center' justify='center' style={{ height: '50px' }}>
              <Title level={3}>分析结果</Title>
            </Flex>

            <div ref={scrollRef}>
              {generating ? (
                <StreamTypewriter text={markdownTable} end={!generating} />
              ) : (
                <>
                  <Tabs
                    defaultActiveKey='1'
                    tabPosition='left'
                    style={{ height: 220 }}
                    items={[
                      {
                        key: '1',
                        label: '政策解读',
                        children: (
                          <div
                            className='scroll-container'
                            style={{ height: 'calc(100vh - 165px)', overflowY: 'auto' }}
                          >
                            {highlightedMessages.length > 0 ? highlightedMessages : <Empty description='暂无数据' />}
                          </div>
                        )
                      },
                      {
                        key: '2',
                        label: '政策内容',
                        children: (
                          <div
                            className='scroll-container'
                            style={{ height: 'calc(100vh - 165px)', overflowY: 'auto' }}
                          >
                            <ReactMarkdown className='markdown-body pl-6'>{type2Value}</ReactMarkdown>
                          </div>
                        )
                      },
                      {
                        key: '3',
                        label: '政策风险',
                        children: (
                          <div
                            className='scroll-container'
                            style={{ height: 'calc(100vh - 165px)', overflowY: 'auto' }}
                          >
                            <ReactMarkdown remarkPlugins={[remarkGfm]} className='markdown-body'>
                              {type3Value}
                            </ReactMarkdown>
                          </div>
                        )
                      }
                    ]
                      .filter(
                        x =>
                          (x.key === '1' && type1Value) ||
                          (x.key === '2' && type2Value) ||
                          (x.key === '3' && type3Value)
                      )
                      .map(x => ({
                        label: x.label,
                        key: x.key,
                        children: x.children
                      }))}
                  />
                </>
              )}
            </div>
          </Layout.Content>
        )}
      </Layout>
    </div>
  )
}

export default PolicyInsightSystem
