.content-wrapper {
  width: 100%;
  height: calc(100% - 100px);

  .template-form {
    margin: 0 auto;
    width: 40%;
    overflow-y: auto;

    .ant-upload-wrapper {
      height: 300px;
    }
  }

  .preview-panel {
    opacity: 0;
    animation: slideIn 0.3s ease-in-out forwards;
    height: 100%;
    min-height: 1px;
    width: 60%;
    margin-right: 24px;

    .preview-header {
      flex: 0 0 auto;

      .template-select {
        flex: 1;
      }

      button {
        width: 200px;
      }
    }

    .preview-content {
      background-color: #f1f1f1;
      overflow: auto;
      min-height: 0;
    }

    .markdown-body {
      display: flex;
      flex-direction: column;
      gap: 0.5em;
      overflow: auto;
      width: 100%;

      table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin: 25px 0;
        font-size: 0.9em;
        font-family: system-ui, -apple-system, sans-serif;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        overflow: hidden;
        table-layout: auto; /* 确保表格列宽度根据内容自动调整 */

        thead tr {
          background-color: #4f46e5;
          color: #ffffff;
          text-align: left;
        }

        th {
          padding: 12px 15px;
          white-space: nowrap; /* 防止表头文字换行 */
          width: auto; /* 根据内容自动调整宽度 */
        }

        td {
          padding: 12px 15px;
          word-wrap: break-word; /* 允许单元格内的长文本换行 */
        }

        tbody tr {
          border-bottom: 1px solid #dddddd;
        }

        tbody tr:nth-of-type(even) {
          background-color: #f8f8f8;
        }

        tbody tr:last-of-type {
          border-bottom: 2px solid #4f46e5;
        }

        tbody tr:hover {
          background-color: #f5f4ff;
          transition: all 0.2s ease;
        }
      }
    }
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
