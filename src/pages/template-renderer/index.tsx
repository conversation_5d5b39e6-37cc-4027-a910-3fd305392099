/** 模板生成工具 */
import {Button, Card, Flex, message, Select, Space, Spin, Typography, Upload} from 'antd'
import {useCallback, useEffect, useRef, useState} from 'react'
import {
  convertFileToPDF,
  downloadPaddingResult,
  listAllTemplates,
  structGeneration,
  templatePadding,
  uploadFile
} from '@/api/template.ts'
import {CheckCircleFilled, CopyOutlined, DownloadOutlined, InboxOutlined, LoadingOutlined} from '@ant-design/icons'
import ReactMarkdown from 'react-markdown'
import RemarkMath from 'remark-math'
import RemarkGfm from 'remark-gfm'
import RemarkBreaks from 'remark-breaks'
import {Prism as SyntaxHighlighter} from 'react-syntax-highlighter'
import {dark} from 'react-syntax-highlighter/dist/esm/styles/prism'
import {extractJSONFromString} from '@/utils/json-extractor'
import {extractContent} from '@/utils/common'
import GetKey from '@/component/getKey'
import './index.less'

const TEMPLATE_AGENT_TOKEN = import.meta.env['VITE_TEMPLATE_AGENT_TOKEN'] || ''

const typeOptions = [
  {value: 1, label: <span>人员简历信息提取</span>},
  {value: 2, label: <span>厂商产品信息提取</span>},
  {value: 3, label: <span>保险产品计划书生成</span>},
  {value: 4, label: <span>保单生成</span>}
]

export const TemplateRenderer = () => {
  const [key, setKey] = useState('')
  const [open, setOpen] = useState(false)
  const [messageApi, contextHolder] = message.useMessage()
  // FIXME 这个装填应该提交到全局状态库
  const [globalLoading, setGlobalLoading] = useState<boolean>(true)

  // 点击【开始生成】后，状态变更
  const [startSending, setStartSending] = useState<boolean>(false)

  // 生成状态
  const [generating, setGenerating] = useState<boolean>(false)
  // 生成消息
  const [messages, setMessages] = useState<string>('')
  // 生成异常
  const [error, setError] = useState<string | null>(null)
  const [generationMetadata, setGenerationMetadata] = useState<object>()
  const [selectedType, setSelectedType] = useState<number>()

  const [templates, setTemplates] = useState<object[]>([])
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>()
  const [uploadedFile, setUploadedFile] = useState<object>()

  // 用于存储完整的响应文本
  const fullContentRef = useRef<string>('')
  // 用于控制动画帧
  const animationFrameRef = useRef<number>()
  // 用于跟踪当前显示的字符位置
  const currentIndexRef = useRef<number>(0)

  useEffect(() => {
    listAllTemplates(selectedType === 3 ? '保险' : '')
      .then(response => {
        if (response.code === 200 && response.data) {
          const templates = response.data.map(item => {
            return {value: item.id, label: <span>{item.title.replace('神州新桥', '')}</span>}
          })
          setTemplates(templates)
        }
      })
      .finally(() => {
        setGlobalLoading(false)
      })
  }, [])

  useEffect(() => {
    return cleanup
  }, [])

  // 清理函数
  const cleanup = () => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current)
    }
    fullContentRef.current = ''
    currentIndexRef.current = 0
  }

  const smoothRender = useCallback(() => {
    const renderNextChunk = () => {
      if (currentIndexRef.current < fullContentRef.current.length) {
        // 每次渲染多个字符以提高性能，同时保持流畅性
        const chunkSize = 2
        const nextIndex = Math.min(currentIndexRef.current + chunkSize, fullContentRef.current.length)

        setMessages(fullContentRef.current.slice(0, nextIndex))
        currentIndexRef.current = nextIndex

        // 继续下一帧渲染
        animationFrameRef.current = requestAnimationFrame(renderNextChunk)
      }
    }

    renderNextChunk()
  }, [])

  // 处理新收到的文本
  const handleNewText = useCallback(
    (text: string) => {
      fullContentRef.current += text
      // 如果当前没有动画在进行，启动新的渲染
      if (!animationFrameRef.current) {
        smoothRender()
      }
    },
    [smoothRender]
  )

  const handleGeneration = useCallback(
    async (fileId: string, type: number) => {
      setGenerating(true)
      setError(null)
      setMessages('')
      cleanup()
      let str = ''
      try {
        await structGeneration(
          fileId,
          {
            type,
            key
          },
          {
            onMessage: (text, finished) => {
              if (text) {
                handleNewText(text)
                setMessages(prev => prev + text)
                str += text
              }
              if (finished) {
                setGenerating(false)
                const errorStr = extractContent(str, 'error')
                if (errorStr) {
                  message.error(errorStr)
                  setKey('')
                  setOpen(true)
                  return
                }
              }
            },
            onError: error => {
              setError(error.message)
              setGenerating(false)
              cleanup()
            },
            onFinish: (data: any) => {
              setGenerationMetadata(data.metadata)
            }
          }
        )
      } catch (err) {
        setError(err instanceof Error ? err.message : '生成过程中发生错误')
        setGenerating(false)
        cleanup()
      }
    },
    [handleNewText, key]
  )

  const beforeUpload = (file: File) => {
    const originalFilename = file.name.substring(0, file.name.lastIndexOf('.'))
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)
    if (['pdf', 'docx', 'xlsx'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中'
      })
      convertFileToPDF(file).then(async response => {
        if (response['status'] && response['status'] !== 200) {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件处理异常，请稍后重试',
            duration: 1
          })
        } else if ('blob' in response) {
          const blob = await response.blob()
          const pdfFile = new File([blob], `${originalFilename}.pdf`, {type: 'application/pdf'})
          uploadFile(pdfFile, TEMPLATE_AGENT_TOKEN).then(async response => {
            if (response.id) {
              setUploadedFile(response)
              messageApi.open({
                key: 'uploading',
                type: 'success',
                content: '文件上传成功',
                duration: 1
              })
            } else {
              messageApi.open({
                key: 'uploading',
                type: 'error',
                content: '文件上传失败',
                duration: 1
              })
            }
          })
        }
      })
    } else {
      messageApi.error('目前仅支持.docx，.pdf, .xlsx类型的文件，请您将文件转成这些格式后再次进行上传')
    }
  }

  const handleGenerationStart = () => {
    if (!key) {
      setOpen(true)
      return
    }
    setStartSending(true)
    setGenerationMetadata(null)
    handleGeneration(uploadedFile!.id, selectedType)
  }

  const handleTemplatePadding = () => {
    const templatePaddingData: string | null = extractJSONFromString(messages)
    if (selectedTemplateId && templatePaddingData) {
      templatePadding(selectedTemplateId, templatePaddingData).then(response => {
        if (response.code === 200) {
          const url: string = response.data.shortUrl
          if (url) {
            downloadPaddingResult(url)
          }
        }
      })
    }
  }

  const markdownComponents = {
    code({inline, className, children, ...props}) {
      const match = /language-(\w+)/.exec(className || '')
      return !inline && match ? (
        <SyntaxHighlighter
          {...props}
          className='editor custom-scrollbar'
          language={match?.[1]}
          showLineNumbers={true}
          wrapLines={true}
          style={dark}
          customStyle={{
            border: 'none',
            margin: '0'
          }}
          children={String(children).replace(/\n$/, '')}
        ></SyntaxHighlighter>
      ) : (
        <code {...props} className={className}>
          {children}
        </code>
      )
    }
  }

  const handleTableFormat = () => {
    // 创建一个隐藏的 div 来存放转换后的表格
    const tempDiv = document.createElement('div')
    tempDiv.style.position = 'absolute'
    tempDiv.style.left = '-9999px'
    document.body.appendChild(tempDiv)

    // 获取当前 markdown 内容中的表格
    const markdownContent = messages
    const tableRegex = /\|.*\|/g
    const tableLines = markdownContent.match(tableRegex)

    if (tableLines) {
      // 解析 markdown 表格并转换为 HTML 表格
      const tableData = tableLines.map(line =>
        line
          .split('|')
          .filter(cell => cell.trim())
          .map(cell => cell.trim())
      )

      // 创建 HTML 表格
      const table = document.createElement('table')
      table.style.borderCollapse = 'collapse'
      table.style.width = '100%'

      // 添加表头
      const thead = document.createElement('thead')
      const headerRow = document.createElement('tr')
      tableData[0].forEach(headerText => {
        const th = document.createElement('th')
        th.style.border = '1px solid black'
        th.style.padding = '8px'
        th.style.backgroundColor = '#f2f2f2'
        th.textContent = headerText
        headerRow.appendChild(th)
      })
      thead.appendChild(headerRow)
      table.appendChild(thead)

      // 添加表体
      const tbody = document.createElement('tbody')
      for (let i = 2; i < tableData.length; i++) {
        const row = document.createElement('tr')
        tableData[i].forEach(cellText => {
          const td = document.createElement('td')
          td.style.border = '1px solid black'
          td.style.padding = '8px'
          td.textContent = cellText
          row.appendChild(td)
        })
        tbody.appendChild(row)
      }
      table.appendChild(tbody)

      // 将表格添加到临时 div
      tempDiv.appendChild(table)

      // 选择表格内容
      const range = document.createRange()
      range.selectNodeContents(table)
      const selection = window.getSelection()
      selection.removeAllRanges()
      selection.addRange(range)

      try {
        // 复制到剪贴板
        document.execCommand('copy')
        messageApi.success('表格格式已转换，请粘贴到 Word 中使用')
      } catch (err) {
        messageApi.error('复制失败，请手动选择表格并复制')
      }

      // 清理
      selection.removeAllRanges()
      document.body.removeChild(tempDiv)
    } else {
      messageApi.warning('未检测到表格内容')
    }
  }

  return (
    <>
      {contextHolder}
      <Spin tip='加载中' spinning={globalLoading} fullscreen size='large'/>
      <GetKey open={open} onClose={setOpen} onChange={setKey}/>
      <Flex className='toolbar' justify='space-between'>
        <Typography.Text className='title-text'>模板填充工具</Typography.Text>
        <Space></Space>
      </Flex>
      <Flex className='content-wrapper' gap='large'>
        <Card className='template-form'>
          <Flex vertical gap='middle'>
            <Select placeholder='请选择生成场景' options={typeOptions} size='large' onChange={setSelectedType}/>
            <Upload.Dragger showUploadList={false} multiple={false} beforeUpload={beforeUpload}>
              <p className='ant-upload-drag-icon'>{uploadedFile ? <CheckCircleFilled/> : <InboxOutlined/>}</p>
              <p className='ant-upload-text'>{uploadedFile ? uploadedFile.name : '点击或者将文件拖拽到这里进行上传'}</p>
              <p className='ant-upload-hint'>
                {uploadedFile ? (
                  '点击或者将文件拖拽到这里重新上传'
                ) : (
                  <>
                    <p>在这里上传您的文件，让AI帮您进行解析</p>
                    <p>目前仅支持上传一个文件，支持.docx，.pdf类型</p>
                  </>
                )}
              </p>
            </Upload.Dragger>
            <Button
              size='large'
              type='primary'
              disabled={!uploadedFile || !selectedType || generating}
              onClick={handleGenerationStart}
            >
              开 始 AI 提 取
            </Button>
          </Flex>
        </Card>

        {startSending && (
          <Flex className='preview-panel' vertical gap='middle'>
            <Flex className='preview-header' justify='space-between' gap='middle'>
              <Select
                className='template-select'
                size='large'
                placeholder={
                  generationMetadata
                    ? selectedType === 2
                      ? '信息提取完毕，请划词复制表格后粘贴使用'
                      : '文件提取完毕，请选择模板'
                    : '正在提取文件信息，请不要关闭或刷新页面'
                }
                options={templates}
                disabled={!generationMetadata || selectedType === 2}
                onChange={setSelectedTemplateId}
              />
              {selectedType !== 2 ? (
                <Button
                  icon={<DownloadOutlined/>}
                  type='primary'
                  size='large'
                  disabled={!selectedTemplateId}
                  onClick={handleTemplatePadding}
                >
                  下 载
                </Button>
              ) : (
                <Button type='primary' size='large' icon={<CopyOutlined/>} onClick={handleTableFormat}>
                  复制到剪贴板
                </Button>
              )}
            </Flex>
            <Card className='preview-content custom-scrollbar'>
              {messages ? (
                <ReactMarkdown
                  className='markdown-body custom-scrollbar'
                  remarkPlugins={[[RemarkMath], RemarkGfm, RemarkBreaks]}
                  components={markdownComponents}
                >
                  {messages}
                </ReactMarkdown>
              ) : (
                <>
                  <LoadingOutlined style={{fontSize: 48, marginBottom: 24}}/>
                  <Typography.Text>正在提取文件信息，请不要关闭或刷新页面</Typography.Text>
                </>
              )}
            </Card>
          </Flex>
        )}
      </Flex>
    </>
  )
}

export default TemplateRenderer
