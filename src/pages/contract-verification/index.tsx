import { useCallback, useState, useEffect } from 'react'
import { Upload, Button, <PERSON>po<PERSON>, Card, Spin, Flex, message } from 'antd'
import { CheckCircleFilled, InboxOutlined, LoadingOutlined } from '@ant-design/icons'
import { calibrationContract } from '@/api/contract'
import { uploadFile } from '@/api/template'
import ReactMarkdown from 'react-markdown'
import RemarkBreaks from 'remark-breaks'
import RemarkMath from 'remark-math'
import RemarkGfm from 'remark-gfm'
import './index.less'

const CONTRACT_AGENT_TOKEN = import.meta.env['VITE_CONTRACT_AGENT_TOKEN'] || "";
export const ContractVerification = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [uploadedFile, setUploadedFile] = useState<{ id: string; name: string }>()
  const [startSending, setStartSending] = useState<boolean>(false)
  const [generating, setGenerating] = useState<boolean>(false)
  const [messages, setMessages] = useState<string>('')

  useEffect(() => {
    if (uploadedFile) {
      messageApi.open({
        key: 'uploading',
        type: 'success',
        content: '文件上传成功',
        duration: 1
      })
    }
  }, [uploadedFile, messageApi])

  const beforeUpload = (file: File) => {
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)?.toLowerCase()
    if (['pdf', 'docx', 'xlsx', 'pptx', 'xls', 'csv', 'txt'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中'
      })

      uploadFile(file, CONTRACT_AGENT_TOKEN).then(async response => {
        if (response.id) {
          setUploadedFile(response)
        } else {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件上传失败',
            duration: 1
          })
        }
      })
    } else {
      messageApi.error(
        '目前仅支持.docx，.pdf, .xlsx, .pptx, .xls, .csv, .txt类型的文件，请您将文件转成这些格式后再次进行上传'
      )
    }
  }

  const handleGenerationStart = () => {
    setStartSending(true)
    handleGeneration(uploadedFile?.id || '')
  }

  const handleGeneration = useCallback(async (fileId: string) => {
    setGenerating(true)
    setMessages('')

    try {
      await calibrationContract(
        {
          id: fileId
        },
        {
          onMessage: (text, finished) => {
            if (text) {
              setMessages(prev => prev + text)
            }
            if (finished) {
              setGenerating(false)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }, [])

  return (
    <>
      {contextHolder}
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />

      <Flex className='toolbar' justify='center'>
        <Typography.Text className='title-text'>合同智能校验工具</Typography.Text>
      </Flex>

      <Card className='template-form'>
        <Flex vertical gap='middle'>
          <Upload.Dragger showUploadList={false} multiple={false} beforeUpload={beforeUpload}>
            <div className='ant-upload-drag-icon'>{uploadedFile ? <CheckCircleFilled /> : <InboxOutlined />}</div>
            <div className='ant-upload-text'>
              {uploadedFile ? uploadedFile.name : '点击或者将文件拖拽到这里进行上传'}
            </div>
            <div className='ant-upload-hint'>
              {uploadedFile ? '点击或者将文件拖拽到这里重新上传' : <span>在这里上传您的文件，让AI帮您进行解析</span>}
            </div>
          </Upload.Dragger>
          <Button size='large' type='primary' disabled={!uploadedFile || generating} onClick={handleGenerationStart}>
            开 始 校 验
          </Button>
        </Flex>
      </Card>

      {startSending && (
        <Flex vertical gap='middle'>
          <Card className='preview-content custom-scrollbar'>
            {messages ? (
              <ReactMarkdown
                className='markdown-body'
                remarkPlugins={[[RemarkMath], RemarkGfm, RemarkBreaks]}
                components={{
                  td: ({ node, ...props }) => (
                    <td
                      {...props}
                      style={{
                        fontWeight:
                          typeof props.children === 'string' &&
                          (props.children.includes('否') || props.children.includes('不适用'))
                            ? 'bold'
                            : 'normal',
                        color:
                          typeof props.children === 'string' &&
                          (props.children.includes('否') || props.children.includes('不适用'))
                            ? '#fb5d5d'
                            : 'inherit'
                      }}
                    />
                  )
                }}
              >
                {messages}
              </ReactMarkdown>
            ) : (
              <>
                <LoadingOutlined style={{ fontSize: 48, marginBottom: 24 }} />
                <Typography.Text>正在提取文件信息，请不要关闭或刷新页面</Typography.Text>
              </>
            )}
          </Card>
        </Flex>
      )}
    </>
  )
}

export default ContractVerification
