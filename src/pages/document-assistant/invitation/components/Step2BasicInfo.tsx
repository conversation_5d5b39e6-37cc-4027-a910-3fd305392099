import React from 'react'
import { Input, Button, Typography, Space, Tooltip, Tag } from 'antd'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons'
import { FireFilled } from '@ant-design/icons'

const fields = [
  {
    key: 'Reason_Invitation',
    label: '邀请缘由',
    placeholder: '例：婚礼邀请',
  },
  {
    key: 'Invited_person',
    label: '邀请对象',
    placeholder: '例：余老师',
  },
  {
    key: 'inviting_party',
    label: '邀请方',
    placeholder: '例：陈某某&林某某',
  },
  {
    key: 'time',
    label: '时间',
    placeholder: '例：2025年3月28日',
  },
  {
    key: 'place',
    label: '地点',
    placeholder: '例：凯宾斯基酒店1F大礼堂',
  },
  {
    key: 'precise_arrangement',
    label: '具体安排',
    placeholder:
      '例：-18:00 宾客签到 & 迎宾酒会-18:30 婚礼仪式正式开始-19:30 晚宴及互动环节-21:00 自由交流与合影留言。',
  },
]

interface Step2BasicInfoProps {
  value: Record<string, string>
  onChange: (v: Record<string, string>) => void
  onRemove?: (key: string) => void
  onAIClick?: (key: keyof Step2BasicInfoProps['value'], label: string) => void
}

const Step2BasicInfo: React.FC<Step2BasicInfoProps> = ({
  value,
  onChange,
  onRemove,
  onAIClick,
}) => {
  const handleInput = (key: string, val: string) => {
    onChange({ ...value, [key]: val })
  }

  // 需要AI帮写的字段
  const aiFields = ['Reason_Invitation', 'precise_arrangement']
  // 用Input的字段
  const inputFields = ['Invited_person', 'inviting_party', 'time', 'place']

  return (
    <div>
      {fields.map((f) => (
        <div
          key={f.key}
          style={{
            marginBottom: 20,
            borderRadius: 8,
            padding: 12,
          }}
        >
          <div
            style={{ display: 'flex', alignItems: 'center', marginBottom: 6 }}
          >
            <span style={{ fontWeight: 500 }}>{f.label}</span>
            <span style={{ color: 'red', marginLeft: 4 }}>*</span>
            <Space size={8} style={{ marginLeft: 'auto' }}>
              {aiFields.includes(f.key) && (
                <Tooltip title="AI帮写">
                  <Button
                    type="link"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() =>
                      onAIClick?.(
                        f.key as keyof Step2BasicInfoProps['value'],
                        f.label
                      )
                    }
                  >
                    AI帮写
                  </Button>
                </Tooltip>
              )}
              <Tooltip title="删除">
                <Button
                  type="link"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={() => onRemove?.(f.key)}
                />
              </Tooltip>
            </Space>
          </div>

          {/* 输入框类型 */}
          {inputFields.includes(f.key) ? (
            <Input
              placeholder={f.placeholder}
              value={value[f.key] || ''}
              onChange={(e) => handleInput(f.key, e.target.value)}
              maxLength={50}
              showCount
            />
          ) : (
            <Input.TextArea
              placeholder={f.placeholder}
              value={value[f.key] || ''}
              onChange={(e) => handleInput(f.key, e.target.value)}
              maxLength={800}
              rows={3}
              showCount
            />
          )}
        </div>
      ))}
    </div>
  )
}

export default Step2BasicInfo
