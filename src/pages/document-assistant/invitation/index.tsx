import React, { useState, useEffect } from 'react'
import {
  Steps,
  Button,
  Card,
  Input,
  Typography,
  Flex,
  Tag,
  Radio,
  message,
  Spin,
} from 'antd'
import {
  CloseOutlined,
  ArrowRightOutlined,
  FireFilled,
} from '@ant-design/icons'
import './index.less'
import { invitation } from '@/api/invitation'
import Step2BasicInfo from './components/Step2BasicInfo'
import OutlineEditor from '../components/OutlineEditor'
import Step4Report from './components/Step4Report'
import { extractContent } from '@/utils/common'
import OptimizationOptions from '../components/OptimizationOptions'

const dependency = import.meta.env['VITE_DEPENDENCY'] || ''
const { Step } = Steps

const recommendedOptions = ['邀请函', '2022级xxx大学毕业典礼邀请函']
const invitationTypes: any[] = [
  { label: '对公', value: '对公' },
  { label: '对私', value: '对私' },
]

export const Invitation: React.FC = () => {
  const [current, setCurrent] = useState(0)
  const [title, setTitle] = useState('')
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState<boolean>(false)
  const [infoMessage, setInfoMessage] = useState('')
  const [step3Message, setStep3Message] = useState('')
  const [step4Message, setStep4Message] = useState('')
  const [invitationType, setInvitationType] = useState('对公')
    const [baseUrl, setBaseUrl] = useState<string>('')

  const [step2Form, setStep2Form] = useState({
    Reason_Invitation: '',
    Invited_person: '',
    inviting_party: '',
    time: '',
    place: '',
    precise_arrangement: '',
  })
  const [outlineContent, setOutlineContent] = useState('')

  const handleNext = async (type: string, outline?: string) => {
    if (current < 2) {
      setCurrent(current + 1)
    }
    console.log(outline, 'outline')
    if (type) {
      await handleGenerationStart(type, undefined, undefined, outline)
    }
  }
  const handlePrev = () => {
    setCurrent(current - 1)
  }

  // AI帮写
  const handleAIClick = (key: keyof typeof step2Form, label: string) => {
    let params = {
      information_type: label,
      query: step2Form[key],
    }
    handleGenerationStart('生成信息', params, key)
  }
  const handleRemove = (key: string) => {
    setStep2Form({ ...step2Form, [key]: '' })
  }
  const handleGenerationStart = async (
    type: string,
    params?: any,
    aiKey?: keyof typeof step2Form,
    outline?: string
  ) => {
    setGenerating(true)
    let inputs = {}
    switch (type) {
      case '生成信息':
        inputs = {
          Invitation_Letter_category: invitationType,
          ...params,
        }
        break
      case '生成大纲':
        inputs = {
          Invitation_Letter_category: invitationType,
          Reason_Invitation: step2Form.Reason_Invitation,
          Invited_person: step2Form.Invited_person,
          inviting_party: step2Form.inviting_party,
          time: step2Form.time,
          place: step2Form.place,
          precise_arrangement: step2Form.precise_arrangement,
        }
        setOutlineContent('')
        break
      case '生成报告':
        inputs = {
          Invitation_Letter_category: invitationType,
          Reason_Invitation: step2Form.Reason_Invitation,
          Invited_person: step2Form.Invited_person,
          inviting_party: step2Form.inviting_party,
          time: step2Form.time,
          place: step2Form.place,
          precise_arrangement: step2Form.precise_arrangement,
        }
        setStep4Message('')
        break
      case '生成文件':
        inputs = {
          query: step4Message,
        }
        break
      default:
        break
    }
    console.log(inputs, 'inputs')
    let res = ''
    try {
      await invitation(
        {
          type,
          title,
          ...inputs,
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              console.log(res, 'res')
              if (type === '生成信息' && aiKey) {
                setStep2Form((prev) => ({ ...prev, [aiKey]: res }))
              } else if (type === '生成大纲') {
                const cleanedData = res.replace(/^```markdown\s*|```$/g, '')
                setStep3Message(cleanedData)
                setOutlineContent(cleanedData)
              } else if (type === '生成报告') {
                // setFileQuery(res)
                const cleanedData = res.replace(/^```markdown\s*|```$/g, '')
                setStep4Message(cleanedData)
              }
            }
            if (finished) {
              if (type === '生成文件') {
                const errorStr = extractContent(res, 'error')
                if (errorStr) {
                  messageApi.error(errorStr)
                  return
                }
                // 提取()中的内容
                const parenthesesContent = res.match(/\((.*?)\)/)
                const parenthesesResult = parenthesesContent
                  ? parenthesesContent[1]
                  : null

                // 提取[]中的内容
                const squareBracketsContent = res.match(/\[(.*?)\]/)
                const squareBracketsResult = squareBracketsContent
                  ? squareBracketsContent[1]
                  : null
                console.log(parenthesesResult, 'parenthesesResult')
                console.log(squareBracketsResult, 'squareBracketsResult')
                const link = document.createElement('a')
                link.href = parenthesesResult
                link.download = `邀请函${squareBracketsResult}`
                document.body.appendChild(link)
                link.click()
                link.remove()
              }

              setGenerating(false)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {},
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }
  useEffect(() => {
      if (process.env.NODE_ENV === 'development') {
        setBaseUrl(window.location.origin + '/#')
      } else if (process.env.NODE_ENV === 'production') {
        setBaseUrl(window.location.origin + '/toolbox/#')
      }
    }, [])
  const handleJump = async (type: string) => {
    console.log(type, '优化类型')
    setGenerating(true)
    let res = ''
    try {
      await invitation(
        {
          title,
          type: '生成文件',
          query: step4Message,
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
              console.log(res, 'res')
              if (type === '格式校验') {
                window.open(
                  `${baseUrl}/document-format-verification?fileParams=${encodeURIComponent(
                    res
                  )}`,
                  '_blank'
                )
              } else if (type === '敏感词稽查') {
                window.open(
                  `${baseUrl}/document-sensitive-inspection?fileParams=${encodeURIComponent(
                    res
                  )}`,
                  '_blank'
                )
              } else if (type === '错别字校验') {
                window.open(
                  `${baseUrl}/document-verification?fileParams=${encodeURIComponent(
                    res
                  )}`,
                  '_blank'
                )
              }

              setGenerating(false)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {},
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }
  const optimizationOptionsData = [
    {
      label: '格式校验',
      description: '按照标准公文格式矫正您的公文',
      type: '格式校验',
    },
    {
      label: '敏感词稽查',
      description:
        '识别并处理可能存在的政治性、政策性、保密性、规范性或社会敏感性词汇',
      type: '敏感词稽查',
    },
    {
      label: '错别字校验',
      description:
        '拆解长难句，进行语法结构分析，逻辑矛盾检测，识别语病，错字稽查',
      type: '错别字校验',
    },
  ]

  return (
    <div className="work-report-container">
      {contextHolder}
      <Spin tip="加载中" spinning={generating} fullscreen size="large" />
      <Typography.Title
        level={2}
        style={{
          textAlign: 'center',
          color: '#2156f3',
          margin: '30px 0 24px',
          paddingBottom: 20,
        }}
      >
        邀请函生成
      </Typography.Title>
      <Steps current={current} className="custom-steps">
        <Step title={<span>step 1 输入类别与标题</span>} />
        <Step title={<span>step 2 输入信息</span>} />
        <Step title={<span>step 3 生成邀请函</span>} />
      </Steps>
      <div className="steps-content">
        {current === 0 && (
          <Flex justify="center">
            <Card style={{ width: '40%' }}>
              <Typography.Title level={4}>邀请函生成</Typography.Title>
              <div style={{ marginBottom: 16, marginTop: 10 }}>
                <span style={{ fontWeight: 500 }}>邀请函类别</span>
                <span style={{ color: 'red', marginLeft: 4 }}>*</span>
                <div
                  className="report-type"
                  style={{ display: 'flex', marginTop: 8 }}
                >
                  {invitationTypes.map((r) => (
                    <span
                      key={r.value}
                      className={`report-type-item${
                        invitationType === r.value
                          ? ' report-type-item-active'
                          : ''
                      }`}
                      onClick={() => setInvitationType(r.value)}
                    >
                      {r.label}
                    </span>
                  ))}
                </div>
              </div>
              <div style={{ marginBottom: 16, marginTop: 10 }}>
                <span style={{ fontWeight: 500 }}>邀请函标题</span>
                <span style={{ color: 'red', marginLeft: 4 }}>*</span>
                <Input
                  placeholder="（邀请事由）+邀请函"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  maxLength={50}
                  // suffix={<ArrowRightOutlined />}
                  showCount
                  allowClear
                  style={{ marginTop: 8 }}
                />
              </div>
              <div style={{ marginBottom: 16 }}>
                {recommendedOptions.map((t) => (
                  <Tag
                    key={t}
                    color="#ff523b"
                    icon={<FireFilled />}
                    style={{ marginBottom: 8, cursor: 'pointer' }}
                    onClick={() => setTitle(t)}
                  >
                    {t}
                  </Tag>
                ))}
              </div>
              <Button
                type="primary"
                block
                size="large"
                style={{ marginTop: 16 }}
                onClick={() => handleNext('')}
                disabled={!title}
              >
                下一步
              </Button>
            </Card>
          </Flex>
        )}
        {current === 1 && (
          <Flex justify="center">
            <Card style={{ width: '40%' }}>
              <Typography.Title level={4}>邀请函生成</Typography.Title>
              <Step2BasicInfo
                value={step2Form}
                onChange={(v) => setStep2Form({ ...step2Form, ...v })}
                onAIClick={handleAIClick}
                onRemove={handleRemove}
              />
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginTop: 24,
                }}
              >
                <Button onClick={handlePrev}>返回上一步</Button>
                <Button
                  type="primary"
                  onClick={() => handleNext('生成报告')}
                  disabled={
                    !step2Form.Reason_Invitation ||
                    !step2Form.Invited_person ||
                    !step2Form.inviting_party ||
                    !step2Form.time ||
                    !step2Form.place ||
                    !step2Form.precise_arrangement
                  }
                >
                  生成全文
                </Button>
              </div>
            </Card>
          </Flex>
        )}
        {/* {current === 2 && (
          <Flex justify="center">
            <Card style={{ width: 480 }}>
              <OutlineEditor
                outlineInfo={outlineContent}
                onChange={(v) =>
                  setOutlineContent(
                    typeof v === 'string' ? v : v ? String(v) : ''
                  )
                }
              />
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-around',
                  marginTop: 24,
                }}
              >
                <Button
                  onClick={() =>
                    handleGenerationStart(
                      '生成大纲',
                      undefined,
                      undefined,
                      undefined
                    )
                  }
                >
                  换个大纲
                </Button>
                <Button onClick={handlePrev}>返回上一步</Button>
                <Button
                  type="primary"
                  onClick={() => handleNext('生成报告', outlineContent)}
                >
                  撰写报告
                </Button>
              </div>
            </Card>
          </Flex>
        )} */}
        {current === 2 && (
          <Flex justify="center">
            <Card style={{ width: '80vw' }}>
              <div style={{ display: 'flex', gap: 24 }}>
                <Step4Report
                  reportText={step4Message}
                  baseInfo={{
                    Reason_Invitation: step2Form.Reason_Invitation,
                    Invited_person: step2Form.Invited_person,
                    inviting_party: step2Form.inviting_party,
                    time: step2Form.time,
                    place: step2Form.place,
                    precise_arrangement: step2Form.precise_arrangement,
                    title: title,
                    invitationType: invitationType,
                  }}
                  outlineInfo={step3Message}
                />
                  {dependency !== 'shanghuawei' && (
                  <OptimizationOptions options={optimizationOptionsData} onSelect={handleJump} />
                )}
              </div>

              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-around',
                  marginTop: 24,
                }}
              >
                <Button onClick={handlePrev}>返回上一步</Button>
                <Button
                  onClick={() =>
                    handleGenerationStart('生成报告', undefined, undefined, '')
                  }
                >
                  重新生成
                </Button>
                <Button type="primary" onClick={() => handleNext('生成文件')}>
                  下载文件
                </Button>
              </div>
            </Card>
          </Flex>
        )}
      </div>
    </div>
  )
}
export default Invitation
