.document-assistant-container {
  text-align: center;
  margin-top: 60px;
  .title {
    color: #2156f3;
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 48px;
  }
  .ant-row {
    margin: 0px 32px!important;
  }
  
  .doc-card {
    min-height: 140px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: box-shadow 0.2s;
    border-radius: 12px;
    .icon {
      margin-bottom: 16px;
    }
    
    .card-title {
      font-size: 18px;
      font-weight: 500;
      margin-top: 8px;
    }
  }
}



