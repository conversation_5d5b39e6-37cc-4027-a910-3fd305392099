import React, { useState, useEffect } from 'react'
import { Card, Row, Col } from 'antd'
import {
  MessageOutlined,
  FileDoneOutlined,
  FileTextOutlined,
  FileSearchOutlined,
  MailOutlined,
  FileAddOutlined,
  FileExclamationOutlined,
  FileProtectOutlined,
} from '@ant-design/icons'
import './index.less'
import { useNavigate } from 'react-router-dom'

const cardData = [
  {
    title: '工作报告',
    icon: <MessageOutlined style={{ color: '#FFB74D', fontSize: 32 }} />,
    route: '/work-report',
  },
  {
    title: '心得体会',
    icon: <FileDoneOutlined style={{ color: '#81C784', fontSize: 32 }} />,
    route: '/experience',
  },
  {
    title: '通知',
    icon: <FileTextOutlined style={{ color: '#64B5F6', fontSize: 32 }} />,
    route: '/notice',
  },
  {
    title: '讲话稿',
    icon: <FileSearchOutlined style={{ color: '#BA68C8', fontSize: 32 }} />,
    route: '/speaking-script',
  },
  {
    title: '邀请函',
    icon: <MailOutlined style={{ color: '#64B5F6', fontSize: 32 }} />,
    route: '/invitation',
  },
  {
    title: '请示',
    icon: (
      <FileExclamationOutlined style={{ color: '#BA68C8', fontSize: 32 }} />
    ),
    route: '/request-report',
  },
  {
    title: '公告',
    icon: <FileAddOutlined style={{ color: '#FFB74D', fontSize: 32 }} />,
    route: '/announcement',
  },
  {
    title: '邮件',
    icon: <FileProtectOutlined style={{ color: '#81C784', fontSize: 32 }} />,
    route: '/email',
  },
]

const DocumentAssistant: React.FC = () => {
  const navigate = useNavigate()
  const [baseUrl, setBaseUrl] = useState<string>('')
  // 获取当前环境
  // const environment = process.env.NODE_ENV // 'development', 'production' 或 'test'

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      setBaseUrl(window.location.origin + '/#')
    } else if (process.env.NODE_ENV === 'production') {
      setBaseUrl(window.location.origin + '/toolbox/#')
    }
  }, [])
  console.log(baseUrl, 'baseUrl')
  return (
    <div className="document-assistant-container">
      <h1 className="title">公文助手2.0</h1>
      <Row gutter={[32, 32]} justify="center">
        {cardData.map((item) => (
          <Col key={item.title} xs={24} sm={12} md={6}>
            <a
              href={`${baseUrl}${item.route}`}
              target="_blank"
              rel="noopener noreferrer"
              style={{ textDecoration: 'none' }}
            >
              <Card
                className="doc-card"
                hoverable
                style={{ cursor: 'pointer' }}
              >
                <div className="icon">{item.icon}</div>
                <div className="card-title">{item.title}</div>
              </Card>
            </a>
          </Col>
        ))}
      </Row>
    </div>
  )
}

export default DocumentAssistant
