import React from 'react'
import { Input, Button, Typography, Space, Tooltip } from 'antd'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons'

const fields = [
  {
    key: 'summary',
    label: '工作总结',
    placeholder: '对主要工作内容的回顾与总结',
  },
  {
    key: 'plan',
    label: '工作计划',
    placeholder: '之后工作从哪几个方面展开',
  },
  {
    key: 'feedback',
    label: '问题反思',
    placeholder: '对过去经验教训的总结',
  },
  {
    key: 'background',
    label: '报告背景',
    placeholder: '此项工作展开时的背景或想法思路',
  },
]

interface Step2BasicInfoProps {
  value: Record<string, string>
  onChange: (v: Record<string, string>) => void
  onRemove?: (key: string) => void
  onAIClick?: (key: keyof Step2BasicInfoProps['value'], label: string) => void
}

const Step2BasicInfo: React.FC<Step2BasicInfoProps> = ({
  value,
  onChange,
  onRemove,
  onAIClick,
}) => {
  const handleInput = (key: string, val: string) => {
    onChange({ ...value, [key]: val })
  }
  return (
    <div>
      {fields.map((f) => (
        <div
          key={f.key}
          style={{
            marginBottom: 20,
            borderRadius: 8,
            padding: 12,
          }}
        >
          <div
            style={{ display: 'flex', alignItems: 'center', marginBottom: 6 }}
          >
            <span style={{ fontWeight: 500 }}>{f.label}</span>
            <span style={{ color: 'red', marginLeft: 4 }}>*</span>
            <Space size={8} style={{ marginLeft: 'auto' }}>
              <Tooltip title="AI帮写">
                <Button
                  type="link"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() =>
                    onAIClick?.(
                      f.key as keyof Step2BasicInfoProps['value'],
                      f.label
                    )
                  }
                >
                  AI帮写
                </Button>
              </Tooltip>
              <Tooltip title="删除">
                <Button
                  type="link"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={() => onRemove?.(f.key)}
                />
              </Tooltip>
            </Space>
          </div>
          <Input.TextArea
            placeholder={f.placeholder}
            value={value[f.key] || ''}
            onChange={(e) => handleInput(f.key, e.target.value)}
            maxLength={800}
            rows={3}
            showCount
          />
        </div>
      ))}
    </div>
  )
}

export default Step2BasicInfo
