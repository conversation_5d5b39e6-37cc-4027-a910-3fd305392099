import React from 'react'
import { Input, Button, Typography, Space, Tooltip, Tag } from 'antd'
import { EditOutlined, DeleteOutlined, FireFilled } from '@ant-design/icons'

interface Step2BasicInfoProps {
  value: Record<string, string>
  onChange: (v: Record<string, string>) => void
  onRemove?: (key: string) => void
  onAIClick?: (key: keyof Step2BasicInfoProps['value'], label: string) => void
  fields: {
    key: string
    label: string
    placeholder?: string
    recommendedOptions?: string[]
  }[]
}

const Step2BasicInfo: React.FC<Step2BasicInfoProps> = ({
  value,
  onChange,
  onRemove,
  onAIClick,
  fields,
}) => {
  const handleInput = (key: string, val: string) => {
    onChange({ ...value, [key]: val })
  }

  // 需要AI帮写的字段（可根据实际业务调整）
  const aiFields = [
    'Notice_Content',
    'Reason_notification',
    'requirement_performing',
    'peroration',
    'Reason_forwarding',
    'Forwarded_content',
    'Approval_opinion',
    'Instruction_content',
    'job_content',
    'Work_arrangement',
  ]
  // 用Input的字段（短文本）
  const inputFields = ['Main_sending_mechanism', 'document_issuing_agency']

  return (
    <div>
      {fields.map((f) => (
        <div
          key={f.key}
          style={{
            marginBottom: 20,
            borderRadius: 8,
            padding: 12,
          }}
        >
          <div
            style={{ display: 'flex', alignItems: 'center', marginBottom: 6 }}
          >
            <span style={{ fontWeight: 500 }}>{f.label}</span>
            <span style={{ color: 'red', marginLeft: 4 }}>*</span>
            <Space size={8} style={{ marginLeft: 'auto' }}>
              {aiFields.includes(f.key) && (
                <Tooltip title="AI帮写">
                  <Button
                    type="link"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() =>
                      onAIClick?.(
                        f.key as keyof Step2BasicInfoProps['value'],
                        f.label
                      )
                    }
                  >
                    AI帮写
                  </Button>
                </Tooltip>
              )}
              <Tooltip title="删除">
                <Button
                  type="link"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={() => onRemove?.(f.key)}
                />
              </Tooltip>
            </Space>
          </div>
          {inputFields.includes(f.key) ? (
            <Input
              placeholder={f.placeholder}
              value={value[f.key] || ''}
              onChange={(e) => handleInput(f.key, e.target.value)}
              maxLength={50}
              showCount
            />
          ) : (
            <Input.TextArea
              placeholder={f.placeholder}
              value={value[f.key] || ''}
              onChange={(e) => handleInput(f.key, e.target.value)}
              maxLength={800}
              rows={3}
              showCount
            />
          )}
          {/* 推荐项（如有） */}
          {f.recommendedOptions && (
            <div style={{ marginBottom: 8 }}>
              {f.recommendedOptions.map((t) => (
                <Tag
                  key={t}
                  color="#ff523b"
                  icon={<FireFilled />}
                  style={{ cursor: 'pointer', marginTop: 8 }}
                  onClick={() => handleInput(f.key, t)}
                >
                  {t}
                </Tag>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

export default Step2BasicInfo
