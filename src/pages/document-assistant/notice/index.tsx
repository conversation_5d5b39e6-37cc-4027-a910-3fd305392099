import React, { useState, useEffect } from 'react'
import {
  Steps,
  Button,
  Card,
  Input,
  Typography,
  Flex,
  Tag,
  Radio,
  message,
  Spin,
  Upload,
} from 'antd'
import {
  CloseOutlined,
  ArrowRightOutlined,
  FireFilled,
  CheckCircleFilled,
  InboxOutlined,
  DeleteOutlined,
} from '@ant-design/icons'
import './index.less'
import { notice } from '@/api/notice'
import Step2BasicInfo from './components/Step2BasicInfo'
import OutlineEditor from '../components/OutlineEditor'
import { uploadFile, convertFileToPDF } from '@/api/template'
import Step4Report from './components/Step4Report'
import { extractContent } from '@/utils/common'
import OptimizationOptions from '../components/OptimizationOptions'

const { Step } = Steps
const VITE_NOTICE = import.meta.env['VITE_NOTICE'] || ''
const dependency = import.meta.env['VITE_DEPENDENCY'] || ''

const recommendedOptions = [
  '科学技术部直属事业单位2025年度公开招聘统一笔试公告',
  '××市人民政府关于延长机动车限行政策的公告',
  '××省防汛抗旱指挥部启动防汛Ⅰ级应急响应的公告',
]
type NoticeType = '发布类' | '转发类' | '批转类' | '指示类' | '事务类'
const noticeTypes: { label: NoticeType; value: NoticeType }[] = [
  { label: '发布类', value: '发布类' },
  { label: '转发类', value: '转发类' },
  { label: '批转类', value: '批转类' },
  { label: '指示类', value: '指示类' },
  { label: '事务类', value: '事务类' },
]

interface RecommendedOption {
  title: string
  recommendedOptions: string[]
}

const recommended: Record<NoticeType, RecommendedOption> = {
  发布类: {
    title: '（单位/部门名称）+关于印发+（文件名称）+的通知',
    recommendedOptions: [
      'xx院关于印发“加快构建科技金融体制 有力支撑高水平 科技自立自强的若干政策举措”的通知',
      '省政府关于成立省数据集团有限公司的通知',
    ],
  },
  转发类: {
    title: '（单位/部门名称）+关于转发+（文件名称）+的通知',
    recommendedOptions: ['XX院关于印发  《空气质量持续改善行动计划》的通知'],
  },
  批转类: {
    title: '（单位/部门名称）+关于批转+（文件名称）+的通知',
    recommendedOptions: [
      'XX院关于批转全国“十二五”期间年森林采伐限额审核意见的通知',
    ],
  },
  指示类: {
    title: '（单位/部门名称）+关于+（工作指示）+的通知',
    recommendedOptions: [
      'XX市人民政府关于加强高温作业及高温天气作业劳动保护 切实防范职业性中暑事件的通知',
    ],
  },
  事务类: {
    title: '（单位/部门名称）+关于做好+（工作名称）+的通知',
    recommendedOptions: ['XX省政府办公厅关于进一步做好国家助学贷款工作的通知'],
  },
}

// 各通知类别对应的表单项配置
const noticeTypeFields: Record<
  NoticeType,
  { key: string; label: string; placeholder?: string }[]
> = {
  发布类: [
    {
      key: 'Notice_Content',
      label: '通知内容',
      placeholder:
        '是通知的核心内容。例:通过建立科技金融统筹推进机制，加强部门协调和政策联动，形成多元化、多层次、多渠道的科技投入格局。',
    },
    {
      key: 'Reason_notification',
      label: '通知缘由',
      placeholder:
        '是发布通知的原因或背景。例:为深入贯彻党的二十大和二十届三中全会精神，落实全国科技大会和中央金融工作会议部署，加快构建与科技创新相适应的科技金融体制。',
    },
    {
      key: 'requirement_performing',
      label: '执行要求',
      placeholder:
        '是接收者需要采取的具体行动或遵守的规定。例:1.加强领导，提高意识;2.周密部署，排除隐患;3.强化纪律，严厉问责。',
    },
    {
      key: 'peroration',
      label: '结语',
      placeholder:
        '通常使用“特此通知”“请遵照执行”等固定语句，来总结通知内容，表达期望或强调重要性。',
    },
    {
      key: 'Main_sending_mechanism',
      label: '主送机关',
      placeholder: '例：各省、自治区、直辖市及计划单列市科技厅（委、局）',
    },
    {
      key: 'document_issuing_agency',
      label: '发文机关',
      placeholder: '例：科技部',
    },
  ],
  转发类: [
    {
      key: 'requirement_performing',
      label: '执行要求',
      placeholder:
        '是接收者需要采取的具体行动或遵守的规定。例：一、高度重视。二、认真学习。三、参考使用文件内容。',
    },
    {
      key: 'Main_sending_mechanism',
      label: '主送机关',
      placeholder: '例：各省、自治区、直辖市人民政府，国务院各部委、各直属机构',
    },
    {
      key: 'document_issuing_agency',
      label: '发文机关',
      placeholder: '例：国务院',
    },
    {
      key: 'Reason_forwarding',
      label: '转发缘由',
      placeholder:
        '是转发通知的原因或背景。例:为深入贯彻落实党中央、国务院关于打好污染防治攻坚战的决策部署，持续深入打好蓝天保卫战。',
    },
    {
      key: 'Forwarded_content',
      label: '转发内容',
      placeholder:
        '例:行动计划从优化产业结构、能源结构、交通结构等十个方面提出39项重点任务。',
    },
  ],
  批转类: [
    {
      key: 'requirement_performing',
      label: '执行要求',
      placeholder:
        '是接收者需要采取的具体行动或遵守的规定。例:严格执行国务院批准的“十二五”期间年森林采伐限额，全国年采伐胸径5厘米以上林木蓄积总量不得超过XX万立方米，严禁突破限额。',
    },
    {
      key: 'Main_sending_mechanism',
      label: '主送机关',
      placeholder: '例：各省、自治区、直辖市人民政府，国务院各部委、各直属机构',
    },
    {
      key: 'document_issuing_agency',
      label: '发文机关',
      placeholder: '例：国务院',
    },
    {
      key: 'Approval_opinion',
      label: '批复意见',
      placeholder:
        '是上级机关对下级机关所呈报文件的核心态度和处理决定，直接体现批转行为的权威性与目的性。例：经研究，同意落实政策。',
    },
  ],
  指示类: [
    {
      key: 'Notice_Content',
      label: '通知内容',
      placeholder:
        '是通知的核心内容，例如：例:为加强高温天气作业劳动保护，现通知如下:一、提高思想认识。二、加强分析研判。三、注重源头预防。四、加大宣教力度。',
    },
    {
      key: 'Main_sending_mechanism',
      label: '主送机关',
      placeholder: '例：市环保局',
    },
    {
      key: 'document_issuing_agency',
      label: '发文机关',
      placeholder: '例：市政府',
    },
    {
      key: 'Reason_notification',
      label: '通知缘由',
      placeholder:
        '是发布通知的原因或背景。例:目前，全国各地陆续进入夏季高温季节',
    },
    {
      key: 'Instruction_content',
      label: '批示内容',
      placeholder:
        '直接体现上级对下级的具体工作要求、措施和安排。例:加强高温作业及高温天气作业劳动保护，有效防范职业性中暑事件的发生，切实保障劳动者身心健康和生命安全，并请各省级卫生健康行政部门及时总结防暑降温工作开展情况。',
    },
    {
      key: 'peroration',
      label: '结语',
      placeholder:
        '通常使用“特此通知”“请遵照执行”等固定语句，来总结通知内容，表达期望或强调重要性。',
    },
  ],
  事务类: [
    {
      key: 'job_content',
      label: '工作内容',
      placeholder:
        '通过具体描述工作内容，限定任务范围，防止接收方误解或遗漏关键事项。例:一是加强政策宣传；二是优化贷款申请流程；三是强化贷后管理；',
    },
    {
      key: 'Main_sending_mechanism',
      label: '主送机关',
      placeholder: '例：省学生资助管理中心  ',
    },
    {
      key: 'document_issuing_agency',
      label: '发文机关',
      placeholder: '例：××省人民政府办公厅',
    },
    {
      key: 'Reason_notification',
      label: '通知缘由',
      placeholder:
        '是发布通知的原因或背景。例:为进一步贯彻落实国家关于教育扶贫和资助政策的决策部署，切实保障家庭经济困难学生顺利入学和完成学业。',
    },
    {
      key: 'Work_arrangement',
      label: '工作安排',
      placeholder:
        '工作安排的时序性和责任分工，让接收方快速明确行动路径；明确责任主体，避免推诿扯皮，促进多部门协作；设定时效约束，确保进度可控。例:即日起至本月底，教育部门负责梳理汇总全省高校助学贷款需求，财政部门同步落实资金保障，金融监管部门协调金融机构优化贷款流程。',
    },
    {
      key: 'peroration',
      label: '结语',
      placeholder:
        '通常使用“特此通知”“请遵照执行”等固定语句，来总结通知内容，表达期望或强调重要性。',
    },
  ],
}

// 根据表单项生成初始值
const getInitialForm = (fields: { key: string }[]) => {
  const obj: Record<string, string> = {}
  fields.forEach((f) => {
    obj[f.key] = ''
  })
  return obj
}

export const Notice: React.FC = () => {
  const [current, setCurrent] = useState(0)
  const [title, setTitle] = useState('')
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState<boolean>(false)
  const [infoMessage, setInfoMessage] = useState('')
  const [step3Message, setStep3Message] = useState('')
  const [step4Message, setStep4Message] = useState('')
  const [noticeType, setNoticeType] = useState<NoticeType>('发布类')
  const [baseUrl, setBaseUrl] = useState<string>('')

  // 根据通知类别动态生成表单项
  const currentFields = noticeTypeFields[noticeType]

  // step2Form初始化和切换类别时重置
  const [step2Form, setStep2Form] = useState(getInitialForm(currentFields))
  useEffect(() => {
    setStep2Form(getInitialForm(currentFields))
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [noticeType])

  const [outlineContent, setOutlineContent] = useState('')
  const [uploadedFiles, setUploadedFiles] = useState<
    { id: string; name: string }[]
  >([])

  // 生成全文按钮禁用逻辑：所有当前表单项都不能为空
  const isStep2Disabled = currentFields.some((f) => !step2Form[f.key])

  const handleNext = async (type: string, outline?: string) => {
    if (current < 2) {
      setCurrent(current + 1)
    }
    if (type) {
      await handleGenerationStart(type, undefined, undefined, outline)
    }
  }
  const handlePrev = () => {
    setCurrent(current - 1)
  }

  // AI帮写
  const handleAIClick = (key: keyof typeof step2Form, label: string) => {
    // const fileIds = uploadedFiles.map((file) => file.id)
    let params = {
      information_type: label,
      // Occasion_speaking: step2Form.Occasion_speaking,
      query: step2Form[key],
      Notification_category: noticeType,
      // attachment: fileIds.map((x) => ({
      //   type: 'document',
      //   transfer_method: 'local_file',
      //   upload_file_id: x,
      // })),
    }
    handleGenerationStart('生成信息', params, key)
  }
  const handleRemove = (key: string) => {
    setStep2Form({ ...step2Form, [key]: '' })
  }

  useEffect(() => {
    if (uploadedFiles.length > 0) {
      // messageApi.open({
      //   key: 'uploading',
      //   type: 'success',
      //   content: '文件上传成功',
      //   duration: 1,
      // })
    }
  }, [uploadedFiles, messageApi])

  const beforeUpload = (file: File) => {
    const originalFilename = file.name.substring(0, file.name.lastIndexOf('.'))
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf('.') + 1)
      ?.toLowerCase()
    if (['pdf', 'docx'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中',
      })
      // convertFileToPDF(file).then(async (response) => {
      //   if (response['status'] && response['status'] !== 200) {
      //     messageApi.open({
      //       key: 'uploading',
      //       type: 'error',
      //       content: '文件处理异常，请稍后重试',
      //       duration: 1,
      //     })
      //   } else if ('blob' in response) {
      //     const blob = await response.blob()
      //     const pdfFile = new File([blob], `${originalFilename}.pdf`, {
      //       type: 'application/pdf',
      //     })
      //     uploadFile(pdfFile, VITE_NOTICE).then(async (response) => {
      //       if (response.id) {
      //         setUploadedFiles((prevFiles) => [...prevFiles, response])
      //         messageApi.open({
      //           key: 'uploading',
      //           type: 'success',
      //           content: '文件上传成功',
      //           duration: 1,
      //         })
      //       } else {
      //         messageApi.open({
      //           key: 'uploading',
      //           type: 'error',
      //           content: '文件上传失败',
      //           duration: 1,
      //         })
      //       }
      //     })
      //   }
      // })
      uploadFile(file, VITE_NOTICE).then(async (response) => {
        if (response.id) {
          setUploadedFiles((prevFiles) => [...prevFiles, response])
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1,
          })
        } else {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件上传失败',
            duration: 1,
          })
        }
      })
    } else {
      messageApi.error(
        '目前仅支持.docx, .pdf类型的文件，请您将文件转成这些格式后再次进行上传'
      )
    }
    return false
  }

  const handleDelete = (fileId: string) => {
    setUploadedFiles((prevFiles) =>
      prevFiles.filter((file) => file.id !== fileId)
    )
  }

  const handleGenerationStart = async (
    type: string,
    params?: any,
    aiKey?: keyof typeof step2Form,
    outline?: string
  ) => {
    setGenerating(true)
    let inputs = {}
    const fileIds = uploadedFiles.map((file) => file.id)
    switch (type) {
      case '生成信息':
        inputs = {
          ...params,
          files: fileIds.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x,
          })),
        }
        break
      case '生成大纲':
        inputs = {
          Notice_Content: step2Form.Notice_Content,
          Reason_notification: step2Form.Reason_notification,
          document_issuing_agency: step2Form.document_issuing_agency,
        }
        setOutlineContent('')
        break
      case '生成报告':
        // 动态传递所有当前表单项
        inputs = {
          ...step2Form,
          files: fileIds.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x,
          })),
          Notification_category: noticeType,
        }
        setStep4Message('')
        break
      case '生成文件':
        inputs = {
          query: step4Message,
          files: fileIds.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x,
          })),
        }
        break
      default:
        break
    }
    console.log(inputs, 'inputs')
    let res = ''
    try {
      await notice(
        {
          type,
          title,
          ...inputs,
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              console.log(res, 'res')
              if (type === '生成信息' && aiKey) {
                setStep2Form((prev) => ({ ...prev, [aiKey]: res }))
              } else if (type === '生成大纲') {
                const cleanedData = res.replace(/^```markdown\s*|```$/g, '')
                setStep3Message(cleanedData)
                setOutlineContent(cleanedData)
              } else if (type === '生成报告') {
                // setFileQuery(res)
                const cleanedData = res.replace(/^```markdown\s*|```$/g, '')
                setStep4Message(cleanedData)
              }
            }
            if (finished) {
              if (type === '生成文件') {
                const errorStr = extractContent(res, 'error')
                if (errorStr) {
                  messageApi.error(errorStr)
                  return
                }
                // 提取()中的内容
                const parenthesesContent = res.match(/\((.*?)\)/)
                const parenthesesResult = parenthesesContent
                  ? parenthesesContent[1]
                  : null

                // 提取[]中的内容
                const squareBracketsContent = res.match(/\[(.*?)\]/)
                const squareBracketsResult = squareBracketsContent
                  ? squareBracketsContent[1]
                  : null
                console.log(parenthesesResult, 'parenthesesResult')
                console.log(squareBracketsResult, 'squareBracketsResult')
                const link = document.createElement('a')
                link.href = parenthesesResult
                // link.href = `https://copilot.sino-bridge.com${parenthesesResult}`
                link.download = `通知${squareBracketsResult}`
                document.body.appendChild(link)
                link.click()
                link.remove()
              }

              setGenerating(false)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {},
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  // 动态生成基础信息内容，字段名和label都展示
  const baseInfo = {
    title,
    ...currentFields.reduce((acc, f) => {
      acc[f.key] = step2Form[f.key]
      return acc
    }, {} as Record<string, string>),
  }
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      setBaseUrl(window.location.origin + '/#')
    } else if (process.env.NODE_ENV === 'production') {
      setBaseUrl(window.location.origin + '/toolbox/#')
    }
  }, [])
  const handleJump = async (type: string) => {
    console.log(type, '优化类型')
    setGenerating(true)
    let res = ''
    const fileIds = uploadedFiles.map((file) => file.id)
    try {
      await notice(
        {
          title,
          type: '生成文件',
          query: step4Message,
          files: fileIds.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x,
          })),
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
              console.log(res, 'res')
              if (type === '格式校验') {
                window.open(
                  `${baseUrl}/document-format-verification?fileParams=${encodeURIComponent(
                    res
                  )}`,
                  '_blank'
                )
              } else if (type === '敏感词稽查') {
                window.open(
                  `${baseUrl}/document-sensitive-inspection?fileParams=${encodeURIComponent(
                    res
                  )}`,
                  '_blank'
                )
              } else if (type === '错别字校验') {
                window.open(
                  `${baseUrl}/document-verification?fileParams=${encodeURIComponent(
                    res
                  )}`,
                  '_blank'
                )
              }

              setGenerating(false)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {},
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }
  const optimizationOptionsData = [
    {
      label: '格式校验',
      description: '按照标准公文格式矫正您的公文',
      type: '格式校验',
    },
    {
      label: '敏感词稽查',
      description:
        '识别并处理可能存在的政治性、政策性、保密性、规范性或社会敏感性词汇',
      type: '敏感词稽查',
    },
    {
      label: '错别字校验',
      description:
        '拆解长难句，进行语法结构分析，逻辑矛盾检测，识别语病，错字稽查',
      type: '错别字校验',
    },
  ]

  return (
    <div className="work-report-container">
      {contextHolder}
      <Spin tip="加载中" spinning={generating} fullscreen size="large" />
      <Typography.Title
        level={2}
        style={{
          textAlign: 'center',
          color: '#2156f3',
          margin: '30px 0 24px',
          paddingBottom: 20,
        }}
      >
        通知生成
      </Typography.Title>
      <Steps current={current} className="custom-steps">
        <Step title={<span>step 1 输入类别与标题</span>} />
        <Step title={<span>step 2 输入信息</span>} />
        <Step title={<span>step 3 生成通知</span>} />
      </Steps>
      <div className="steps-content">
        {current === 0 && (
          <Flex justify="center">
            <Card style={{ width: '50%' }}>
              <Typography.Title level={4}>通知生成</Typography.Title>
              <div style={{ marginBottom: 16, marginTop: 10 }}>
                <span style={{ fontWeight: 500 }}>通知类别</span>
                <span style={{ color: 'red', marginLeft: 4 }}>*</span>
                <div
                  className="report-type"
                  style={{ display: 'flex', marginTop: 8 }}
                >
                  {noticeTypes.map((r) => (
                    <span
                      key={r.value}
                      className={`report-type-item${
                        noticeType === r.value ? ' report-type-item-active' : ''
                      }`}
                      onClick={() => setNoticeType(r.value)}
                    >
                      {r.label}
                    </span>
                  ))}
                </div>
              </div>
              <div style={{ marginBottom: 16, marginTop: 10 }}>
                <span style={{ fontWeight: 500 }}>通知标题</span>
                <span style={{ color: 'red', marginLeft: 4 }}>*</span>
                <Input
                  placeholder={recommended[noticeType].title}
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  maxLength={50}
                  // suffix={<ArrowRightOutlined />}
                  showCount
                  allowClear
                  style={{ marginTop: 8 }}
                />
              </div>
              <div style={{ marginBottom: 16 }}>
                {recommended[noticeType].recommendedOptions.map((t) => (
                  <Tag
                    key={t}
                    color="#ff523b"
                    icon={<FireFilled />}
                    style={{ marginBottom: 8, cursor: 'pointer' }}
                    onClick={() => setTitle(t)}
                  >
                    {t}
                  </Tag>
                ))}
              </div>
              <div style={{ marginBottom: 16, marginTop: 10 }}>
                <span style={{ fontWeight: 500 }}>文件上传</span>
                <span style={{ color: 'red', marginLeft: 4 }}>*</span>
                <Upload.Dragger
                  multiple
                  showUploadList={false}
                  beforeUpload={beforeUpload}
                  style={{ marginTop: '16px' }}
                  disabled={noticeType === '事务类'}
                >
                  <div
                    className={`ant-upload-drag-icon ${
                      noticeType === '事务类'
                        ? 'ant-upload-drag-icon-disabled'
                        : ''
                    }`}
                  >
                    {uploadedFiles.length > 0 ? (
                      <CheckCircleFilled />
                    ) : (
                      <InboxOutlined />
                    )}
                  </div>
                  <div className="ant-upload-hint">
                    <span>建议上传pdf、docx纯文档格式的文件</span>
                    <br />
                  </div>
                </Upload.Dragger>

                {/* 文件列表 */}
                {uploadedFiles.length > 0 && (
                  <div className="file-list">
                    <Typography.Text
                      className="section-title"
                      style={{ marginTop: '24px', display: 'block' }}
                    >
                      文档列表：
                    </Typography.Text>
                    {uploadedFiles.map((x) => (
                      // <div key={file.id} className="file-item">
                      //   <span>{file.name}</span>
                      //   <DeleteOutlined
                      //     onClick={() => handleDelete(file.id)}
                      //     style={{ color: '#ff4d4f', cursor: 'pointer' }}
                      //   />
                      // </div>
                      <p key={x.id}>
                        <Tag
                          closeIcon
                          style={{ marginTop: 4 }}
                          onClose={() => {
                            setUploadedFiles((prevList) =>
                              prevList.filter((y) => y.id !== x.id)
                            )
                            return false
                          }}
                        >
                          {x.name}
                        </Tag>
                      </p>
                    ))}
                  </div>
                )}
              </div>
              <Button
                type="primary"
                block
                size="large"
                style={{ marginTop: 16 }}
                onClick={() => handleNext('')}
                // disabled={!title || uploadedFiles.length === 0}
                disabled={!title}
              >
                下一步
              </Button>
            </Card>
          </Flex>
        )}
        {current === 1 && (
          <Flex justify="center">
            <Card style={{ width: '40%' }}>
              <Typography.Title level={4}>通知生成</Typography.Title>
              <Step2BasicInfo
                value={step2Form}
                onChange={(v) => setStep2Form({ ...step2Form, ...v })}
                onAIClick={handleAIClick}
                onRemove={handleRemove}
                fields={currentFields}
              />
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginTop: 24,
                }}
              >
                <Button onClick={handlePrev}>返回上一步</Button>
                <Button
                  type="primary"
                  onClick={() => handleNext('生成报告')}
                  disabled={isStep2Disabled}
                >
                  生成全文
                </Button>
              </div>
            </Card>
          </Flex>
        )}
        {/* {current === 2 && (
          <Flex justify="center">
            <Card style={{ width: 480 }}>
              <OutlineEditor
                outlineInfo={outlineContent}
                onChange={(v) =>
                  setOutlineContent(
                    typeof v === 'string' ? v : v ? String(v) : ''
                  )
                }
              />
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-around',
                  marginTop: 24,
                }}
              >
                <Button
                  onClick={() =>
                    handleGenerationStart(
                      '生成大纲',
                      undefined,
                      undefined,
                      undefined
                    )
                  }
                >
                  换个大纲
                </Button>
                <Button onClick={handlePrev}>返回上一步</Button>
                <Button
                  type="primary"
                  onClick={() => handleNext('生成报告', outlineContent)}
                >
                  撰写报告
                </Button>
              </div>
            </Card>
          </Flex>
        )} */}
        {current === 2 && (
          <Flex justify="center">
            <Card style={{ width: '80vw' }}>
              <div style={{ display: 'flex', gap: 24 }}>
                <Step4Report
                  reportText={step4Message}
                  baseInfo={baseInfo}
                  fields={currentFields}
                  outlineInfo={step3Message}
                />
                  {dependency !== 'shanghuawei' && (
                  <OptimizationOptions options={optimizationOptionsData} onSelect={handleJump} />
                )}
              </div>

              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-around',
                  marginTop: 24,
                }}
              >
                <Button onClick={handlePrev}>返回上一步</Button>
                <Button
                  onClick={() =>
                    handleGenerationStart('生成报告', undefined, undefined, '')
                  }
                >
                  重新生成
                </Button>
                <Button type="primary" onClick={() => handleNext('生成文件')}>
                  下载文件
                </Button>
              </div>
            </Card>
          </Flex>
        )}
        {/* 预留后续步骤 */}
        {/* {current > 0 && (
          <div style={{ textAlign: 'center', marginTop: 80 }}>
            <Typography.Text type="secondary">
              后续步骤开发中...
            </Typography.Text>
          </div>
        )} */}
      </div>
    </div>
  )
}
export default Notice
