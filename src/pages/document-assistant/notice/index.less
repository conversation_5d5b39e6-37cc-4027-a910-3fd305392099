.work-report-container {
  width: 100vw;
  height: 100vh;
  .custom-steps {
    padding: 20px 10vw 40px;
  }
  .ant-tag {
    background-color: #fff!important;
    color: #333!important;
    .anticon.anticon-fire {
      color: #ff523b!important;
    }
  }
  .ant-radio-button-wrapper {
    border-radius: 6px;
    margin-right: 10px;
    flex: none;

  }
  .report-type-item {
    border-radius: 6px;
    margin-right: 10px;
    padding: 8px 15px;
    border: 1px solid #d9d9d9;
    cursor: pointer;
  }
  .report-type-item-active {
    border-color: #2156f3;
    color: #2156f3;
  }
  .ant-upload-drag-icon {
    margin-bottom: 16px;
    font-size: 48px;
    color: #1890ff;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }
  .ant-upload-drag-icon-disabled {
    color:rgba(0,0,0,0.25)

  }
}

