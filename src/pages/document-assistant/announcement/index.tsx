import React, { useState, useEffect } from 'react'
import {
  Steps,
  Button,
  Card,
  Input,
  Typography,
  Flex,
  Tag,
  Radio,
  message,
  Spin,
  Upload,
} from 'antd'
import {
  CloseOutlined,
  ArrowRightOutlined,
  FireFilled,
  CheckCircleFilled,
  InboxOutlined,
  DeleteOutlined,
} from '@ant-design/icons'
import './index.less'
import { announcement } from '@/api/announcement'
import Step2BasicInfo from './components/Step2BasicInfo'
import OutlineEditor from '../components/OutlineEditor'
import { uploadFile, convertFileToPDF } from '@/api/template'
import Step4Report from './components/Step4Report'
import { extractContent } from '@/utils/common'
import OptimizationOptions from '../components/OptimizationOptions'

const { Step } = Steps
const VITE_ANNOUNCEMENT = import.meta.env['VITE_ANNOUNCEMENT'] || ''
const dependency = import.meta.env['VITE_DEPENDENCY'] || ''

const recommendedOptions = [
  '科学技术部直属事业单位2025年度公开招聘统一笔试公告',
  '××市人民政府关于延长机动车限行政策的公告',
  '××省防汛抗旱指挥部启动防汛Ⅰ级应急响应的公告',
]

export const Announcement: React.FC = () => {
  const [current, setCurrent] = useState(0)
  const [title, setTitle] = useState('')
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState<boolean>(false)
  const [infoMessage, setInfoMessage] = useState('')
  const [step3Message, setStep3Message] = useState('')
  const [step4Message, setStep4Message] = useState('')
  const [baseUrl, setBaseUrl] = useState<string>('')

  const [step2Form, setStep2Form] = useState({
    Notice_Content: '',
    Reason_notification: '',
    document_issuing_agency: '',
    day: '',
  })
  const [outlineContent, setOutlineContent] = useState('')
  const [uploadedFiles, setUploadedFiles] = useState<
    { id: string; name: string }[]
  >([])

  const handleNext = async (type: string, outline?: string) => {
    if (current < 2) {
      setCurrent(current + 1)
    }
    if (type) {
      await handleGenerationStart(type, undefined, undefined, outline)
    }
  }
  const handlePrev = () => {
    setCurrent(current - 1)
  }

  // AI帮写
  const handleAIClick = (key: keyof typeof step2Form, label: string) => {
    // const fileIds = uploadedFiles.map((file) => file.id)
    let params = {
      information_type: label,
      // Occasion_speaking: step2Form.Occasion_speaking,
      query: step2Form[key],
      // attachment: fileIds.map((x) => ({
      //   type: 'document',
      //   transfer_method: 'local_file',
      //   upload_file_id: x,
      // })),
    }
    handleGenerationStart('生成信息', params, key)
  }
  const handleRemove = (key: string) => {
    setStep2Form({ ...step2Form, [key]: '' })
  }

  useEffect(() => {
    if (uploadedFiles.length > 0) {
      // messageApi.open({
      //   key: 'uploading',
      //   type: 'success',
      //   content: '文件上传成功',
      //   duration: 1,
      // })
    }
  }, [uploadedFiles, messageApi])

  const beforeUpload = (file: File) => {
    const originalFilename = file.name.substring(0, file.name.lastIndexOf('.'))
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf('.') + 1)
      ?.toLowerCase()
    if (['pdf', 'docx'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中',
      })
      convertFileToPDF(file).then(async (response) => {
        if (response['status'] && response['status'] !== 200) {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件处理异常，请稍后重试',
            duration: 1,
          })
        } else if ('blob' in response) {
          const blob = await response.blob()
          const pdfFile = new File([blob], `${originalFilename}.pdf`, {
            type: 'application/pdf',
          })
          uploadFile(pdfFile, VITE_ANNOUNCEMENT).then(async (response) => {
            if (response.id) {
              setUploadedFiles((prevFiles) => [...prevFiles, response])
              messageApi.open({
                key: 'uploading',
                type: 'success',
                content: '文件上传成功',
                duration: 1,
              })
            } else {
              messageApi.open({
                key: 'uploading',
                type: 'error',
                content: '文件上传失败',
                duration: 1,
              })
            }
          })
        }
      })
    } else {
      messageApi.error(
        '目前仅支持.docx, .pdf类型的文件，请您将文件转成这些格式后再次进行上传'
      )
    }
    return false
  }

  const handleDelete = (fileId: string) => {
    setUploadedFiles((prevFiles) =>
      prevFiles.filter((file) => file.id !== fileId)
    )
  }

  const handleGenerationStart = async (
    type: string,
    params?: any,
    aiKey?: keyof typeof step2Form,
    outline?: string
  ) => {
    setGenerating(true)
    let inputs = {}
    const fileIds = uploadedFiles.map((file) => file.id)
    switch (type) {
      case '生成信息':
        inputs = {
          ...params,
          attachment: fileIds.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x,
          })),
        }
        break
      case '生成大纲':
        inputs = {
          Notice_Content: step2Form.Notice_Content,
          Reason_notification: step2Form.Reason_notification,
          document_issuing_agency: step2Form.document_issuing_agency,
        }
        setOutlineContent('')
        break
      case '生成报告':
        inputs = {
          Notice_Content: step2Form.Notice_Content,
          Reason_notification: step2Form.Reason_notification,
          document_issuing_agency: step2Form.document_issuing_agency,
          day: step2Form.day,
          attachment: fileIds.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x,
          })),
        }
        setStep4Message('')
        break
      case '生成文件':
        inputs = {
          query: step4Message,
          attachment: fileIds.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x,
          })),
        }
        break
      default:
        break
    }
    console.log(inputs, 'inputs')
    let res = ''
    try {
      await announcement(
        {
          type,
          title,
          ...inputs,
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              console.log(res, 'res')
              if (type === '生成信息' && aiKey) {
                setStep2Form((prev) => ({ ...prev, [aiKey]: res }))
              } else if (type === '生成大纲') {
                const cleanedData = res.replace(/^```markdown\s*|```$/g, '')
                setStep3Message(cleanedData)
                setOutlineContent(cleanedData)
              } else if (type === '生成报告') {
                // setFileQuery(res)
                const cleanedData = res.replace(/^```markdown\s*|```$/g, '')
                setStep4Message(cleanedData)
              }
            }
            if (finished) {
              if (type === '生成文件') {
                const errorStr = extractContent(res, 'error')
                if (errorStr) {
                  messageApi.error(errorStr)
                  return
                }
                // 提取()中的内容
                const parenthesesContent = res.match(/\((.*?)\)/)
                const parenthesesResult = parenthesesContent
                  ? parenthesesContent[1]
                  : null

                // 提取[]中的内容
                const squareBracketsContent = res.match(/\[(.*?)\]/)
                const squareBracketsResult = squareBracketsContent
                  ? squareBracketsContent[1]
                  : null
                console.log(parenthesesResult, 'parenthesesResult')
                console.log(squareBracketsResult, 'squareBracketsResult')
                const link = document.createElement('a')
                link.href = parenthesesResult
                link.download = `邮件${squareBracketsResult}`
                document.body.appendChild(link)
                link.click()
                link.remove()
              }

              setGenerating(false)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {},
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      setBaseUrl(window.location.origin + '/#')
    } else if (process.env.NODE_ENV === 'production') {
      setBaseUrl(window.location.origin + '/toolbox/#')
    }
  }, [])
  const handleJump = async (type: string) => {
    console.log(type, '优化类型')
    setGenerating(true)
    let res = ''
    const fileIds = uploadedFiles.map((file) => file.id)
    try {
      await announcement(
        {
          title,
          type: '生成文件',
          query: step4Message,
          attachment: fileIds.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x,
          })),
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
              console.log(res, 'res')
              if (type === '格式校验') {
                window.open(
                  `${baseUrl}/document-format-verification?fileParams=${encodeURIComponent(
                    res
                  )}`,
                  '_blank'
                )
              } else if (type === '敏感词稽查') {
                window.open(
                  `${baseUrl}/document-sensitive-inspection?fileParams=${encodeURIComponent(
                    res
                  )}`,
                  '_blank'
                )
              } else if (type === '错别字校验') {
                window.open(
                  `${baseUrl}/document-verification?fileParams=${encodeURIComponent(
                    res
                  )}`,
                  '_blank'
                )
              }

              setGenerating(false)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {},
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }
  const optimizationOptionsData = [
    {
      label: '格式校验',
      description: '按照标准公文格式矫正您的公文',
      type: '格式校验',
    },
    {
      label: '敏感词稽查',
      description:
        '识别并处理可能存在的政治性、政策性、保密性、规范性或社会敏感性词汇',
      type: '敏感词稽查',
    },
    {
      label: '错别字校验',
      description:
        '拆解长难句，进行语法结构分析，逻辑矛盾检测，识别语病，错字稽查',
      type: '错别字校验',
    },
  ]

  return (
    <div className="work-report-container">
      {contextHolder}
      <Spin tip="加载中" spinning={generating} fullscreen size="large" />
      <Typography.Title
        level={2}
        style={{
          textAlign: 'center',
          color: '#2156f3',
          margin: '30px 0 24px',
          paddingBottom: 20,
        }}
      >
        公告生成
      </Typography.Title>
      <Steps current={current} className="custom-steps">
        <Step title={<span>step 1 输入标题和上传文件</span>} />
        <Step title={<span>step 2 输入信息</span>} />
        <Step title={<span>step 3 生成公告</span>} />
      </Steps>
      <div className="steps-content">
        {current === 0 && (
          <Flex justify="center">
            <Card style={{ width: '50%' }}>
              <Typography.Title level={4}>公告生成</Typography.Title>
              <div style={{ marginBottom: 16, marginTop: 10 }}>
                <span style={{ fontWeight: 500 }}>公告标题</span>
                <span style={{ color: 'red', marginLeft: 4 }}>*</span>
                <Input
                  placeholder="发文机关名称+事由+文种”组成（如《国务院关于实施乡村振兴战略的公告》）"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  maxLength={50}
                  // suffix={<ArrowRightOutlined />}
                  showCount
                  allowClear
                  style={{ marginTop: 8 }}
                />
              </div>
              <div style={{ marginBottom: 16 }}>
                {recommendedOptions.map((t) => (
                  <Tag
                    key={t}
                    color="#ff523b"
                    icon={<FireFilled />}
                    style={{ marginBottom: 8, cursor: 'pointer' }}
                    onClick={() => setTitle(t)}
                  >
                    {t}
                  </Tag>
                ))}
              </div>
              <div style={{ marginBottom: 16, marginTop: 10 }}>
                <span style={{ fontWeight: 500 }}>文件上传</span>
                <span style={{ color: 'red', marginLeft: 4 }}>*</span>
                <Upload.Dragger
                  multiple
                  showUploadList={false}
                  beforeUpload={beforeUpload}
                  style={{ marginTop: '16px' }}
                >
                  <div className="ant-upload-drag-icon">
                    {uploadedFiles.length > 0 ? (
                      <CheckCircleFilled />
                    ) : (
                      <InboxOutlined />
                    )}
                  </div>
                  <div className="ant-upload-hint">
                    <span>建议上传pdf、docx纯文档格式的文件</span>
                    <br />
                  </div>
                </Upload.Dragger>

                {/* 文件列表 */}
                {uploadedFiles.length > 0 && (
                  <div className="file-list">
                    <Typography.Text
                      className="section-title"
                      style={{ marginTop: '24px', display: 'block' }}
                    >
                      文档列表：
                    </Typography.Text>
                    {uploadedFiles.map((x) => (
                      // <div key={file.id} className="file-item">
                      //   <span>{file.name}</span>
                      //   <DeleteOutlined
                      //     onClick={() => handleDelete(file.id)}
                      //     style={{ color: '#ff4d4f', cursor: 'pointer' }}
                      //   />
                      // </div>
                      <p key={x.id}>
                        <Tag
                          closeIcon
                          style={{ marginTop: 4 }}
                          onClose={() => {
                            setUploadedFiles((prevList) =>
                              prevList.filter((y) => y.id !== x.id)
                            )
                            return false
                          }}
                        >
                          {x.name}
                        </Tag>
                      </p>
                    ))}
                  </div>
                )}
              </div>
              <Button
                type="primary"
                block
                size="large"
                style={{ marginTop: 16 }}
                onClick={() => handleNext('')}
                disabled={!title || uploadedFiles.length === 0}
              >
                下一步
              </Button>
            </Card>
          </Flex>
        )}
        {current === 1 && (
          <Flex justify="center">
            <Card style={{ width: '40%' }}>
              <Typography.Title level={4}>公告生成</Typography.Title>
              <Step2BasicInfo
                value={step2Form}
                onChange={(v) => setStep2Form({ ...step2Form, ...v })}
                onAIClick={handleAIClick}
                onRemove={handleRemove}
              />
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginTop: 24,
                }}
              >
                <Button onClick={handlePrev}>返回上一步</Button>
                <Button
                  type="primary"
                  onClick={() => handleNext('生成报告')}
                  disabled={
                    !step2Form.Notice_Content ||
                    !step2Form.Reason_notification ||
                    !step2Form.document_issuing_agency ||
                    !step2Form.day
                  }
                >
                  生成全文
                </Button>
              </div>
            </Card>
          </Flex>
        )}
        {/* {current === 2 && (
          <Flex justify="center">
            <Card style={{ width: 480 }}>
              <OutlineEditor
                outlineInfo={outlineContent}
                onChange={(v) =>
                  setOutlineContent(
                    typeof v === 'string' ? v : v ? String(v) : ''
                  )
                }
              />
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-around',
                  marginTop: 24,
                }}
              >
                <Button
                  onClick={() =>
                    handleGenerationStart(
                      '生成大纲',
                      undefined,
                      undefined,
                      undefined
                    )
                  }
                >
                  换个大纲
                </Button>
                <Button onClick={handlePrev}>返回上一步</Button>
                <Button
                  type="primary"
                  onClick={() => handleNext('生成报告', outlineContent)}
                >
                  撰写报告
                </Button>
              </div>
            </Card>
          </Flex>
        )} */}
        {current === 2 && (
          <Flex justify="center">
            <Card style={{ width: '80vw' }}>
              <div style={{ display: 'flex', gap: 24 }}>
                <Step4Report
                  reportText={step4Message}
                  baseInfo={{
                    Notice_Content: step2Form.Notice_Content,
                    Reason_notification: step2Form.Reason_notification,
                    document_issuing_agency: step2Form.document_issuing_agency,
                    title: title,
                    day: step2Form.day,
                  }}
                  outlineInfo={step3Message}
                />
                  {dependency !== 'shanghuawei' && (
                  <OptimizationOptions options={optimizationOptionsData} onSelect={handleJump} />
                )}
              </div>

              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-around',
                  marginTop: 24,
                }}
              >
                <Button onClick={handlePrev}>返回上一步</Button>
                <Button
                  onClick={() =>
                    handleGenerationStart('生成报告', undefined, undefined, '')
                  }
                >
                  重新生成
                </Button>
                <Button type="primary" onClick={() => handleNext('生成文件')}>
                  下载文件
                </Button>
              </div>
            </Card>
          </Flex>
        )}
        {/* 预留后续步骤 */}
        {/* {current > 0 && (
          <div style={{ textAlign: 'center', marginTop: 80 }}>
            <Typography.Text type="secondary">
              后续步骤开发中...
            </Typography.Text>
          </div>
        )} */}
      </div>
    </div>
  )
}
export default Announcement
