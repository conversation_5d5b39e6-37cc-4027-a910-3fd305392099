import React from 'react'
import { Input, Button, Typography, Space, Tooltip, Tag } from 'antd'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons'
import { FireFilled } from '@ant-design/icons'

const fields = [
  {
    key: 'Notice_Content',
    label: '内容提要',
    placeholder:
      '是公告的核心内容。例:根据事业单位公开招聘工作有关规定，科学技术部直属事业单位将组织开展2025年度公开招聘统一笔试工作。',
  },
  {
    key: 'Reason_notification',
    label: '公告背景',
    placeholder:
      '是发布公告的原因或背景。例:为深入贯彻党中央、国务院关于深化事业单位人事制度改革的决策部署，落实《事业单位人事管理条例》要求，进一步加强科学技术部直属事业单位人才队伍建设。',
  },
  {
    key: 'document_issuing_agency',
    label: '发布单位',
    placeholder: '科技部',
  },
  {
    key: 'day',
    label: '发布日期',
    placeholder: '2025年5月1日',
  },
]

interface Step2BasicInfoProps {
  value: Record<string, string>
  onChange: (v: Record<string, string>) => void
  onRemove?: (key: string) => void
  onAIClick?: (key: keyof Step2BasicInfoProps['value'], label: string) => void
}

const Step2BasicInfo: React.FC<Step2BasicInfoProps> = ({
  value,
  onChange,
  onRemove,
  onAIClick,
}) => {
  const handleInput = (key: string, val: string) => {
    onChange({ ...value, [key]: val })
  }

  // 需要AI帮写的字段
  const aiFields = ['Notice_Content', 'Reason_notification']
  // 用Input的字段
  const inputFields = ['document_issuing_agency', 'day']

  return (
    <div>
      {fields.map((f) => (
        <div
          key={f.key}
          style={{
            marginBottom: 20,
            borderRadius: 8,
            padding: 12,
          }}
        >
          <div
            style={{ display: 'flex', alignItems: 'center', marginBottom: 6 }}
          >
            <span style={{ fontWeight: 500 }}>{f.label}</span>
            <span style={{ color: 'red', marginLeft: 4 }}>*</span>
            <Space size={8} style={{ marginLeft: 'auto' }}>
              {aiFields.includes(f.key) && (
                <Tooltip title="AI帮写">
                  <Button
                    type="link"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() =>
                      onAIClick?.(
                        f.key as keyof Step2BasicInfoProps['value'],
                        f.label
                      )
                    }
                  >
                    AI帮写
                  </Button>
                </Tooltip>
              )}
              <Tooltip title="删除">
                <Button
                  type="link"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={() => onRemove?.(f.key)}
                />
              </Tooltip>
            </Space>
          </div>

          {/* 输入框类型 */}
          {inputFields.includes(f.key) ? (
            <Input
              placeholder={f.placeholder}
              value={value[f.key] || ''}
              onChange={(e) => handleInput(f.key, e.target.value)}
              maxLength={50}
              showCount
            />
          ) : (
            <Input.TextArea
              placeholder={f.placeholder}
              value={value[f.key] || ''}
              onChange={(e) => handleInput(f.key, e.target.value)}
              maxLength={800}
              rows={3}
              showCount
            />
          )}
          {/* 推荐项 */}
          {f.key === 'Occasion_speaking' && f.recommendedOptions && (
            <div style={{ marginBottom: 8 }}>
              {f.recommendedOptions.map((t) => (
                <Tag
                  key={t}
                  color="#ff523b"
                  icon={<FireFilled />}
                  style={{ cursor: 'pointer', marginTop: 8 }}
                  onClick={() => handleInput(f.key, t)}
                >
                  {t}
                </Tag>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

export default Step2BasicInfo
