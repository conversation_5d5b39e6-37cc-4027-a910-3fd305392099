import React from 'react'
import { Input, Button, Typography, Space, Tooltip, Tag } from 'antd'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons'
import { FireFilled } from '@ant-design/icons'

const fields = [
  {
    key: 'Request_reason',
    label: '请示缘由',
    placeholder:
      '是发布请示报告的原因或背景。例:为了加快推进XX区“十三五”养老床位建设计划，满足老百姓对养老床位的需求。',
    recommendedOptions: [
      '动员部署大会',
      'XX活动心得交流',
      'XX活动致辞',
      'XXX任职发言等',
    ],
  },
  {
    key: 'Content_request_Instructions',
    label: '请示内容',
    placeholder:
      '是请示公文的核心部分，需清晰、具体、有逻辑地呈现请求事项及依据。例:加快落实XXX地块的划拨工作。',
  },
  {
    key: 'Main_sending_mechanism',
    label: '主送机关',
    placeholder: '例：市政府',
  },
  {
    key: 'document_issuing_agency',
    label: '发文机关',
    placeholder: '例：市民政局',
  },
  {
    key: 'peroration',
    label: '结语',
    placeholder:
      '用来总结报告内容，表达期望或强调重要性。例:总之，作为一名年轻干部，在今后学习生活工作中就是要以“七种能力”为方向，为以后更好的工作打下夯实的基础。',
  },
  {
    key: 'day',
    label: '成文日期',
    placeholder: '例：2025年05月01日',
  },
  {
    key: 'contact_information',
    label: '联系方式',
    placeholder: '例：李四17512540698',
  },
]

interface Step2BasicInfoProps {
  value: Record<string, string>
  onChange: (v: Record<string, string>) => void
  onRemove?: (key: string) => void
  onAIClick?: (key: keyof Step2BasicInfoProps['value'], label: string) => void
}

const Step2BasicInfo: React.FC<Step2BasicInfoProps> = ({
  value,
  onChange,
  onRemove,
  onAIClick,
}) => {
  const handleInput = (key: string, val: string) => {
    onChange({ ...value, [key]: val })
  }

  // 需要AI帮写的字段
  const aiFields = [
    'Request_reason',
    'Content_request_Instructions',
    'peroration',
  ]
  // 用Input的字段
  const inputFields = [
    'Main_sending_mechanism',
    'document_issuing_agency',
    'day',
    'contact_information',
  ]

  return (
    <div>
      {fields.map((f) => (
        <div
          key={f.key}
          style={{
            marginBottom: 20,
            borderRadius: 8,
            padding: 12,
          }}
        >
          <div
            style={{ display: 'flex', alignItems: 'center', marginBottom: 6 }}
          >
            <span style={{ fontWeight: 500 }}>{f.label}</span>
            <span style={{ color: 'red', marginLeft: 4 }}>*</span>
            <Space size={8} style={{ marginLeft: 'auto' }}>
              {aiFields.includes(f.key) && (
                <Tooltip title="AI帮写">
                  <Button
                    type="link"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() =>
                      onAIClick?.(
                        f.key as keyof Step2BasicInfoProps['value'],
                        f.label
                      )
                    }
                  >
                    AI帮写
                  </Button>
                </Tooltip>
              )}
              <Tooltip title="删除">
                <Button
                  type="link"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={() => onRemove?.(f.key)}
                />
              </Tooltip>
            </Space>
          </div>

          {/* 输入框类型 */}
          {inputFields.includes(f.key) ? (
            <Input
              placeholder={f.placeholder}
              value={value[f.key] || ''}
              onChange={(e) => handleInput(f.key, e.target.value)}
              maxLength={50}
              showCount
            />
          ) : (
            <Input.TextArea
              placeholder={f.placeholder}
              value={value[f.key] || ''}
              onChange={(e) => handleInput(f.key, e.target.value)}
              maxLength={800}
              rows={3}
              showCount
            />
          )}
          {/* 推荐项 */}
          {f.key === 'Occasion_speaking' && f.recommendedOptions && (
            <div style={{ marginBottom: 8 }}>
              {f.recommendedOptions.map((t) => (
                <Tag
                  key={t}
                  color="#ff523b"
                  icon={<FireFilled />}
                  style={{ cursor: 'pointer', marginTop: 8 }}
                  onClick={() => handleInput(f.key, t)}
                >
                  {t}
                </Tag>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

export default Step2BasicInfo
