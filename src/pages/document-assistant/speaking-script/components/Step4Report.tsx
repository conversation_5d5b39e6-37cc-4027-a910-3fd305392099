import React from 'react'
import { <PERSON>, Button, Typography, message } from 'antd'
import './Step4Report.less'
import StreamTypewriter from '@/component/StreamTypewriter'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import RemarkBreaks from 'remark-breaks'
import RemarkMath from 'remark-math'
import rehypeRaw from 'rehype-raw'

interface Step4ReportProps {
  reportText: string
  baseInfo: {
    Occasion_speaking: string
    Speech_content: string
    Spokesperson_Identity: string
    Audience_role: string
    Background_Speech: string
  }
  outlineInfo: string
}

const Step4Report: React.FC<Step4ReportProps> = ({
  reportText,
  baseInfo,
  outlineInfo,
}) => {
  const handleCopy = () => {
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard
        .writeText(reportText)
        .then(() => message.success('已复制到剪贴板'))
        .catch(() => fallbackCopyTextToClipboard(reportText))
    } else {
      fallbackCopyTextToClipboard(reportText)
    }
  }

  function fallbackCopyTextToClipboard(text: string) {
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.top = '0'
    textArea.style.left = '0'
    textArea.style.width = '2em'
    textArea.style.height = '2em'
    textArea.style.padding = '0'
    textArea.style.border = 'none'
    textArea.style.outline = 'none'
    textArea.style.boxShadow = 'none'
    textArea.style.background = 'transparent'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    try {
      const successful = document.execCommand('copy')
      if (successful) {
        message.success('已复制到剪贴板')
      } else {
        message.error('复制失败，请手动复制')
      }
    } catch (err) {
      message.error('复制失败，请手动复制')
    }
    document.body.removeChild(textArea)
  }

  const scrollRef = React.useRef<HTMLDivElement>(null)
  // 只在 reportText 为空时重置 key，避免每次内容变化都重头渲染
  const [typewriterKey, setTypewriterKey] = React.useState(0)
  React.useEffect(() => {
    if (!reportText) setTypewriterKey((k) => k + 1)
  }, [reportText])

  return (
    <div className="step4-report-wrapper">
      <div className="step4-report-left">
        <Card ref={scrollRef} style={{ height: '100%', overflow: 'auto' }}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: 12,
            }}
          >
            <Typography.Text strong>
              根据您提供的信息内容，撰写出以下内容：
            </Typography.Text>
            <Button type="link" onClick={handleCopy}>
              复制全文
            </Button>
          </div>
          {/* <pre
            className="report-text"
            style={{
              whiteSpace: 'pre-wrap',
              fontFamily: 'inherit',
              fontSize: 15,
            }}
          >
            {reportText}
          </pre> */}

          <StreamTypewriter
            key={typewriterKey}
            text={reportText}
            onchange={() => {
              scrollRef.current?.scrollTo({
                top: scrollRef.current.scrollHeight,
                behavior: 'smooth',
              })
            }}
          />

          {/* <ReactMarkdown
            className="markdown-body step3Analysis-container"
            remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}
            rehypePlugins={[rehypeRaw]}
          >
            {reportText}
          </ReactMarkdown> */}
        </Card>
      </div>
      <div className="step4-report-right">
        <Card style={{ marginBottom: 16 }}>
          <Typography.Title level={5}>基础信息</Typography.Title>
          <div style={{ fontSize: 14 }}>
            <div>
              <b>发言场合</b>
              <div style={{ whiteSpace: 'pre-wrap', marginBottom: 8 }}>
                {baseInfo.Occasion_speaking}
              </div>
            </div>
            <div>
              <b>发言内容</b>
              <div style={{ whiteSpace: 'pre-wrap', marginBottom: 8 }}>
                {baseInfo.Speech_content}
              </div>
            </div>
            <div>
              <b>发言人身份</b>
              <div style={{ whiteSpace: 'pre-wrap', marginBottom: 8 }}>
                {baseInfo.Spokesperson_Identity}
              </div>
            </div>
            <div>
              <b>听众角色</b>
              <div style={{ whiteSpace: 'pre-wrap' }}>
                {baseInfo.Audience_role}
              </div>
            </div>
            <div>
              <b>发言背景</b>
              <div style={{ whiteSpace: 'pre-wrap' }}>
                {baseInfo.Background_Speech}
              </div>
            </div>
          </div>
        </Card>
        <Card>
          <Typography.Title level={5}>大纲信息</Typography.Title>
          {/* <pre
            style={{
              whiteSpace: 'pre-wrap',
              fontFamily: 'inherit',
              fontSize: 14,
            }}
          >
            {outlineInfo}
          </pre> */}
          <ReactMarkdown
            className="markdown-body step3Analysis-container"
            remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}
            rehypePlugins={[rehypeRaw]}
          >
            {outlineInfo}
          </ReactMarkdown>
        </Card>
      </div>
    </div>
  )
}

export default Step4Report
