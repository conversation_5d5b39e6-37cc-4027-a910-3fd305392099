import React from 'react'
import { Input, Button, Typography, Space, Tooltip, Tag } from 'antd'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons'
import { FireFilled } from '@ant-design/icons'

const fields = [
  {
    key: 'Occasion_speaking',
    label: '发言场合',
    placeholder: '例：教师节动员部署会',
    recommendedOptions: [
      '动员部署大会',
      'XX活动心得交流',
      'XX活动致辞',
      'XXX任职发言等',
    ],
  },
  {
    key: 'Speech_content',
    label: '发言内容',
    placeholder:
      '例：回顾去年的工作，表示对教职工的问候，并提出后续工作的指导性意见，包括发展战略、教育公平、人才培养等方面。',
  },
  {
    key: 'Spokesperson_Identity',
    label: '发言人身份',
    placeholder: '例：县委书记',
  },
  {
    key: 'Audience_role',
    label: '听众角色',
    placeholder: '例：与会代表',
  },
  {
    key: 'Background_Speech',
    label: '发言背景',
    placeholder: '例：值此教师节到来之际，落实习近平总书记的精神。',
  },
]

interface Step2BasicInfoProps {
  value: Record<string, string>
  onChange: (v: Record<string, string>) => void
  onRemove?: (key: string) => void
  onAIClick?: (key: keyof Step2BasicInfoProps['value'], label: string) => void
}

const Step2BasicInfo: React.FC<Step2BasicInfoProps> = ({
  value,
  onChange,
  onRemove,
  onAIClick,
}) => {
  const handleInput = (key: string, val: string) => {
    onChange({ ...value, [key]: val })
  }

  // 需要AI帮写的字段
  const aiFields = ['Speech_content', 'Background_Speech']
  // 用Input的字段
  const inputFields = [
    'Occasion_speaking',
    'Spokesperson_Identity',
    'Audience_role',
  ]

  return (
    <div>
      {fields.map((f) => (
        <div
          key={f.key}
          style={{
            marginBottom: 20,
            borderRadius: 8,
            padding: 12,
          }}
        >
          <div
            style={{ display: 'flex', alignItems: 'center', marginBottom: 6 }}
          >
            <span style={{ fontWeight: 500 }}>{f.label}</span>
            <span style={{ color: 'red', marginLeft: 4 }}>*</span>
            <Space size={8} style={{ marginLeft: 'auto' }}>
              {aiFields.includes(f.key) && (
                <Tooltip title="AI帮写">
                  <Button
                    type="link"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() =>
                      onAIClick?.(
                        f.key as keyof Step2BasicInfoProps['value'],
                        f.label
                      )
                    }
                  >
                    AI帮写
                  </Button>
                </Tooltip>
              )}
              <Tooltip title="删除">
                <Button
                  type="link"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={() => onRemove?.(f.key)}
                />
              </Tooltip>
            </Space>
          </div>

          {/* 输入框类型 */}
          {inputFields.includes(f.key) ? (
            <Input
              placeholder={f.placeholder}
              value={value[f.key] || ''}
              onChange={(e) => handleInput(f.key, e.target.value)}
              maxLength={50}
              showCount
            />
          ) : (
            <Input.TextArea
              placeholder={f.placeholder}
              value={value[f.key] || ''}
              onChange={(e) => handleInput(f.key, e.target.value)}
              maxLength={800}
              rows={3}
              showCount
            />
          )}
          {/* 推荐项 */}
          {f.key === 'Occasion_speaking' && f.recommendedOptions && (
            <div style={{ marginBottom: 8 }}>
              {f.recommendedOptions.map((t) => (
                <Tag
                  key={t}
                  color="#ff523b"
                  icon={<FireFilled />}
                  style={{ cursor: 'pointer', marginTop: 8 }}
                  onClick={() => handleInput(f.key, t)}
                >
                  {t}
                </Tag>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

export default Step2BasicInfo
