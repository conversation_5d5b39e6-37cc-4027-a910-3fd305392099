import React from 'react'
import { Input, Button, Typography, Space, Tooltip, Tag } from 'antd'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons'
import { FireFilled } from '@ant-design/icons'

const fields = [
  {
    key: 'email_reason',
    label: '邮件缘由',
    placeholder:
      '是发送邮件的原因或背景。例:为了加快推进XX区“十三五”养老床位建设计划，满足老百姓对养老床位的需求。',
  },
  {
    key: 'Content_email',
    label: '邮件内容',
    placeholder:
      '是邮件的核心部分，需清晰、具体、有逻辑地呈现请求事项及依据。例:加快落实XXX地块的划拨工作。',
  },
  {
    key: 'addresser',
    label: '发件人',
    placeholder: '例：XX科技公司-信息管理部门-赵婷 ',
  },
  {
    key: 'addressee',
    label: '收件人',
    placeholder: '例：XX责任有限公司',
  },
  {
    key: 'carbon_copy_recipients',
    label: '抄送/密送人员',
    placeholder: '例：XX部门 王总',
  },
  {
    key: 'day',
    label: '成文日期',
    placeholder: '例：2025年05月01日',
  },
  {
    key: 'contact_information',
    label: '发件人联系方式',
    placeholder: '例：赵婷 17512540698',
  },
  {
    key: 'address',
    label: '发件人单位/地址',
    placeholder: '例：XX科技公司注册地址',
  },
]

interface Step2BasicInfoProps {
  value: Record<string, string>
  onChange: (v: Record<string, string>) => void
  onRemove?: (key: string) => void
  onAIClick?: (key: keyof Step2BasicInfoProps['value'], label: string) => void
}

const Step2BasicInfo: React.FC<Step2BasicInfoProps> = ({
  value,
  onChange,
  onRemove,
  onAIClick,
}) => {
  const handleInput = (key: string, val: string) => {
    onChange({ ...value, [key]: val })
  }

  // 需要AI帮写的字段
  const aiFields = ['email_reason', 'Content_email']
  // 用Input的字段
  const inputFields = [
    'addresser',
    'addressee',
    'carbon_copy_recipients',
    'day',
    'contact_information',
    'address',
  ]

  return (
    <div>
      {fields.map((f) => (
        <div
          key={f.key}
          style={{
            marginBottom: 20,
            borderRadius: 8,
            padding: 12,
          }}
        >
          <div
            style={{ display: 'flex', alignItems: 'center', marginBottom: 6 }}
          >
            <span style={{ fontWeight: 500 }}>{f.label}</span>
            <span style={{ color: 'red', marginLeft: 4 }}>*</span>
            <Space size={8} style={{ marginLeft: 'auto' }}>
              {aiFields.includes(f.key) && (
                <Tooltip title="AI帮写">
                  <Button
                    type="link"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() =>
                      onAIClick?.(
                        f.key as keyof Step2BasicInfoProps['value'],
                        f.label
                      )
                    }
                  >
                    AI帮写
                  </Button>
                </Tooltip>
              )}
              <Tooltip title="删除">
                <Button
                  type="link"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={() => onRemove?.(f.key)}
                />
              </Tooltip>
            </Space>
          </div>

          {/* 输入框类型 */}
          {inputFields.includes(f.key) ? (
            <Input
              placeholder={f.placeholder}
              value={value[f.key] || ''}
              onChange={(e) => handleInput(f.key, e.target.value)}
              maxLength={50}
              showCount
            />
          ) : (
            <Input.TextArea
              placeholder={f.placeholder}
              value={value[f.key] || ''}
              onChange={(e) => handleInput(f.key, e.target.value)}
              maxLength={800}
              rows={3}
              showCount
            />
          )}
          {/* 推荐项 */}
          {f.key === 'Occasion_speaking' && f.recommendedOptions && (
            <div style={{ marginBottom: 8 }}>
              {f.recommendedOptions.map((t) => (
                <Tag
                  key={t}
                  color="#ff523b"
                  icon={<FireFilled />}
                  style={{ cursor: 'pointer', marginTop: 8 }}
                  onClick={() => handleInput(f.key, t)}
                >
                  {t}
                </Tag>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

export default Step2BasicInfo
