import React, { useState, useEffect } from 'react'
import {
  Steps,
  Button,
  Card,
  Input,
  Typography,
  Flex,
  Tag,
  Radio,
  message,
  Spin,
} from 'antd'
import {
  CloseOutlined,
  ArrowRightOutlined,
  FireFilled,
} from '@ant-design/icons'
import './index.less'
import { emailReport } from '@/api/email'
import Step2BasicInfo from './components/Step2BasicInfo'
import OutlineEditor from '../components/OutlineEditor'
import Step4Report from './components/Step4Report'
import { extractContent } from '@/utils/common'
import OptimizationOptions from '../components/OptimizationOptions'

const { Step } = Steps

const dependency = import.meta.env['VITE_DEPENDENCY'] || ''
const recommendedOptions = [
  '【通知】2023年第三季度总结会时间调整',
  '【请示】关于市场部2024年活动预算调整的申请',
  '【报告】2023年“双十一”促销活动总结',
  '【邀请函】诚邀参加2023年XX行业峰会',
  '【申请】关于XX项目场地租赁的协调支持',
  '【公告】关于调整员工考勤制度的通知',
]

export const EmailReport: React.FC = () => {
  const [current, setCurrent] = useState(0)
  const [title, setTitle] = useState('')
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState<boolean>(false)
  const [infoMessage, setInfoMessage] = useState('')
  const [step3Message, setStep3Message] = useState('')
  const [step4Message, setStep4Message] = useState('')
  const [baseUrl, setBaseUrl] = useState<string>('')

  const [step2Form, setStep2Form] = useState({
    email_reason: '',
    Content_email: '',
    addresser: '',
    addressee: '',
    carbon_copy_recipients: '',
    day: '',
    contact_information: '',
    address: '',
  })
  const [outlineContent, setOutlineContent] = useState('')

  const handleNext = async (type: string, outline?: string) => {
    if (current < 2) {
      setCurrent(current + 1)
    }
    if (type) {
      await handleGenerationStart(type, undefined, undefined, outline)
    }
  }
  const handlePrev = () => {
    setCurrent(current - 1)
  }

  // AI帮写
  const handleAIClick = (key: keyof typeof step2Form, label: string) => {
    let params = {
      information_type: label,
      // Occasion_speaking: step2Form.Occasion_speaking,
      query: step2Form[key],
    }
    handleGenerationStart('生成信息', params, key)
  }
  const handleRemove = (key: string) => {
    setStep2Form({ ...step2Form, [key]: '' })
  }
  const handleGenerationStart = async (
    type: string,
    params?: any,
    aiKey?: keyof typeof step2Form,
    outline?: string
  ) => {
    setGenerating(true)
    let inputs = {}
    switch (type) {
      case '生成信息':
        inputs = {
          ...params,
        }
        break
      case '生成大纲':
        inputs = {
          email_reason: step2Form.email_reason,
          Content_email: step2Form.Content_email,
          addresser: step2Form.addresser,
          addressee: step2Form.addressee,
          carbon_copy_recipients: step2Form.carbon_copy_recipients,
          day: step2Form.day,
          contact_information: step2Form.contact_information,
          address: step2Form.address,
        }
        setOutlineContent('')
        break
      case '生成报告':
        inputs = {
          email_reason: step2Form.email_reason,
          Content_email: step2Form.Content_email,
          addresser: step2Form.addresser,
          addressee: step2Form.addressee,
          carbon_copy_recipients: step2Form.carbon_copy_recipients,
          day: step2Form.day,
          contact_information: step2Form.contact_information,
          address: step2Form.address,
        }
        setStep4Message('')
        break
      case '生成文件':
        inputs = {
          query: step4Message,
        }
        break
      default:
        break
    }
    console.log(inputs, 'inputs')
    let res = ''
    try {
      await emailReport(
        {
          type,
          title,
          ...inputs,
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              console.log(res, 'res')
              if (type === '生成信息' && aiKey) {
                setStep2Form((prev) => ({ ...prev, [aiKey]: res }))
              } else if (type === '生成大纲') {
                const cleanedData = res.replace(/^```markdown\s*|```$/g, '')
                setStep3Message(cleanedData)
                setOutlineContent(cleanedData)
              } else if (type === '生成报告') {
                // setFileQuery(res)
                const cleanedData = res.replace(/^```markdown\s*|```$/g, '')
                setStep4Message(cleanedData)
              }
            }
            if (finished) {
              if (type === '生成文件') {
                const errorStr = extractContent(res, 'error')
                if (errorStr) {
                  messageApi.error(errorStr)
                  return
                }
                // 提取()中的内容
                const parenthesesContent = res.match(/\((.*?)\)/)
                const parenthesesResult = parenthesesContent
                  ? parenthesesContent[1]
                  : null

                // 提取[]中的内容
                const squareBracketsContent = res.match(/\[(.*?)\]/)
                const squareBracketsResult = squareBracketsContent
                  ? squareBracketsContent[1]
                  : null
                console.log(parenthesesResult, 'parenthesesResult')
                console.log(squareBracketsResult, 'squareBracketsResult')
                const link = document.createElement('a')
                link.href = parenthesesResult
                link.download = `邮件${squareBracketsResult}`
                document.body.appendChild(link)
                link.click()
                link.remove()
              }

              setGenerating(false)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {},
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      setBaseUrl(window.location.origin + '/#')
    } else if (process.env.NODE_ENV === 'production') {
      setBaseUrl(window.location.origin + '/toolbox/#')
    }
  }, [])
  const handleJump = async (type: string) => {
    console.log(type, '优化类型')
    setGenerating(true)
    let res = ''
    try {
      await emailReport(
        {
          title,
          type: '生成文件',
          query: step4Message,
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
              console.log(res, 'res')
              if (type === '格式校验') {
                window.open(
                  `${baseUrl}/document-format-verification?fileParams=${encodeURIComponent(
                    res
                  )}`,
                  '_blank'
                )
              } else if (type === '敏感词稽查') {
                window.open(
                  `${baseUrl}/document-sensitive-inspection?fileParams=${encodeURIComponent(
                    res
                  )}`,
                  '_blank'
                )
              } else if (type === '错别字校验') {
                window.open(
                  `${baseUrl}/document-verification?fileParams=${encodeURIComponent(
                    res
                  )}`,
                  '_blank'
                )
              }

              setGenerating(false)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {},
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }
  const optimizationOptionsData = [
    {
      label: '格式校验',
      description: '按照标准公文格式矫正您的公文',
      type: '格式校验',
    },
    {
      label: '敏感词稽查',
      description:
        '识别并处理可能存在的政治性、政策性、保密性、规范性或社会敏感性词汇',
      type: '敏感词稽查',
    },
    {
      label: '错别字校验',
      description:
        '拆解长难句，进行语法结构分析，逻辑矛盾检测，识别语病，错字稽查',
      type: '错别字校验',
    },
  ]

  return (
    <div className="work-report-container">
      {contextHolder}
      <Spin tip="加载中" spinning={generating} fullscreen size="large" />
      <Typography.Title
        level={2}
        style={{
          textAlign: 'center',
          color: '#2156f3',
          margin: '30px 0 24px',
          paddingBottom: 20,
        }}
      >
        邮件生成
      </Typography.Title>
      <Steps current={current} className="custom-steps">
        <Step title={<span>step 1 输入标题</span>} />
        <Step title={<span>step 2 输入信息</span>} />
        <Step title={<span>step 3 生成邮件</span>} />
      </Steps>
      <div className="steps-content">
        {current === 0 && (
          <Flex justify="center">
            <Card style={{ width: '50%' }}>
              <Typography.Title level={4}>邮件生成</Typography.Title>
              <div style={{ marginBottom: 16, marginTop: 10 }}>
                <span style={{ fontWeight: 500 }}>邮件标题</span>
                <span style={{ color: 'red', marginLeft: 4 }}>*</span>
                <Input
                  placeholder="【邮件类型】关于 +（内容）"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  maxLength={50}
                  // suffix={<ArrowRightOutlined />}
                  showCount
                  allowClear
                  style={{ marginTop: 8 }}
                />
              </div>
              <div style={{ marginBottom: 16 }}>
                {recommendedOptions.map((t) => (
                  <Tag
                    key={t}
                    color="#ff523b"
                    icon={<FireFilled />}
                    style={{ marginBottom: 8, cursor: 'pointer' }}
                    onClick={() => setTitle(t)}
                  >
                    {t}
                  </Tag>
                ))}
              </div>
              <Button
                type="primary"
                block
                size="large"
                style={{ marginTop: 16 }}
                onClick={() => handleNext('')}
                disabled={!title}
              >
                下一步
              </Button>
            </Card>
          </Flex>
        )}
        {current === 1 && (
          <Flex justify="center">
            <Card style={{ width: '40%' }}>
              <Typography.Title level={4}>邮件生成</Typography.Title>
              <Step2BasicInfo
                value={step2Form}
                onChange={(v) => setStep2Form({ ...step2Form, ...v })}
                onAIClick={handleAIClick}
                onRemove={handleRemove}
              />
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginTop: 24,
                }}
              >
                <Button onClick={handlePrev}>返回上一步</Button>
                <Button
                  type="primary"
                  onClick={() => handleNext('生成报告')}
                  disabled={
                    !step2Form.email_reason ||
                    !step2Form.Content_email ||
                    !step2Form.addresser ||
                    !step2Form.addressee ||
                    !step2Form.carbon_copy_recipients ||
                    !step2Form.day ||
                    !step2Form.contact_information ||
                    !step2Form.address
                  }
                >
                  生成全文
                </Button>
              </div>
            </Card>
          </Flex>
        )}
        {/* {current === 2 && (
          <Flex justify="center">
            <Card style={{ width: 480 }}>
              <OutlineEditor
                outlineInfo={outlineContent}
                onChange={(v) =>
                  setOutlineContent(
                    typeof v === 'string' ? v : v ? String(v) : ''
                  )
                }
              />
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-around',
                  marginTop: 24,
                }}
              >
                <Button
                  onClick={() =>
                    handleGenerationStart(
                      '生成大纲',
                      undefined,
                      undefined,
                      undefined
                    )
                  }
                >
                  换个大纲
                </Button>
                <Button onClick={handlePrev}>返回上一步</Button>
                <Button
                  type="primary"
                  onClick={() => handleNext('生成报告', outlineContent)}
                >
                  撰写报告
                </Button>
              </div>
            </Card>
          </Flex>
        )} */}
        {current === 2 && (
          <Flex justify="center">
            <Card style={{ width: '80vw' }}>
              <div style={{ display: 'flex', gap: 24 }}>
                <Step4Report
                  reportText={step4Message}
                  baseInfo={{
                    email_reason: step2Form.email_reason,
                    Content_email: step2Form.Content_email,
                    addresser: step2Form.addresser,
                    addressee: step2Form.addressee,
                    carbon_copy_recipients: step2Form.carbon_copy_recipients,
                    day: step2Form.day,
                    contact_information: step2Form.contact_information,
                    address: step2Form.address,
                    title: title,
                  }}
                  outlineInfo={step3Message}
                />
                  {dependency !== 'shanghuawei' && (
                  <OptimizationOptions options={optimizationOptionsData} onSelect={handleJump} />
                )}
              </div>

              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-around',
                  marginTop: 24,
                }}
              >
                <Button onClick={handlePrev}>返回上一步</Button>
                <Button
                  onClick={() =>
                    handleGenerationStart('生成报告', undefined, undefined, '')
                  }
                >
                  重新生成
                </Button>
                <Button type="primary" onClick={() => handleNext('生成文件')}>
                  下载文件
                </Button>
              </div>
            </Card>
          </Flex>
        )}
        {/* 预留后续步骤 */}
        {/* {current > 0 && (
          <div style={{ textAlign: 'center', marginTop: 80 }}>
            <Typography.Text type="secondary">
              后续步骤开发中...
            </Typography.Text>
          </div>
        )} */}
      </div>
    </div>
  )
}
export default EmailReport
