import React from 'react'
import { Input, Button, Typography, Space, Tooltip } from 'antd'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons'

const fields = [
  {
    key: 'white_background',
    label: '写作背景',
    placeholder:
      '输入写作背景，例如：在过去一年大学生村官工作中，我有以下心得体会',
  },
  {
    key: 'avizandum',
    label: '个人思考',
    placeholder:
      '经过此次学习，所提出自己的思考，如：一、制定详细的学习计划，系统提升纪检监察专业知识；二、积极参与实践锻炼，提高案件处理能力；三、加强与同事的交流协作；四、定期反思工作表现并及时改进不足之处；五、努力将所学知识运用到实际工作中去等等。',
  },
  {
    key: 'synopsis',
    label: '内容摘要',
    placeholder: '本次学习或会议的主要内容',
  },
  {
    key: 'future_plan',
    label: '未来计划',
    placeholder:
      '一、制定详细的学习计划，系统提升专业知识；二、积极参与实践锻炼，提高事情处理能力；三、加强与同事的交流协作；四、定期反思工作表现并及时改进不足之处；五、努力将所学知识运用到实际工作中去等等。',
  },
]

interface Step2BasicInfoProps {
  value: Record<string, string>
  onChange: (v: Record<string, string>) => void
  onRemove?: (key: string) => void
  onAIClick?: (key: keyof Step2BasicInfoProps['value'], label: string) => void
}

const Step2BasicInfo: React.FC<Step2BasicInfoProps> = ({
  value,
  onChange,
  onRemove,
  onAIClick,
}) => {
  const handleInput = (key: string, val: string) => {
    onChange({ ...value, [key]: val })
  }
  return (
    <div>
      {fields.map((f) => (
        <div
          key={f.key}
          style={{
            marginBottom: 20,
            borderRadius: 8,
            padding: 12,
          }}
        >
          <div
            style={{ display: 'flex', alignItems: 'center', marginBottom: 6 }}
          >
            <span style={{ fontWeight: 500 }}>{f.label}</span>
            <span style={{ color: 'red', marginLeft: 4 }}>*</span>
            <Space size={8} style={{ marginLeft: 'auto' }}>
              <Tooltip title="AI帮写">
                <Button
                  type="link"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() =>
                    onAIClick?.(
                      f.key as keyof Step2BasicInfoProps['value'],
                      f.label
                    )
                  }
                >
                  AI帮写
                </Button>
              </Tooltip>
              <Tooltip title="删除">
                <Button
                  type="link"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={() => onRemove?.(f.key)}
                />
              </Tooltip>
            </Space>
          </div>
          <Input.TextArea
            placeholder={f.placeholder}
            value={value[f.key] || ''}
            onChange={(e) => handleInput(f.key, e.target.value)}
            maxLength={800}
            rows={3}
            showCount
          />
        </div>
      ))}
    </div>
  )
}

export default Step2BasicInfo
