import React from 'react'
import { <PERSON>, But<PERSON>, Typography, message } from 'antd'
import './Step4Report.less'
import StreamTypewriter from '@/component/StreamTypewriter'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import RemarkBreaks from 'remark-breaks'
import RemarkMath from 'remark-math'
import rehypeRaw from 'rehype-raw'

interface Step4ReportProps {
  reportText: string
  baseInfo: {
    white_background: string
    avizandum: string
    synopsis: string
    future_plan: string
    title: string
    report_type: string
  }
  outlineInfo: string
}

const Step4Report: React.FC<Step4ReportProps> = ({
  reportText,
  baseInfo,
  outlineInfo,
}) => {
  const handleCopy = () => {
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard
        .writeText(reportText)
        .then(() => message.success('已复制到剪贴板'))
        .catch(() => fallbackCopyTextToClipboard(reportText))
    } else {
      fallbackCopyTextToClipboard(reportText)
    }
  }

  function fallbackCopyTextToClipboard(text: string) {
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.top = '0'
    textArea.style.left = '0'
    textArea.style.width = '2em'
    textArea.style.height = '2em'
    textArea.style.padding = '0'
    textArea.style.border = 'none'
    textArea.style.outline = 'none'
    textArea.style.boxShadow = 'none'
    textArea.style.background = 'transparent'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    try {
      const successful = document.execCommand('copy')
      if (successful) {
        message.success('已复制到剪贴板')
      } else {
        message.error('复制失败，请手动复制')
      }
    } catch (err) {
      message.error('复制失败，请手动复制')
    }
    document.body.removeChild(textArea)
  }

  const scrollRef = React.useRef<HTMLDivElement>(null)
  // 只在 reportText 为空时重置 key，避免每次内容变化都重头渲染
  const [typewriterKey, setTypewriterKey] = React.useState(0)
  React.useEffect(() => {
    if (!reportText) setTypewriterKey((k) => k + 1)
  }, [reportText])

  return (
    <div className="step4-report-wrapper">
      <div className="step4-report-left">
        <Card ref={scrollRef} style={{ height: '100%', overflow: 'auto' }}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: 12,
            }}
          >
            <Typography.Text strong>
              根据您提供的信息内容，撰写出以下内容：
            </Typography.Text>
            <Button type="link" onClick={handleCopy}>
              复制全文
            </Button>
          </div>
          {/* <pre
            className="report-text"
            style={{
              whiteSpace: 'pre-wrap',
              fontFamily: 'inherit',
              fontSize: 15,
            }}
          >
            {reportText}
          </pre> */}

          <StreamTypewriter
            key={typewriterKey}
            text={reportText}
            onchange={() => {
              scrollRef.current?.scrollTo({
                top: scrollRef.current.scrollHeight,
                behavior: 'smooth',
              })
            }}
          />

          {/* <ReactMarkdown
            className="markdown-body step3Analysis-container"
            remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}
            rehypePlugins={[rehypeRaw]}
          >
            {reportText}
          </ReactMarkdown> */}
        </Card>
      </div>
      <div className="step4-report-right">
        <Card style={{ marginBottom: 16 }}>
          <Typography.Title level={5}>基础信息</Typography.Title>
          <div style={{ fontSize: 14 }}>
            <div>
              <b>心得类别</b>
              <div style={{ whiteSpace: 'pre-wrap', marginBottom: 8 }}>
                {baseInfo.report_type}
              </div>
            </div>
            <div>
              <b>心得体会标题</b>
              <div style={{ whiteSpace: 'pre-wrap', marginBottom: 8 }}>
                {baseInfo.title}
              </div>
            </div>
            <div>
              <b>写作背景</b>
              <div style={{ whiteSpace: 'pre-wrap', marginBottom: 8 }}>
                {baseInfo.white_background}
              </div>
            </div>
            <div>
              <b>个人思考</b>
              <div style={{ whiteSpace: 'pre-wrap', marginBottom: 8 }}>
                {baseInfo.avizandum}
              </div>
            </div>
            <div>
              <b>内容摘要</b>
              <div style={{ whiteSpace: 'pre-wrap', marginBottom: 8 }}>
                {baseInfo.synopsis}
              </div>
            </div>
            <div>
              <b>未来计划</b>
              <div style={{ whiteSpace: 'pre-wrap' }}>
                {baseInfo.future_plan}
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}

export default Step4Report
