.outline-row {
  display: flex;
  align-items: center;
  margin: 4px 0;
  min-height: 36px;
  position: relative;
  background: #fff;
  transition: background 0.2s;
}

.outline-row.dragging {
  background: #e6f7ff;
  opacity: 0.7;
  z-index: 999;
}

.outline-drag-handle {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  cursor: grab;
  user-select: none;
}

.outline-content {
  flex: 1;
  min-height: 36px;
  display: flex;
  align-items: center;
}

.outline-text {
  cursor: pointer;
  padding-left: 8px;
  word-break: break-all;
}
