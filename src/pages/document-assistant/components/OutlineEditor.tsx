import React, { useState, useEffect, useRef, useCallback } from 'react'
import { DndContext, DragOverlay, closestCenter } from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Button, Input, Space } from 'antd'
import {
  PlusOutlined,
  DeleteOutlined,
  EnterOutlined,
  MenuOutlined,
} from '@ant-design/icons'
import './OutlineEditor.less'

// 可拖拽的条目组件
const SortableItem = ({
  id,
  text,
  level,
  onUpdate,
  onDelete,
  onIndent,
  onAddChild,
  editingId,
  setEditingId,
}: {
  id: string
  text: string
  level: number
  onUpdate: (id: string, value: string) => void
  onDelete: (id: string) => void
  onIndent: (id: string, action: 'indent' | 'outdent') => void
  onAddChild: (id: string) => void
  editingId: string | null
  setEditingId: (id: string | null) => void
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id })

  const [editValue, setEditValue] = React.useState(text)

  // 只在 editingId 变化或 text 变化时更新 editValue
  React.useEffect(() => {
    if (editingId === id) {
      setEditValue(text)
    }
  }, [editingId, id, text])

  const handleBlur = React.useCallback(() => {
    if (editValue !== text) {
      onUpdate(id, editValue)
    }
    setEditingId(null)
  }, [editValue, id, onUpdate, setEditingId, text])

  const handleKeyPress = React.useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        handleBlur()
      }
    },
    [handleBlur]
  )

  return (
    <div
      ref={setNodeRef}
      className={`outline-row${isDragging ? ' dragging' : ''}`}
      style={{
        transform: CSS.Transform.toString(transform),
        transition,
        zIndex: isDragging ? 999 : undefined,
      }}
      {...attributes}
    >
      {/* 拖拽手柄，绝对定位在最左侧 */}
      <span className="outline-drag-handle" {...listeners} title="拖拽排序">
        <MenuOutlined />
      </span>
      {/* 内容区，paddingLeft 控制缩进，留出手柄宽度 */}
      <div className="outline-content" style={{ paddingLeft: 24 + level * 24 }}>
        {editingId === id ? (
          <Input
            value={editValue}
            autoFocus
            onChange={(e) => setEditValue(e.target.value)}
            onBlur={handleBlur}
            onPressEnter={handleKeyPress}
            // bordered={false}
          />
        ) : (
          <span className="outline-text" onClick={() => setEditingId(id)}>
            {text}
          </span>
        )}
      </div>
      <Space>
        <Button
          icon={<PlusOutlined />}
          size="small"
          onClick={() => onAddChild(id)}
          title="添加子节点"
        />
        {/* <Button
          icon={<EnterOutlined rotate={90} />}
          size="small"
          onClick={() => onIndent(id, 'indent')}
          disabled={level >= 3}
          title="增加缩进"
        />
        <Button
          icon={<EnterOutlined rotate={270} />}
          size="small"
          onClick={() => onIndent(id, 'outdent')}
          disabled={level <= 0}
          title="减少缩进"
        /> */}
        <Button
          icon={<DeleteOutlined />}
          size="small"
          danger
          onClick={() => onDelete(id)}
          title="删除"
        />
      </Space>
    </div>
  )
}

// 将Markdown解析为结构化数据
const parseMarkdownToOutline = (markdown: string) => {
  const lines = markdown.split('\n').filter((line) => line.trim())
  let outline: any[] = []
  let stack: any[] = []

  lines.forEach((line) => {
    const match = line.match(/^(#+)\s*(.*)/)
    if (match) {
      const level = match[1].length - 1
      const text = match[2]

      const item = {
        id: `id-${Date.now()}-${Math.random()}`,
        text,
        level,
      }

      // 根据层级关系构建树形结构
      while (stack.length > 0 && stack[stack.length - 1].level >= level) {
        stack.pop()
      }

      if (stack.length > 0) {
        if (!stack[stack.length - 1].children) {
          stack[stack.length - 1].children = []
        }
        stack[stack.length - 1].children.push(item)
      } else {
        outline.push(item)
      }

      stack.push(item)
    }
  })

  // 扁平化处理方便拖拽排序
  const flattenOutline = (items: any[], result: any[] = []) => {
    items.forEach((item) => {
      result.push({ id: item.id, text: item.text, level: item.level })
      if (item.children) {
        flattenOutline(item.children, result)
      }
    })
    return result
  }

  return flattenOutline(outline)
}

// 将结构化数据转回Markdown
const outlineToMarkdown = (
  items: { id: string; text: string; level: number }[]
) => {
  return items
    .map((item) => {
      const prefix = '#'.repeat(item.level + 1)
      return `${prefix} ${item.text}`
    })
    .join('\n')
}

interface OutlineEditorProps {
  outlineInfo?: string
  onChange?: (outline: string) => void
}

const OutlineEditor: React.FC<OutlineEditorProps> = React.memo(
  ({ outlineInfo, onChange }) => {
    const [items, setItems] = useState(() =>
      parseMarkdownToOutline(outlineInfo || '')
    )
    const [activeId, setActiveId] = useState<string | null>(null)
    const [editingId, setEditingId] = useState<string | null>(null)
    const prevOutlineInfo = useRef<string | undefined>(outlineInfo)
    const prevItems = useRef<string>('')

    // 只在外部大纲变化时重置内容
    useEffect(() => {
      if (
        outlineInfo !== undefined &&
        outlineInfo !== prevOutlineInfo.current
      ) {
        setItems(parseMarkdownToOutline(outlineInfo))
        prevOutlineInfo.current = outlineInfo
      }
    }, [outlineInfo])

    // 处理 items 变化
    const handleItemsChange = useCallback(
      (newItems: typeof items) => {
        setItems(newItems)
        const markdown = outlineToMarkdown(newItems)
        if (markdown !== prevItems.current && markdown !== outlineInfo) {
          prevItems.current = markdown
          onChange?.(markdown)
        }
      },
      [onChange, outlineInfo]
    )

    // 拖拽结束处理
    const handleDragEnd = useCallback(
      (event: any) => {
        const { active, over } = event
        setActiveId(null)
        if (!over || active.id === over.id) return

        const oldIndex = items.findIndex((item) => item.id === active.id)
        const newIndex = items.findIndex((item) => item.id === over.id)
        const newItems = arrayMove(items, oldIndex, newIndex)
        handleItemsChange(newItems)
      },
      [items, handleItemsChange]
    )

    // 更新条目文本
    const handleUpdateItem = useCallback(
      (id: string, newText: string) => {
        const newItems = items.map((item) =>
          item.id === id ? { ...item, text: newText } : item
        )
        handleItemsChange(newItems)
      },
      [items, handleItemsChange]
    )

    // 删除条目
    const handleDeleteItem = useCallback(
      (id: string) => {
        const newItems = items.filter((item) => item.id !== id)
        handleItemsChange(newItems)
      },
      [items, handleItemsChange]
    )

    // 调整缩进（层级）
    const handleIndent = useCallback(
      (id: string, action: 'indent' | 'outdent') => {
        const newItems = items.map((item) => {
          if (item.id !== id) return item
          return {
            ...item,
            level:
              action === 'indent'
                ? Math.min(item.level + 1, 3)
                : Math.max(item.level - 1, 0),
          }
        })
        handleItemsChange(newItems)
      },
      [items, handleItemsChange]
    )

    // 添加子节点
    const handleAddChild = useCallback(
      (parentId: string) => {
        const parentIndex = items.findIndex((item) => item.id === parentId)
        if (parentIndex === -1) return

        const parentLevel = items[parentIndex].level
        const newItem = {
          id: `child-${Date.now()}`,
          text: '新子节点',
          level: parentLevel + 1,
        }

        let insertIndex = parentIndex + 1
        while (
          insertIndex < items.length &&
          items[insertIndex].level > parentLevel
        ) {
          insertIndex++
        }

        const newItems = [
          ...items.slice(0, insertIndex),
          newItem,
          ...items.slice(insertIndex),
        ]
        handleItemsChange(newItems)
      },
      [items, handleItemsChange]
    )

    return (
      <div style={{ maxWidth: 800, margin: '0 auto' }}>
        <DndContext
          collisionDetection={closestCenter}
          onDragStart={({ active }) => setActiveId(active.id as string)}
          onDragEnd={handleDragEnd}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            {/* <Button
            type="primary"
            onClick={handleExport}
            style={{ marginBottom: 16 }}
          >
            导出Markdown
          </Button> */}

            <div
              style={{
                border: '1px solid #d9d9d9',
                borderRadius: 4,
                padding: 16,
                background: '#fafafa',
              }}
            >
              <SortableContext
                items={items.map((item) => item.id)}
                strategy={verticalListSortingStrategy}
              >
                {items.map((item) => (
                  <SortableItem
                    key={item.id}
                    id={item.id}
                    text={item.text}
                    level={item.level}
                    onUpdate={handleUpdateItem}
                    onDelete={handleDeleteItem}
                    onIndent={handleIndent}
                    onAddChild={handleAddChild}
                    editingId={editingId}
                    setEditingId={setEditingId}
                  />
                ))}
              </SortableContext>
            </div>

            <DragOverlay>
              {activeId ? (
                <div
                  style={{
                    background: '#fff',
                    padding: '8px 16px',
                    borderRadius: 4,
                    boxShadow: '0 3px 10px rgba(0,0,0,0.1)',
                    border: '1px solid #1890ff',
                  }}
                >
                  {items.find((item) => item.id === activeId)?.text}
                </div>
              ) : null}
            </DragOverlay>
          </Space>
        </DndContext>
      </div>
    )
  }
)

export default OutlineEditor
