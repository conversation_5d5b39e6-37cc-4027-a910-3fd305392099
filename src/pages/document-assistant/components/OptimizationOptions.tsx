import React from 'react'
import { <PERSON><PERSON>, <PERSON>, Typography } from 'antd'

interface OptimizationOption {
  label: string
  description: string
  type: string // The value passed to the onSelect function
}

interface OptimizationOptionsProps {
  options: OptimizationOption[]
  onSelect: (type: string) => void // Handler for button clicks
}

const OptimizationOptions: React.FC<OptimizationOptionsProps> = ({
  options,
  onSelect,
}) => {
  return (
    <div style={{ maxWidth: '20%' }}>
      <Typography.Text type="secondary">
        您也可以选择进一步优化您的公文:
      </Typography.Text>
      {options.map((option) => (
        <Card style={{ marginTop: 10 }} key={option.type}>
          {' '}
          {/* Use type as key for uniqueness */}
          <Button
            type="link"
            onClick={() => onSelect(option.type)}
            style={{ fontSize: 16 }}
          >
            {option.label}
          </Button>
          <Typography.Paragraph type="secondary">
            {option.description}
          </Typography.Paragraph>
        </Card>
      ))}
    </div>
  )
}

export default OptimizationOptions
