import React, { useState } from 'react'
import { Card, Button, Flex } from 'antd'
import { useIsDesktop } from '@/hooks/useIsDeskTop'
import './index.less'

interface TagsProps {
  activeIndex: number
  onSelect: (index: number) => void
  contracts: any[]
}

const Tags: React.FC<TagsProps> = ({ activeIndex, onSelect, contracts }) => {
  const isDesktop = useIsDesktop()
  const [showButtons, setShowButtons] = useState(false)
  const scrollRef = React.useRef<HTMLDivElement>(null)
  const [baseWidth] = useState(isDesktop ? 400 : 250)

  const scroll = (direction: 'left' | 'right', step: number = 1) => {
    if (scrollRef.current) {
      const scrollAmount = direction === 'left' ? -baseWidth * step : baseWidth * step
      scrollRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' })
    }
  }

  const handleClick = (index: number) => {
    if (isDesktop) {
      scroll(index > activeIndex ? 'right' : 'left', index - activeIndex)
    }
    onSelect(index)
  }

  return (
    <div
      style={{ position: 'relative', overflowX: 'hidden', padding: 16, backgroundColor: '#EEEEEE' }}
      onMouseEnter={() => setShowButtons(true)}
      onMouseLeave={() => setShowButtons(false)}
    >
      <Flex
        ref={scrollRef}
        gap={16}
        style={{
          overflowX: contracts.length > 3 ? 'auto' : 'hidden', // 动态设置滚动
          whiteSpace: 'nowrap'
        }}
      >
        {contracts.map((contract, index) => (
          <div key={index} style={{ width: '50%', minWidth: baseWidth, display: 'inline-block' }}>
            <Card
              className={index === activeIndex ? 'actived' : ''}
              title={`合同情况`}
              onClick={() => handleClick(index)}
            >
              {Object.keys(contract).map((key, index) => (
                <p key={index}>
                  {key}: {contract[key]}
                </p>
              ))}
            </Card>
          </div>
        ))}
      </Flex>
      {((showButtons && contracts.length > 3) || !isDesktop) && ( // 长度大于3时才显示按钮
        <>
          <Button className='scroll-button left' onClick={() => scroll('left')}>
            &lt;
          </Button>
          <Button className='scroll-button right' onClick={() => scroll('right')}>
            &gt;
          </Button>
        </>
      )}
    </div>
  )
}

export default Tags
