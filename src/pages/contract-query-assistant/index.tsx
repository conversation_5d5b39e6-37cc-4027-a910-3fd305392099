import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Spin, Statistic, Pagination, message, Tag, Row, Col } from 'antd'
import { Card, Typography, Button } from 'antd'
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons'
import StreamTypewriter from '@/component/StreamTypewriter'
import { getListByTextSql, getCombineSqlWithCondition } from '@/api/template'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { dark } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { contractQueryAssistant } from '@/api/contractQueryAssistant'
import { extractContent, updateSqlLimit } from '@/utils/common'
import { NoData } from '@/component/NoData'
import GetKey from '@/component/getKey'
import Chart from './Chart'
import Tags from './Tags'
import './index.less'

const { Title, Paragraph } = Typography
const nameMap: any = {}

// 添加一个计算列数的函数
const getGridColumns = (fields: any[]): string => {
  const columnCount = fields.length || 1 // 如果没有字段，至少保持1列
  return `repeat(${columnCount}, 1fr)`
}

export const ContractQueryAssistant: React.FC = () => {
  const [key, setKey] = useState('')
  const [open, setOpen] = useState(false)
  const [thankEnd, setThankEnd] = useState(false)
  const [generating, setGenerating] = useState<boolean>(false)
  const [thankText, setThankText] = useState<string>('')
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [viewMode, setViewMode] = useState<'card' | 'list'>('card')
  const [selectedFields, setSelectedFields] = useState<Array<keyof any>>([])
  const [showFields, setShowFields] = useState<boolean>(false)
  const [filteredData, setFilteredData] = useState<any[]>([])
  const [summaryData, setSummaryData] = useState<any[]>([])
  const [graphics, setGraphics] = useState<any[] | null>(null)
  const fieldRef = useRef<HTMLDivElement>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(10)
  const [maxPage, setMaxPage] = useState(1)
  const [sqlList, setSqlList] = useState<string[]>([])
  const [detailsSql, setDetailsSql] = useState<string>('')
  const [cache, setCache] = useState<any>({}) // 缓存对象，key 为页码，value 为数据
  const [hasMore, setHasMore] = useState(true) // 是否还有更多数据
  const [activeIndex, setActiveIndex] = useState(0)
  const [cacheByDimension, setCacheByDimension] = useState<any>({}) // 缓存对象维度
  const [chartCurrent, setChartCurrent] = useState<any[]>([])
  const [chartSql, setChartSql] = useState<string>('')
  const [chartName, setChartName] = useState<string>('')
  const [graphicsSql, setGraphicsSql] = useState<string>('')
  // 点击外部关闭字段选择
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent): void => {
      if (fieldRef.current && !fieldRef.current.contains(e.target as Node)) {
        setShowFields(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const reset = () => {
    setFilteredData([])
    setSummaryData([])
    setThankText('')
    setCurrentPage(1)
    setSelectedFields([])
    setActiveIndex(0)
    setSqlList([])
    setMaxPage(1)
    setHasMore(true)
    setCache({})
    setGraphics([])
    setChartCurrent([])
    setHasMore(true)
  }

  const changeConditions = async (page: number, index?: number) => {
    if (sqlList[index || activeIndex]) {
      GetListByTextSql(page, sqlList[index || activeIndex], index || activeIndex)
    } else {
      GetSqlList(page, index || activeIndex)
    }
  }

  const GetSqlList = async (page: number, index: number) => {
    if (!detailsSql || !graphicsSql) {
      message.error('获取详情sql和图形sql失败，请重新查询')
      return
    }
    setGenerating(true)
    getCombineSqlWithCondition({
      whereSql: detailsSql,
      groupBySql: graphicsSql,
      additionalCondition: summaryData[index]['维度'] || summaryData[index]['月份'] || summaryData[index]['年份']
    })
      .then(res => {
        setGenerating(false)
        let arr = sqlList.map((x: string, i: number) => (i === index ? res.data : x))
        setSqlList(arr)
        GetListByTextSql(page, arr[index])
      })
      .catch(error => {
        setGenerating(false)
        message.error(error.message)
      })
  }

  const GetListByTextSql = async (page: number, sql?: string, index?: number) => {
    setGenerating(true)
    const res = await getListByTextSql(updateSqlLimit(sql || sqlList[index || activeIndex], page, pageSize))
    setGenerating(false)
    if (res.code !== 200) {
      message.error(res.message)
      return
    }
    // 将新数据存入缓存
    setCache((prevCache: any) => ({
      ...prevCache,
      [page]: res.data
    }))

    // 更新当前展示的数据
    setFilteredData(res.data || [])

    // 动态判断是否还有下一页
    if (res.data.length < pageSize) {
      setHasMore(false) // 没有更多数据
    } else {
      setHasMore(true) // 可能还有更多数据
    }
  }

  const handleGeneration = useCallback(
    async (query: string) => {
      if (!key) {
        setOpen(true)
        return
      }

      if (!query) {
        message.warning('请输入查询条件')
        return
      }
      setGenerating(true)
      reset()
      let res = ''
      let isThink = true
      setThankEnd(false)

      try {
        await contractQueryAssistant(
          {
            query,
            key
          },
          {
            onMessage: (text, finished) => {
              if (text) {
                res += text || ''
                if (text.includes('@')) {
                  isThink = false
                  setThankEnd(true)
                }
                if (isThink) {
                  setThankText(t => t + text)
                }
              }
              if (finished) {
                const errorStr = extractContent(res, 'error')
                if (errorStr) {
                  message.error(errorStr)
                  setKey('')
                  setOpen(true)
                  return
                }

                try {
                  const content = extractContent(res, 'details')
                  const detailsData = JSON.parse(`${content}`)
                  setFilteredData(detailsData || [])
                  if (detailsData.length < pageSize) {
                    setHasMore(false)
                  }

                  setCache((prevCache: any) => ({
                    ...prevCache,
                    [1]: detailsData
                  }))
                } catch (error) {
                  console.error('Error parsing data:', error)
                }

                try {
                  const detailsSql = extractContent(res, 'detailsSql')
                  setDetailsSql(detailsSql)
                  const content = extractContent(res, 'summary')
                  const summaryData = JSON.parse(`${content}`)
                  setSummaryData(summaryData)
                  if (summaryData.length > 1) {
                    setSqlList(summaryData.map(() => ''))
                  } else {
                    setSqlList([detailsSql])
                  }
                } catch (error) {
                  console.error('Error parsing data:', error)
                }

                try {
                  const graphics = extractContent(res, 'graphics')
                  const summaryData = JSON.parse(graphics)
                  setGraphics(summaryData)
                } catch (error) {
                  setGraphics(null)
                  console.error('Error parsing data:', error)
                }

                try {
                  const graphicsSql = extractContent(res, 'graphicsSql')
                  setGraphicsSql(graphicsSql)
                } catch (error) {
                  setGraphicsSql('')
                  console.error('Error parsing data:', error)
                }
              }
            },
            onError: () => {
              setGenerating(false)
            },
            onFinish: () => {
              setGenerating(false)
            }
          }
        )
      } catch (err) {
        setGenerating(false)
      }
    },
    [key]
  )

  // 字段选择处理
  const toggleField = (field: keyof any): void => {
    setSelectedFields(prev => (prev.includes(field) ? prev.filter(f => f !== field) : [...prev, field]))
  }

  // 处理分页器页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // 如果缓存中有该页数据，直接从缓存中取出
    if (cache[page]) {
      setFilteredData(cache[page] || [])
    } else {
      // 如果缓存中没有该页数据，调用接口获取
      changeConditions(page)
      setMaxPage(page)
    }
  }

  // 修改 renderContractItems 函数
  const renderContractItems = () => {
    let data = []
    if (chartCurrent.length > 0) {
      data = chartCurrent
    } else {
      data = filteredData || []
    }

    return (
      <>
        <div className={`contract-query-contract-list ${viewMode === 'list' ? 'list-view' : ''}`}>
          {data.map((contract, index) => {
            if (!contract) return null
            const availableFields = Object.keys(contract) as Array<keyof any>
            const fieldsToShow =
              selectedFields.length > 0
                ? availableFields.filter(field => selectedFields.includes(field))
                : availableFields

            return (
              <div key={index} className={`contract-query-contract-item ${viewMode === 'list' ? 'list-view' : ''}`}>
                {viewMode === 'card' ? (
                  <>
                    {fieldsToShow.map((field: any) => {
                      const fieldLabel = nameMap[field] || field
                      if (field === 'contract_amount') {
                        return (
                          <p key={field}>
                            {fieldLabel}：
                            <span className='contract-query-contract-amount'>¥{contract[field].toLocaleString()}</span>
                          </p>
                        )
                      }
                      if (field === 'contract_effective_date') {
                        return (
                          <p key={field}>
                            {fieldLabel}：{new Date(contract[field]).toLocaleDateString()}
                          </p>
                        )
                      }
                      return (
                        <p key={field}>
                          {fieldLabel}：{contract[field]}
                        </p>
                      )
                    })}
                  </>
                ) : (
                  <div
                    className='list-item-container'
                    style={{
                      gridTemplateColumns: getGridColumns(fieldsToShow)
                    }}
                  >
                    {fieldsToShow.map((field: any) => (
                      <div key={field} className='list-item-field'>
                        <span className='field-label'>{nameMap[field] || field}</span>
                        <span className='field-value'>
                          {field === 'contract_amount'
                            ? `¥${contract[field].toLocaleString()}`
                            : field === 'contract_effective_date'
                            ? new Date(contract[field]).toLocaleDateString()
                            : contract[field]}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )
          })}
        </div>
        {data.length > 0 && (
          <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'center' }}>
            <Pagination
              current={currentPage}
              onChange={
                chartSql
                  ? (page: number) => {
                      getChartData(page, chartSql)
                    }
                  : handlePageChange
              }
              total={hasMore ? maxPage * pageSize + 1 : maxPage * pageSize - 1} // 假设每页 10 条数据
              pageSize={pageSize}
              showSizeChanger={false}
            />
          </div>
        )}
      </>
    )
  }

  const [collapse, setCollapse] = useState(false)
  const handleCollapse = () => {
    // 处理收起逻辑
    setCollapse(!collapse)
  }

  const handleSelect = (index: number) => {
    if (cacheByDimension[index]) {
      // 根据维度缓存数据
      setCache(cacheByDimension[index]['data'])
      // 更新当前展示的数据
      setFilteredData(cacheByDimension[index]['data'][1] || [])
      setMaxPage(cacheByDimension[index]['maxPage'])
      setHasMore(cacheByDimension[index]['hasMore'])
    } else {
      setCacheByDimension({
        ...cacheByDimension,
        [activeIndex]: {
          data: cache,
          maxPage: maxPage,
          hasMore: hasMore
        }
      })
      setMaxPage(1) // 重置最大页码
      setHasMore(true) // 重置是否还有更多数据
      setCache({}) // 清空缓存
      changeConditions(1, index) // 重新获取数据
    }
    setCurrentPage(1) // 重置当前页码
    setActiveIndex(index) // 更新当前选中的维度索引
  }

  const [baseMaxPage, setBaseMaxPage] = useState(1)
  const handleChartClick = (date: string) => {
    setChartName(date)
    setBaseMaxPage(maxPage)
    setGenerating(true)
    setHasMore(true)
    getCombineSqlWithCondition({
      whereSql: detailsSql,
      groupBySql: graphicsSql,
      additionalCondition: date
    }).then(res => {
      setGenerating(false)
      setChartSql(res.data)
      getChartData(1, res.data, 1)
    })
  }

  const getChartData = async (page: number, sql: string, baseMaxPage?: number) => {
    setCurrentPage(page)
    setMaxPage(baseMaxPage || (page > maxPage ? page : maxPage))
    setGenerating(true)
    const res = await getListByTextSql(updateSqlLimit(sql, page, pageSize))
    setGenerating(false)
    if (res.code === 200) {
      setChartCurrent(res.data)
      if (res.data.length < pageSize) {
        setHasMore(false)
      }
    }
  }

  const closeChart = () => {
    setChartCurrent([])
    setMaxPage(baseMaxPage)
    setChartName('')
    setChartSql('')
    setHasMore(true)
    setCurrentPage(1)
  }

  return (
    <>
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      <GetKey open={open} onClose={setOpen} onChange={setKey} />
      <div className='contract-query-assistant'>
        <h1>智能合同查询</h1>

        <div className='contract-query-search-container'>
          <input
            type='text'
            value={searchTerm}
            onKeyDown={e => {
              if (e.key === 'Enter') {
                handleGeneration(searchTerm)
              }
            }}
            onChange={e => setSearchTerm(e.target.value)}
            placeholder='请输入合同查询需求，例如：2024年签订合同金额情况'
          />
          <button onClick={() => handleGeneration(searchTerm)}>搜索</button>
        </div>
        {/* 思考过程区域 */}
        {thankText && (
          <Card className='thinking-card'>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={4} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  fill='none'
                  viewBox='0 0 24 24'
                  stroke='currentColor'
                  style={{ marginRight: 8, width: 20, height: 20, color: '#1890ff' }}
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth='2'
                    d='M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z'
                  />
                </svg>
                查询思考过程
              </Title>
              <Button
                type='link'
                onClick={handleCollapse}
                icon={collapse ? <CaretDownOutlined /> : <CaretUpOutlined />}
              >
                {collapse ? '展开' : '收起'}
              </Button>
            </div>
            <Paragraph
              style={{
                marginTop: 16,
                backgroundColor: '#f5f5f5',
                padding: '8px',
                borderRadius: '4px',
                color: 'rgb(55, 65, 81)',
                opacity: !collapse ? 1 : 0,
                height: !collapse ? 'auto' : 0,
                transition: 'height 0.3s ease-in-out',
                overflow: 'hidden'
              }}
            >
              <StreamTypewriter
                text={thankText}
                end={thankEnd}
                components={{
                  sql: ({ node, ...props }: any) => (
                    <SyntaxHighlighter
                      {...props}
                      language='sql'
                      showLineNumbers={true}
                      style={dark}
                      customStyle={{
                        border: 'none',
                        margin: '0'
                      }}
                    >
                      {typeof props.children === 'string'
                        ? props.children.replace(/\n$/, '')
                        : JSON.stringify(props.children)}
                    </SyntaxHighlighter>
                  )
                }}
              ></StreamTypewriter>
            </Paragraph>
          </Card>
        )}
        {summaryData.length > 1 && <Tags activeIndex={activeIndex} onSelect={handleSelect} contracts={summaryData} />}

        {(filteredData || []).length > 0 && (
          <div style={{ background: 'transparent', margin: '20px 0' }}>
            <Row gutter={[16, 16]}>
              {summaryData.length === 1 && (
                <Col xs={24} md={6}>
                  <Row gutter={[22, 22]} wrap>
                    <Col span={24}>
                      <Card style={{ height: '100%' }}>
                        <Statistic title='合同总数' value={summaryData[0]['总合同数']} />
                      </Card>
                    </Col>
                    <Col span={24}>
                      <Card style={{ height: '100%' }}>
                        <Statistic
                          title='总合同金额'
                          value={`${summaryData[0]['总合同金额'] || summaryData[0]['总合同金额（万）']} 万`}
                          prefix='¥'
                          className='green-statistic'
                        />
                      </Card>
                    </Col>
                  </Row>
                </Col>
              )}
              <Col xs={24} md={18}>
                {graphics && <Chart title='合同金额分布情况' data={graphics} onChartClick={handleChartClick} />}
              </Col>
            </Row>
          </div>
        )}

        {(filteredData || []).length > 0 && (
          <>
            <div className='contract-query-controls'>
              <div className='contract-query-field-selection' ref={fieldRef}>
                <button onClick={() => setShowFields(!showFields)}>自定义展示</button>
                {chartName && (
                  <Tag bordered={false} color='processing' style={{ marginLeft: 16 }} closable onClose={closeChart}>
                    {chartName}
                  </Tag>
                )}
                <div className={`dropdown-content ${showFields ? 'show' : ''}`}>
                  {Object.keys(filteredData[0] || {}).map(field => (
                    <label key={field}>
                      <input
                        type='checkbox'
                        checked={selectedFields.includes(field as keyof any)}
                        onChange={() => toggleField(field as keyof any)}
                      />
                      {nameMap[field] || field}
                    </label>
                  ))}
                </div>
              </div>

              <div className='contract-query-view-toggle'>
                <button className={viewMode === 'card' ? 'active' : ''} onClick={() => setViewMode('card')}>
                  卡片视图
                </button>
                <button className={viewMode === 'list' ? 'active' : ''} onClick={() => setViewMode('list')}>
                  列表视图
                </button>
              </div>
            </div>

            <div className='contract-query-total-count'>共 {(filteredData || []).length} 条合同</div>
          </>
        )}
        {(filteredData || []).length === 0 && !thankText ? (
          <NoData description='未找到匹配的合同信息' marginTop='20px' />
        ) : (
          renderContractItems()
        )}

        <div style={{ height: '20px' }}></div>
      </div>
    </>
  )
}

export default ContractQueryAssistant
