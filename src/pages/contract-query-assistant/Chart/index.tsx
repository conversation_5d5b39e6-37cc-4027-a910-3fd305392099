import React, { useEffect, useState } from 'react'
import * as echarts from 'echarts'
import { Card, Select } from 'antd'

interface ChartProps {
  title: string
  data: any[]
  onChartClick?: (item: any) => void
}

const Chart: React.FC<ChartProps> = ({ title, data, onChartClick }) => {
  const [chartType, setChartType] = useState<'bar' | 'line'>('bar')

  useEffect(() => {
    const chartDom = document.getElementById('chart')
    const myChart = echarts.init(chartDom)

    // X轴数据（合同生效年月）
    const xAxisData = data.map(item => item['合同生效年月'] || item['合同生效月份'] || item['合同生效日期'])

    // Y轴数据（合同金额）
    const yAxisData = data.map(
      item => parseFloat(item['合同总金额'] || item['合同金额（万）'] || item['合同金额']) / 10000
    )

    // 鼠标移入时的提示框数据
    const tooltipFormatter = (params: any) => {
      const { dataIndex } = params[0]
      const item = data[dataIndex]
      return `
      合同生效年月: ${item['合同生效年月'] || item['合同生效月份'] || item['合同生效日期']}<br/>
      合同数量: ${item['合同数量']} 个<br/>
      合同金额: ${item['合同总金额'] || item['合同金额（万）'] || item['合同金额']}
    `
    }

    const option = {
      title: { text: title },
      grid: {
        left: '50px',
        bottom: '20px',
        right: '90px',
        top: '60px'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: tooltipFormatter // 自定义提示框内容
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        name: '合同生效年月'
      },
      yAxis: {
        type: 'value',
        name: '合同金额（万）'
      },
      series: [
        {
          name: '合同金额',
          type: chartType,
          data: yAxisData,
          itemStyle: { color: '#1890ff' },
          barMaxWidth: 30
        }
      ]
    }

    myChart.setOption(option)

    // 注册点击事件
    myChart.on('click', (params: any) => {
      const clickedItem = data[params.dataIndex]
      if (onChartClick) {
        onChartClick(clickedItem['合同生效年月'] || clickedItem['合同生效月份'] || clickedItem['合同生效日期'])
      }
    })

    return () => {
      myChart.dispose()
    }
  }, [chartType, title, data, onChartClick])

  return (
    <Card style={{ width: '100%', position: 'relative' }}>
      <Select
        defaultValue='bar'
        size='small'
        style={{ width: 80, position: 'absolute', top: 20, right: 20, zIndex: 1 }}
        onChange={value => setChartType(value as 'bar' | 'line')}
        options={[
          { value: 'bar', label: '柱状图' },
          { value: 'line', label: '折线图' }
        ]}
      />
      <div id='chart' style={{ width: '100%', height: 200 }}></div>
    </Card>
  )
}

export default Chart
