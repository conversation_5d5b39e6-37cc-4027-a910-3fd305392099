.personal-profile-page {
  background: #f3f5f7;
  min-height: 100vh;
  padding: 10px;

  .profile-title {
    background: #2986d9;
    color: #fff;
    font-size: 28px;
    font-weight: bold;
    text-align: center;
    border-radius: 12px 12px 0 0;
    padding: 10px 0;
    margin-bottom: 24px;
    letter-spacing: 2px;
  }

  .profile-grid {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
  }

  .profile-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    min-width: 320px;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 220px;
    width: 100%;
    .ant-card-head {
      color: #fff;
      background-color: #2986d9;
      font-size: 16px;
      min-height: 44px;
    }
  }

  .profile-list {
    margin: 0;
    padding-left: 18px;
  }

  .profile-upload-card {
    width: 100%;
    min-height: 180px;
    // display: flex;
    // align-items: center;
    // justify-content: center;
  }

  .profile-upload-dragger {
    // padding: 16px;
    width: 100%;
    .ant-upload {
      width: 100%;
    }
  }

  .profile-upload-center {
    text-align: center;
  }

  .profile-upload-icon {
    font-size: 32px;
    &.success {
      color: #52c41a;
    }
    &.default {
      color: #999;
    }
  }

  .profile-upload-filename {
    font-weight: 500;
    font-size: 16px;
  }

  .profile-upload-tip {
    color: #888;
    font-size: 12px;
  }
  .custom-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100%;
    background: rgba(0, 0, 0, 0.45);
    z-index: 2000;
  }
  // 去掉默认白色蒙层
  .ant-spin-blur {
    filter: none !important;
    opacity: 1 !important;
  } 
  .ant-spin-blur::after {
    opacity: 0!important;
  }
  .ant-spin {
    min-height: 545px!important;
    z-index: 9999!important;
  }
  .ant-spin-dot-spin {
    color: #fff!important;
  }
  .ant-spin-text {
    color: #fff!important;
    text-shadow:none!important;
  }

}



