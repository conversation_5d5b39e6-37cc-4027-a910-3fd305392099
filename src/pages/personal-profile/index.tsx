import React, { useState } from 'react'
import {
  Card,
  Row,
  Col,
  Typography,
  Spin,
  message,
  Upload,
  Button,
  Space,
} from 'antd'
import { InboxOutlined, CheckCircleFilled } from '@ant-design/icons'
import { personalProfile } from '@/api/personalProfile'
import { uploadFile } from '@/api/template.ts'
import StreamTypewriter from '@/component/StreamTypewriter'
import './index.less'

const { Title } = Typography
const PERSONAL_PROFILE_TOKEN =
  import.meta.env['VITE_PERSONAL_PROFILE_TOKEN'] || ''

// 递归渲染任意 JSON 数据
const renderValue = (value: any) => {
  if (Array.isArray(value)) {
    return (
      <ul className="profile-list">
        {value.map((item, idx) => (
          <li key={idx}>{renderValue(item)}</li>
        ))}
      </ul>
    )
  } else if (typeof value === 'object' && value !== null) {
    return (
      <ul className="profile-list">
        {Object.entries(value).map(([k, v]) => (
          <li key={k} style={{ marginBottom: 4 }}>
            <strong>{k}：</strong>
            {typeof v === 'object' ? renderValue(v) : String(v)}
          </li>
        ))}
      </ul>
    )
  } else {
    return <span>{String(value)}</span>
  }
}

// 示例 mock 数据
const sampleData = {
  基础信息: {
    姓名: '张三',
    性别: '男',
    出生日期: '1990-01-01',
    身份证号: '370000199001010000',
    联系方式: '13800000000',
    邮箱: '<EMAIL>',
    学历: '硕士研究生',
    毕业院校: '清华大学',
    专业: '计算机科学与技术',
    工作年限: '10年',
    现居住地: '北京市海淀区',
  },
  职业经历: [
    {
      公司: '字节跳动',
      职位: '高级前端工程师',
      起止时间: '2018-2023',
      主要职责: '负责前端架构设计与开发',
    },
    {
      公司: '百度',
      职位: '前端开发工程师',
      起止时间: '2015-2018',
      主要职责: '参与搜索产品前端开发',
    },
  ],
  技能与证书: {
    技术栈: ['React', 'TypeScript', 'Node.js', 'Ant Design'],
    证书: ['高级软件工程师', 'PMP'],
  },
  项目经验: [
    {
      项目名称: '企业画像系统',
      角色: '前端负责人',
      项目描述: '为企业客户提供数据可视化画像分析平台',
    },
    {
      项目名称: '智能合同审核平台',
      角色: '核心开发',
      项目描述: '基于AI的合同自动审核与风险提示系统',
    },
  ],
  荣誉与奖项: ['2022年度优秀员工', '2021技术创新奖'],
  个人评价: '工作认真负责，具备良好的团队协作与沟通能力，热爱技术创新。',
}

interface FileState {
  file: File | null
  name: string
  id: string | number
}

const PersonalProfile: React.FC<{ data?: any }> = ({ data: propData }) => {
  const [profileData, setProfileData] = useState<any>(propData || {})
  const [loading, setLoading] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<FileState[]>([])
  const [profileJsonData, setProfileJsonData] = useState<any>('')

  // 上传文件逻辑，支持多文件
  const beforeUpload = async (file: File) => {
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf('.') + 1)
      .toLocaleLowerCase()
    if (
      !['pdf', 'docx', 'xlsx', 'pptx', 'xls', 'csv', 'txt'].includes(
        originalFileExt
      )
    ) {
      message.error(
        '仅支持.docx, .pdf, .xlsx, .pptx, .xls, .csv, .txt类型的文件'
      )
      return false
    }
    message.loading({ content: '文件上传中...', key: 'uploading' })
    try {
      const response = await uploadFile(file, PERSONAL_PROFILE_TOKEN)
      if (response && response.id) {
        setUploadedFiles((prev) => [
          ...prev,
          { file, name: file.name, id: response.id },
        ])
        message.success({
          content: '文件上传成功',
          key: 'uploading',
          duration: 1,
        })
      } else {
        throw new Error('上传失败：未收到有效的响应')
      }
    } catch (error: any) {
      message.error({
        content: `文件上传失败: ${error.message}`,
        key: 'uploading',
        duration: 2,
      })
    }
    return false // 阻止自动上传
  }

  // 删除文件
  const handleDelete = (fileId: string | number) => {
    setUploadedFiles((prev) => prev.filter((file) => file.id !== fileId))
  }

  // 搜索逻辑，联调 personalProfile API，传递所有文件信息
  const handleSearch = async () => {
    if (uploadedFiles.length === 0) {
      message.warning('请先上传文件')
      return
    }
    setLoading(true)
    let accumulated = ''
    setProfileJsonData('')
    try {
      await personalProfile(
        {
          files: uploadedFiles.map((f) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: f.id,
          })),
        },
        {
          onMessage: (text, finished) => {
            if (text) {
              accumulated += text
              setProfileJsonData(accumulated)
            }
            if (finished) {
              setLoading(false)
              try {
                let cleaned = accumulated
                  .replace(/^```json\s*/, '')
                  .replace(/^```json\n/, '')
                  .replace(/^```json/, '')
                  .replace(/```\s*$/, '')
                  .replace(/```$/, '')
                  .trim()
                const json = JSON.parse(cleaned)
                setProfileData(json)
              } catch (e) {
                setProfileData({ 结果: accumulated })
              }
            }
          },
          onError: () => {
            setLoading(false)
            message.error('查询失败，请重试')
          },
          onFinish: () => {
            setLoading(false)
          },
        }
      )
    } catch (err) {
      setLoading(false)
      message.error('查询失败，请重试')
    }
  }

  // 分离所有一级 key
  const allKeys = Object.keys(profileData)

  return (
    <div className="personal-profile-page" style={{ position: 'relative' }}>
      {loading && <div className="custom-mask" />}
      <Spin tip="加载中..." spinning={loading} size="large">
        <div className="profile-title">个人画像</div>
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            marginBottom: 24,
            width: '100%',
          }}
        >
          <Card className="profile-upload-card">
            <Upload.Dragger
              className="profile-upload-dragger"
              showUploadList={false}
              multiple={true}
              beforeUpload={beforeUpload}
              accept=".docx,.pdf,.xlsx,.pptx,.xls,.csv,.txt"
            >
              <div className="profile-upload-center">
                <span
                  className={`profile-upload-icon ${
                    uploadedFiles.length > 0 ? 'success' : 'default'
                  }`}
                >
                  {uploadedFiles.length > 0 ? (
                    <CheckCircleFilled />
                  ) : (
                    <InboxOutlined />
                  )}
                </span>
                <br />
                <span className="profile-upload-filename">
                  {uploadedFiles.length > 0
                    ? uploadedFiles.map((file, idx) => (
                        <span
                          key={file.id}
                          style={{ display: 'inline-block', marginRight: 8 }}
                        >
                          {idx + 1}. {file.name}
                          <span
                            style={{
                              color: '#f5222d',
                              marginLeft: 4,
                              cursor: 'pointer',
                            }}
                            onClick={(e) => {
                              e.stopPropagation()
                              handleDelete(file.id)
                            }}
                            title="删除"
                          >
                            ×
                          </span>
                        </span>
                      ))
                    : '点击或拖拽文件到此上传'}
                </span>
                <br />
                <span className="profile-upload-tip">
                  {uploadedFiles.length > 0
                    ? '点击或拖拽可继续上传'
                    : '支持.docx, .pdf, .xlsx, .pptx, .xls, .csv, .txt，可多文件'}
                </span>
              </div>
            </Upload.Dragger>
            <Button
              type="primary"
              className="profile-upload-btn"
              style={{ marginTop: 16, width: '100%' }}
              onClick={handleSearch}
              disabled={uploadedFiles.length === 0}
            >
              解析画像
            </Button>
          </Card>
        </div>
        {loading ? (
          <StreamTypewriter
            text={profileJsonData}
            // components={markdownComponents}
            end={!loading} // 添加这一行，当 loading 为 false 时表示输出结束
            // onchange={() => {
            //   scrollRef.current?.scrollTo({
            //     top: scrollRef.current.scrollHeight,
            //     behavior: 'smooth',
            //   })
            // }}
          />
        ) : (
          <Row gutter={[24, 24]} className="profile-grid">
            {allKeys.map((section) => (
              <Col
                xs={24}
                sm={24}
                md={12}
                lg={8}
                xl={8}
                key={section}
                style={{ display: 'flex' }}
              >
                <Card
                  className="profile-card"
                  bordered={false}
                  title={section}
                  style={{ width: '100%' }}
                >
                  {renderValue(profileData[section])}
                </Card>
              </Col>
            ))}
          </Row>
        )}
      </Spin>
    </div>
  )
}

export default PersonalProfile
