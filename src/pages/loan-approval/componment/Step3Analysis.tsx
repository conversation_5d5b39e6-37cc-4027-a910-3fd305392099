import { Card, message } from 'antd'
import { useEffect, useState } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import RemarkBreaks from 'remark-breaks'
import RemarkMath from 'remark-math'
import './Step3Analysis.less'
import { extractAllTagContents } from '@/utils/common'

interface ContentItem {
  [key: string]: any
}
const renderContent = (content: ContentItem | string, depth: number = 0) => {
  if (typeof content === 'string') {
    return (
      <ReactMarkdown className='markdown-body' remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}>
        {content}
      </ReactMarkdown>
    )
  }

  return Object.keys(content).map((key: string, i: number) => {
    const value = content[key]
    const isNested = typeof value === 'object' && !Array.isArray(value)

    return (
      <div key={`${depth}-${i}`} style={{ marginLeft: `${depth * 20}px` }}>
        <div
          style={{
            fontSize: '16px',
            fontWeight: 'bold',
            marginTop: depth > 0 ? '10px' : '0'
          }}
          dangerouslySetInnerHTML={{ __html: key }}
        />
        {isNested ? (
          renderContent(value, depth + 1)
        ) : (
          <ReactMarkdown className='markdown-body' remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}>
            {String(value)}
          </ReactMarkdown>
        )}
      </div>
    )
  })
}

export const Step3Analysis: React.FC<{
  data: string
  isEnd: boolean
}> = ({ data, isEnd }) => {
  const [content, setContent] = useState<{ [key: string]: string }>({})
  useEffect(() => {
    if (isEnd) {
      let contentPro = {}
      try {
        const jsonstr = extractAllTagContents(data, 'json')
        contentPro = JSON.parse(jsonstr[0])
      } catch (error) {
        try {
          contentPro = JSON.parse(data)
        } catch (error) {
          contentPro = {}
        }
      }
      setContent(contentPro)
    }
  }, [isEnd, data])

  return (
    <Card title='处理结果' className='loan-approval-card'>
      {isEnd ? <>{renderContent(content)}</> : null}
    </Card>
  )
}

export default Step3Analysis
