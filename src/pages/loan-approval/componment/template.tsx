import React, { useState, useRef, useEffect } from 'react'
import { message, Flex, Card, Upload, Button, Typography, Tag } from 'antd'
import { uploadFile } from '@/api/template.ts'
import { InboxOutlined } from '@ant-design/icons'
import './template.less'

const token = import.meta.env['VITE_LOAN_APPROVAL_TOKEN'] || ''

interface TemplateProps {
  openTip: () => void
  onUpload: (file: { id: string; name: string }) => void
  onFileListChange: (uploadedFileList: { id: string; name: string }[]) => void
  generating: boolean
  setGenerating: (generating: boolean) => void
  start: () => void
}
export const Template: React.FC<TemplateProps> = ({
  openTip,
  onUpload,
  onFileListChange,
  generating,
  setGenerating,
  start
}) => {
  const [messageApi, contextHolder] = message.useMessage()

  const [uploadedFile, setUploadedFile] = useState<{ id: string; name: string }>({ id: '', name: '' })
  const [uploadedFileList, setUploadedFileList] = useState<{ id: string; name: string }[]>([])
  const allowedFileExtensions = (type: string): string[] => {
    if (type === '1') {
      return ['docx', 'pdf']
    }
    return ['txt', 'docx', 'mdx', 'pdf', 'html', 'xlsx', 'xls', 'csv', 'md', 'htm'] // 允许全部格式
  }
  const beforeUpload = (file: File, type: string) => {
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)?.toLowerCase()
    if (!allowedFileExtensions(type).includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'error',
        content: `文件格式不正确，请上传${allowedFileExtensions(type).join(',')}格式的文件`,
        duration: 2
      })
      return false
    }
    messageApi.open({
      key: 'uploading',
      type: 'loading',
      content: '文件上传中'
    })
    setGenerating(true)
    uploadFile(file, token)
      .then(response => {
        setGenerating(false)
        if (response && response.id) {
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
          if (type === '1') {
            setUploadedFile(response)
            onUpload(response)
          } else if (type === '2') {
            setUploadedFileList(prevList => [...prevList, response])
          }
          return false // 阻止自动上传
        } else {
          throw new Error('上传失败：未收到有效的响应')
        }
      })
      .catch(error => {
        setGenerating(false)
        messageApi.open({
          key: 'uploading',
          type: 'error',
          content: `文件上传失败: ${error.message}`,
          duration: 2
        })
      })
    return false // 阻止自动上传
  }

  useEffect(() => {
    onFileListChange(uploadedFileList)
  }, [uploadedFileList])

  return (
    <>
      {contextHolder}
      <Flex gap='large'>
        <Card className='loan-approval-form'>
          <Typography.Title level={2} className='page-title'>
            贷款审批报告生成
          </Typography.Title>
          <Typography.Text type='secondary' className='page-description'>
            你好，作为资深银行贷款审批专家，我可以结合您上传的资料和模板，为您预估贷款风险，生成贷款审查报告。
          </Typography.Text>
          <Typography.Title level={5} style={{ padding: '10px 0', margin: 0 }}>
            上传一份模板文件
          </Typography.Title>
          <Upload.Dragger
            showUploadList={false}
            multiple={false}
            beforeUpload={(file: File) => beforeUpload(file, '1')}
          >
            <p className='reporting-upload-drag-icon'>
              <InboxOutlined />
            </p>
            <p>请拖拽文件到此处或点击上传文件按钮</p>
            <p>支持 pdf、docx 格式</p>
          </Upload.Dragger>
          <Typography.Title level={5} style={{ padding: '10px 0', margin: 0 }}>
            上传一份或多份资料文件
          </Typography.Title>
          <Upload.Dragger showUploadList={false} multiple beforeUpload={(file: File) => beforeUpload(file, '2')}>
            <p className='reporting-upload-drag-icon'>
              <InboxOutlined />
            </p>
            <p>请拖拽文件到此处或点击上传文件按钮</p>
            <p>您可以上传贷款申请表、个人征信报告、抵押物估值报告等贷款审批材料。</p>
            <p>支持 docx, xlsx, txt, mdx, pdf, html, xls, csv, md, htm 格式</p>
          </Upload.Dragger>
          {uploadedFileList.length > 0 && (
            <>
              <p style={{ padding: '4px 0' }}>贷款材料：</p>
              {uploadedFileList.map(x => (
                <p key={x.id}>
                  <Tag
                    closeIcon
                    style={{ marginTop: 4 }}
                    onClose={() => {
                      setUploadedFileList(prevList => prevList.filter(y => y.id !== x.id))
                      return false
                    }}
                  >
                    {x.name}
                  </Tag>
                </p>
              ))}
            </>
          )}
          <div>
            {uploadedFile.id && (
              <>
                <p style={{ padding: '4px 0' }}>模板文件：</p>
                <Tag
                  closeIcon
                  onClose={() => {
                    setUploadedFile({ id: '', name: '' })
                    return false
                  }}
                >
                  {uploadedFile.name}
                </Tag>
              </>
            )}
          </div>
          <Button
            size='large'
            type='primary'
            style={{ width: '100%', marginTop: 20 }}
            disabled={!uploadedFile || generating}
            onClick={start}
          >
            模 板 解 析
          </Button>
        </Card>
      </Flex>
    </>
  )
}

export default Template
