import React, { useCallback, useState } from 'react'
import { Spin, message, Flex, Typography, Steps, Button } from 'antd'
// import GetKey from '@/component/getKey'
import { Template } from './componment/template'
import Step2BasicInfo from './componment/Step2BasicInfo'
import Step3Analysis from './componment/Step3Analysis'
import { loanApproval } from '@/api/loanApproval'
import { extractJSONFromString } from '@/utils/json-extractor'
import { extractAllTagContents, extractContent } from '@/utils/common'
import './index.less'

const { Step } = Steps

export const LoanApproval: React.FC = () => {
  const [key, setKey] = useState('')
  const [open, setOpen] = useState(false)
  const [messageApi, contextHolder] = message.useMessage()
  const [step3message, setStep3message] = useState<string>('')
  const [generating, setGenerating] = useState<boolean>(false)
  const [isEnd, setIsEnd] = useState<boolean>(false)
  const [templateData1, setTemplateData1] = useState<string>('')
  const [templateData2, setTemplateData2] = useState<string>('')
  const [uploadedFile, setUploadedFile] = useState<{ id: string; name: string }>({ id: '', name: '' })
  const [uploadedFileList, setUploadedFileList] = useState<{ id: string; name: string }[]>([])
  const [current, setCurrent] = useState(0)

  const steps = [
    {
      title: '上传资料',
      content: (
        <Template
          openTip={() => setOpen(true)}
          onUpload={file => {
            setUploadedFile(file)
          }}
          onFileListChange={(list: any[]) => {
            setUploadedFileList(list)
          }}
          generating={generating}
          setGenerating={setGenerating}
          start={() => {
            start()
          }}
        />
      )
    },
    {
      title: '审批材料解析',
      content: <Step2BasicInfo data={templateData1} generating={generating} />
    },
    { title: '报告下载', content: <Step3Analysis data={step3message} isEnd={isEnd} /> }
  ]

  const handleGenerationStart = async () => {
    setStep3message('')
    setIsEnd(false)
    setGenerating(true)
    const res2 = extractJSONFromString(templateData2)
    let res = ''
    try {
      await loanApproval(
        {
          type: '贷款审批材料',
          Loan_approval_materials: uploadedFileList.map(item => ({
            upload_file_id: item.id,
            type: 'document',
            transfer_method: 'local_file'
          })),
          info: res2 || '{}'
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              setStep3message(p => p + message)
            }
            if (finished) {
              setIsEnd(true)
              setGenerating(false)
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                setKey('')
                setOpen(true)
                return
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  const nextStep = () => {
    // if (!key) {
    //   setOpen(true)
    //   return
    // }
    if (current === 1) {
      setCurrent(current + 1)
      if (step3message) return
      handleGenerationStart()
    } else {
      setCurrent(current + 1)
    }
  }

  const downloadReport = async () => {
    // if (!key) {
    //   setOpen(true)
    //   return
    // }
    setGenerating(true)
    const res2 = extractJSONFromString(templateData2)
    let res = ''
    const jsonstr = extractAllTagContents(step3message, 'json')
    try {
      await loanApproval(
        {
          type: '报告下载',
          info: res2 || '{}',
          key: key || '',
          files: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: uploadedFile?.id
            }
          ],
          query: jsonstr[0] || step3message
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
              // 提取()中的内容
              const parenthesesContent = res.match(/\((.*?)\)/)
              const parenthesesResult = parenthesesContent ? parenthesesContent[1] : null

              // 提取[]中的内容
              const squareBracketsContent = res.match(/\[(.*?)\]/)
              const squareBracketsResult = squareBracketsContent ? squareBracketsContent[1] : null

              if (parenthesesResult && squareBracketsResult) {
                const link = document.createElement('a')
                link.href = parenthesesResult
                link.download = `贷款审批报告${squareBracketsResult}`
                document.body.appendChild(link)
                link.click()
                link.remove()
              }
              setGenerating(false)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }
  const start = () => {
    handleAnalyzeTemplate()
  }

  const handleAnalyzeTemplate = useCallback(async () => {
    if (!uploadedFile.id || uploadedFileList.length === 0) {
      messageApi.open({
        key: 'uploading',
        type: 'error',
        content: '请先上传文件',
        duration: 2
      })
      return
    }
    setCurrent(1)
    setStep3message('')
    setTemplateData1('')
    setTemplateData2('')
    handleGenerationStart1()
    setGenerating(true)
    let res = ''
    try {
      await loanApproval(
        {
          type: '模板文件',
          template_type: '页面结构',
          key: key || '',
          template: {
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: uploadedFile.id
          }
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              setTemplateData1(res.replace(/^```markdown\s*|```|markdown$/g, ''))
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }, [uploadedFile, uploadedFileList])

  const handleGenerationStart1 = useCallback(async () => {
    let res = ''
    try {
      await loanApproval(
        {
          type: '模板文件',
          template_type: '模板结构',
          key: key || '',
          template: {
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: uploadedFile.id
          }
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              setTemplateData2(res)
              setGenerating(false)
            }
          },
          onError: () => {},
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }, [uploadedFile])

  return (
    <div className='loan-approval-container'>
      {contextHolder}
      {/* <GetKey open={open} onClose={setOpen} onChange={setKey} /> */}
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      <Flex className='toolbar' justify='center'>
        <Typography.Text className='title-text'>贷款审批报告生成</Typography.Text>
      </Flex>
      <div className='steps-container'>
        <Steps current={current} className='custom-steps'>
          {steps.map(item => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <div className='steps-content'>
          {steps.map((step, index) => (
            <div key={index} className={`step-content ${index === current ? 'active' : 'hidden'}`}>
              {step.content}
            </div>
          ))}
        </div>

        <div className='steps-action'>
          {current > 0 && (
            <Button
              style={{ marginRight: 8 }}
              onClick={() => {
                if (current === 1) {
                  handleAnalyzeTemplate()
                } else if (current === 2) {
                  handleGenerationStart()
                }
              }}
            >
              重新生成
            </Button>
          )}
          {current > 0 && (
            <Button style={{ marginRight: 8 }} onClick={() => setCurrent(current - 1)}>
              上一步
            </Button>
          )}
          {current < steps.length - 1 && current !== 0 && (
            <Button type='primary' onClick={nextStep}>
              下一步
            </Button>
          )}
          {current === steps.length - 1 && (
            <Button type='primary' onClick={() => downloadReport()}>
              下载报告
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

export default LoanApproval
