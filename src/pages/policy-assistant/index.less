.policy-search-bar {
  min-height: 100vh;
  background-color: #fff;

  .policy-header {
    background-color: #fff;
    padding: 20px 0;
  }

  .policy-container {
    .thinking-card {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
      }

      .markdown-body {
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .policy-item {
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
      }
    }
  }
}

// 覆盖 antd 默认样式
.ant-tabs-nav {
  &::before {
    border-bottom: none !important;
  }
}

.ant-card {
  border-radius: 8px;
}

.policy-search-bar .policy-container {
  height: calc(100vh - 195px);
  overflow: auto;
  width: 65vw;
  margin: 0 auto;
  max-width: 1200px;
}

.policy-search-bar .policy-container::-webkit-scrollbar {
  width: 0;
}
