import { useCallback, useRef, useState } from 'react'
import { Tabs, Input, Button, Switch, Card, Typography, Row, Col, Tag, Flex, Spin, message } from 'antd'
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons'
import ReactMarkdown from 'react-markdown'
import { policyQuery } from '@/api/PolicyAssistant'
import { extractContent } from '@/utils/common'
import GetKey from '@/component/getKey'
import './index.less'

const { Title, Paragraph, Text } = Typography
const { TabPane } = Tabs

export const PolicyAssistant = () => {
  const [key, setKey] = useState('')
  const [open, setOpen] = useState(false)
  const [type, setType] = useState<string>('1')
  const [searchText, setSearchText] = useState<string>('')
  const [title, setTitle] = useState<string>('')
  const [generating, setGenerating] = useState<boolean>(false)
  const [thankText1, setThankText1] = useState<string>('')
  const [thankText2, setThankText2] = useState<string>('')
  const [policyData1, setPolicyData1] = useState<PolicyItemProps[]>([])
  const [policyData2, setPolicyData2] = useState<PolicyItemProps[]>([])
  const [collapse1, setCollapse1] = useState(false)
  const [collapse2, setCollapse2] = useState(false)
  const [showThinking, setShowThinking] = useState(true)
  const errCount = useRef(0)

  const handleGeneration1 = useCallback(async () => {
    setGenerating(true)
    let responseMessage = ''

    try {
      await policyQuery(
        {
          type: type,
          query: searchText,
          key
        },
        {
          onMessage: (text, finished) => {
            if (text) {
              responseMessage += text
            }
            if (finished) {
              setGenerating(false)
              const errorStr = extractContent(responseMessage, 'error')
              if (errorStr) {
                errCount.current++
                if (errCount.current === 1) {
                  setKey('')
                  setOpen(true)
                  message.error(errorStr)
                }
                return
              }
              // 正则表达式获取<think>标签中的内容
              const regex = /<think>([\s\S]*?)<\/think>/
              const match = responseMessage.match(regex)

              if (match && match[1]) {
                const result = match[1].trim()
                if (type === '3') {
                  setThankText2(result)
                }
              } else {
                if (type === '3') {
                  setThankText2('')
                }
                console.error('没有找到<think>标签中的内容。')
              }

              // 正则表达式获取<think>标签之后的内容
              const regexArr = /<\/think>([\s\S]*)$/
              const matchArr = responseMessage.match(regexArr)

              if (matchArr && matchArr[1]) {
                // 去掉前面的空格或者换行，解析为数组的内容
                const jsonArrayString = matchArr[1].trim()
                // 尝试解析JSON
                try {
                  const contracts = JSON.parse(jsonArrayString)
                  if (contracts && contracts.length > 0) {
                    if (type === '3') {
                      setPolicyData2(contracts)
                    } else {
                      setPolicyData1(contracts)
                    }
                  }
                } catch (error) {
                  if (type === '3') {
                    setPolicyData2([])
                  } else {
                    setPolicyData1([])
                  }
                  console.error('JSON解析失败:', error)
                }
              } else {
                console.error('没有找到<think>标签后的JSON数组')
                if (type === '3') {
                  setPolicyData2([])
                } else {
                  setPolicyData1([])
                }
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }, [searchText, type, key])

  const handleGeneration2 = useCallback(async () => {
    setThankText1('')
    if (!showThinking || type === '3') {
      return
    }
    setGenerating(true)
    let responseMessage = ''

    try {
      await policyQuery(
        {
          type: 2,
          query: searchText,
          key
        },
        {
          onMessage: (text, finished) => {
            if (text) {
              responseMessage += text
            }
            if (finished) {
              setGenerating(false)
              const errorStr = extractContent(responseMessage, 'error')
              if (errorStr) {
                errCount.current++
                if (errCount.current === 1) {
                  setKey('')
                  setOpen(true)
                  message.error(errorStr)
                }
                return
              }
              setThankText1(responseMessage)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }, [searchText, showThinking, type, key])

  const handleCollapse1 = () => {
    // 处理收起逻辑
    setCollapse1(!collapse1)
  }

  const handleCollapse2 = () => {
    // 处理收起逻辑
    setCollapse2(!collapse2)
  }

  type PolicyItemProps = {
    title: string
    description: string
    source: { name: string; url: string }
    publishDate: string
    tags: { name: string }[]
  }

  const PolicyItem = ({ item, index }: { item: PolicyItemProps; index: number }) => (
    <Col span={24} key={index}>
      <Card hoverable className='policy-item'>
        <Title
          level={5}
          ellipsis={{ rows: 2 }}
          style={{ cursor: 'pointer', marginBottom: 12 }}
          onClick={() => window.open(item.source.url, '_blank')}
        >
          {item.title}
        </Title>
        <div style={{ margin: '12px 0' }}>
          {item.tags.map((tag, i) => (
            <Tag key={i} color={tag.name === '财政补贴' ? 'blue' : tag.name === '资管认证' ? 'green' : 'default'}>
              {tag.name}
            </Tag>
          ))}
        </div>
        <Text type='secondary' style={{ display: 'block', marginBottom: 16 }}>
          {item.description}
        </Text>
        <Flex justify='space-between' align='center'>
          <Text type='secondary' style={{ textDecoration: 'underline' }}>
            {item.source.name}
          </Text>
          <Text type='secondary'>{item.publishDate}</Text>
        </Flex>
      </Card>
    </Col>
  )

  const noData = () => {
    return (
      <Flex vertical align='center' justify='center' style={{ padding: '48px 0' }}>
        <div style={{ fontSize: '48px', marginBottom: '16px' }}>📭</div>
        <Text type='secondary'>暂无数据</Text>
      </Flex>
    )
  }

  return (
    <Spin spinning={generating}>
      <GetKey open={open} onClose={setOpen} onChange={setKey} />
      <div className='policy-search-bar'>
        <Flex className='policy-header' align='center' justify='center' vertical>
          {/* Tabs */}
          <Tabs defaultActiveKey='1' size='large' onChange={key => setType(key)}>
            <TabPane tab='政策查询解读' key='1' />
            <TabPane tab='政策智能匹配' key='3' />
          </Tabs>

          {/* Search Bar */}
          <Flex align='center' justify='center' gap={16} style={{ width: '65vw', maxWidth: '1200px' }}>
            <Input
              placeholder='请输入您想了解的政策内容'
              style={{ flex: 1 }}
              size='large'
              className='ant-input'
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              onKeyDown={e => {
                if (e.key === 'Enter') {
                  if (!key) {
                    setOpen(true)
                    return
                  }
                  if (searchText.trim() === '') {
                    message.warning('请输入您想了解的政策内容')
                    return
                  }
                  errCount.current = 0
                  handleGeneration1()
                  handleGeneration2()
                }
              }}
            />
            <Button
              type='primary'
              size='large'
              className='ant-btn-primary'
              onClick={() => {
                if (!key) {
                  setOpen(true)
                  return
                }
                if (searchText.trim() === '') {
                  message.warning('请输入您想了解的政策内容')
                  return
                }
                errCount.current = 0
                handleGeneration1()
                handleGeneration2()
              }}
            >
              查询
            </Button>
            <Flex align='center' gap={8}>
              <span>AI解读</span>
              <Switch
                defaultChecked={showThinking}
                disabled={type === '3'}
                onChange={() => setShowThinking(!showThinking)}
              />
            </Flex>
          </Flex>
        </Flex>

        <div className='policy-container' style={{ maxWidth: '1200px' }}>
          {type === '1' ? (
            <>
              {/* 思考过程区域 */}
              {thankText1 && (
                <Card className='thinking-card' style={{ marginBottom: 24 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Title level={4} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                        style={{ marginRight: 8, width: 20, height: 20, color: '#1890ff' }}
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth='2'
                          d='M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z'
                        />
                      </svg>
                      {`${title} - 政策解读:`}
                    </Title>
                    <Button
                      type='link'
                      onClick={handleCollapse1}
                      icon={collapse1 ? <CaretDownOutlined /> : <CaretUpOutlined />}
                    >
                      {collapse1 ? '展开' : '收起'}
                    </Button>
                  </div>
                  {!collapse1 && (
                    <Paragraph
                      style={{
                        marginTop: 16,
                        backgroundColor: '#f5f5f5',
                        padding: '8px',
                        borderRadius: '4px',
                        color: 'rgb(55, 65, 81)'
                      }}
                    >
                      <ReactMarkdown className='markdown-body'>{thankText1}</ReactMarkdown>
                    </Paragraph>
                  )}
                </Card>
              )}

              {/* 政策列表模块 */}
              {policyData1.length === 0 ? (
                noData()
              ) : (
                <Row gutter={[16, 16]}>
                  {policyData1.map((item, index) => (
                    <PolicyItem key={index} item={item} index={index} />
                  ))}
                </Row>
              )}
            </>
          ) : (
            <>
              {/* 思考过程区域 */}
              {thankText2 && (
                <Card className='thinking-card'>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Title level={4} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                        style={{ marginRight: 8, width: 20, height: 20, color: '#1890ff' }}
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth='2'
                          d='M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z'
                        />
                      </svg>
                      匹配到的政策
                    </Title>
                    <Button
                      type='link'
                      onClick={handleCollapse2}
                      icon={collapse2 ? <CaretDownOutlined /> : <CaretUpOutlined />}
                    >
                      {collapse2 ? '展开' : '收起'}
                    </Button>
                  </div>
                  {!collapse2 && (
                    <Paragraph
                      style={{
                        marginTop: 16,
                        backgroundColor: '#f5f5f5',
                        padding: '8px',
                        borderRadius: '4px',
                        color: 'rgb(55, 65, 81)'
                      }}
                    >
                      <ReactMarkdown className='markdown-body'>{thankText2}</ReactMarkdown>
                    </Paragraph>
                  )}
                </Card>
              )}

              <>
                {policyData2.length === 0 ? (
                  noData()
                ) : (
                  <Row gutter={[16, 16]}>
                    {policyData2.map((item, index) => (
                      <PolicyItem key={index} item={item} index={index} />
                    ))}
                  </Row>
                )}
              </>
            </>
          )}
        </div>
      </div>
    </Spin>
  )
}

export default PolicyAssistant
