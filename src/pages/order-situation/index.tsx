import React, { useCallback, useState } from 'react'
import { Card, Col, Flex, Pagination, Row, Spin, Statistic, message } from 'antd'
import ReactECharts from 'echarts-for-react'
import { getOrderSituation } from '@/api/orderSituation'
import { getListByTextSql } from '@/api/template'
import { extractContent, getAllKeyValuePairs, updateSqlLimit } from '@/utils/common'
import { NoData } from '@/component/NoData'
import './index.less'
import GetKey from '@/component/getKey'

export const OrderSituation: React.FC = () => {
  const [key, setKey] = useState('')
  const [open, setOpen] = useState(false)
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState<boolean>(false)
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [option, setOption] = useState<any | null>(null)
  const [summary, setSummary] = useState<any | null>(null)
  const [detailsData, setDetailsData] = useState<any | null>(null)
  const [graphicsSql, setGraphicsSql] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(10)
  const [maxPage, setMaxPage] = useState(1)
  const [hasMore, setHasMore] = useState(true) // 是否还有更多数据
  const [cache, setCache] = useState<any>({}) // 缓存对象，key 为页码，value 为数据
  const generateOption = (data: any[], xAxisKey: string, yAxisKey: string) => {
    // 动态提取 xAxis 和 yAxis 数据
    const xAxisData = data.map(item => item[xAxisKey])
    const yAxisData = data.map(item => item[yAxisKey])

    return {
      title: {
        text: `${xAxisKey} vs ${yAxisKey}`
      },
      grid: {
        left: '50px',
        bottom: '20px',
        right: '90px',
        top: '80px'
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        name: xAxisKey
      },
      yAxis: {
        type: 'value',
        name: yAxisKey
      },
      series: [
        {
          data: yAxisData,
          type: 'bar',
          barMaxWidth: 30,
          itemStyle: {
            color: '#4F46E5'
          }
        }
      ],
      tooltip: {
        trigger: 'axis'
      }
    }
  }
  const handleGeneration = useCallback(async (query: string) => {
    if (!key) {
      setOpen(true)
      return false
    }
    setGenerating(true)
    let res = ''

    try {
      await getOrderSituation(
        {
          query,
          key
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            setGenerating(false)
            if (message) {
              res += message
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                setKey('')
                setOpen(true)
                return
              }

              try {
                const graphics = extractContent(res, 'graphics')
                const graphicsData = JSON.parse(graphics)
                if (graphicsData.length > 0) {
                  let xAxisKey = Object.keys(graphicsData[0])[0]
                  let yAxisKey = Object.keys(graphicsData[0])[1]
                  const option = generateOption(graphicsData, xAxisKey, yAxisKey)
                  setOption(option)
                } else {
                  setOption(null)
                }
              } catch (error) {
                setOption(null)
                console.error('Error parsing data:', error)
              }
              try {
                const summary = extractContent(res, 'summary')
                const summaryData = JSON.parse(summary)
                setSummary(getAllKeyValuePairs(summaryData))
              } catch (error) {
                setSummary(null)
                console.error('Error parsing data:', error)
              }
              try {
                const details = extractContent(res, 'details')
                const detailsData = JSON.parse(details)
                setDetailsData(detailsData)
              } catch (error) {
                setDetailsData(null)
                console.error('Error parsing data:', error)
              }
              try {
                const graphicsSql = extractContent(res, 'graphicsSql')
                setGraphicsSql(graphicsSql)
              } catch (error) {
                setGraphicsSql('')
                console.error('Error parsing data:', error)
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }, [key])

  const handlePageChange = (page: number) => {
    if (cache[page]) {
      setDetailsData(cache[page])
    } else {
      getListByTextSql(updateSqlLimit(graphicsSql, page, pageSize)).then(res => {
        // 将新数据存入缓存
        setCache((prevCache: any) => ({
          ...prevCache,
          [page]: res.data
        }))

        setDetailsData(res.data)
        // 动态判断是否还有下一页
        if (res.data.length < pageSize) {
          setHasMore(false) // 没有更多数据
        } else {
          setHasMore(true) // 可能还有更多数据
        }
      })
      setMaxPage(page)
    }

    setCurrentPage(page)
  }

  return (
    <>
      {contextHolder}
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      <GetKey open={open} onClose={setOpen} onChange={setKey} />
      <div className='sales-dashboard'>
        <div className='order-situation-search'>
          <input
            type='text'
            value={searchTerm}
            onKeyDown={e => {
              if (e.key === 'Enter') {
                handleGeneration(searchTerm)
              }
            }}
            onChange={e => setSearchTerm(e.target.value)}
            placeholder='请输入查询关键词'
          />
          <button onClick={() => handleGeneration(searchTerm)}>查询</button>
        </div>
        {summary && (
          <Row gutter={16}>
            {summary.map((item: { [key: string]: string }) => (
              <Col span={6} key={item[0]}>
                <Card>
                  <Statistic
                    title={item[0]}
                    value={item[1]}
                    valueStyle={{ color: '#000', fontSize: '28px', fontWeight: 'bold' }}
                  />
                </Card>
              </Col>
            ))}
          </Row>
        )}
        {option && (
          <div style={{ padding: '32px', background: '#fff', marginTop: '28px' }}>
            <ReactECharts option={option as any} style={{ height: '400px' }} />
          </div>
        )}
        {detailsData && (
          <>
            <Row gutter={24} style={{ marginTop: '48px' }}>
              {detailsData.map((order: { [key: string]: string }) => (
                <Col span={8} key={order.orderNumber}>
                  <Card>
                    {Object.keys(order).map(key => (
                      <Flex key={key} align='center' justify='space-between'>
                        <span style={{ fontSize: '14px', color: '#6B7280' }}>{key}:</span>
                        <span style={{ fontSize: '14px', color: '#000', fontWeight: 'bold' }}>{order[key]}</span>
                      </Flex>
                    ))}
                  </Card>
                </Col>
              ))}
            </Row>

            {/* <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'center' }}>
              <Pagination
                current={currentPage}
                onChange={handlePageChange}
                total={hasMore ? maxPage * pageSize + 1 : maxPage * pageSize - 1} // 假设每页 10 条数据
                pageSize={pageSize}
                showSizeChanger={false}
              />
            </div> */}
          </>
        )}

        {!detailsData && !option && !summary && <NoData description='请输入查询关键词并查询' />}
      </div>
    </>
  )
}

export default OrderSituation
