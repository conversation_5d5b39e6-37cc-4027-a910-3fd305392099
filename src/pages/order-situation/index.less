.sales-dashboard {
  padding: 32px 48px; // 主体边距
  background-color: #f9fafb; // 背景颜色
  height: 100vh;
  width: 100vw;
  overflow-y: auto;

  .ant-card {
    margin-bottom: 20px; // 卡片底部边距
  }

  .ant-statistic-title {
    font-weight: bold; // 标题加粗
  }
}

.order-situation-search {
  display: flex;
  margin-bottom: 48px;
  gap: 0.75rem;
}

.order-situation-search input {
  flex-grow: 1;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.order-situation-search input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.order-situation-search button {
  padding: 0.75rem 1.25rem;
  background-color: #4f46e5;
  color: white;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  font-size: 14px;
}

.order-situation-search button:hover {
  background-color: #4f46e599;
}
