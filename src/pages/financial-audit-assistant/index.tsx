import { useState, useCallback } from 'react'
import { <PERSON><PERSON>, Card, Flex, message, Spin, Typography, Upload } from 'antd'
import { CheckCircleFilled, InboxOutlined } from '@ant-design/icons'
import { financialAuditAssistant } from '@/api/financialAuditAssistant'
import StreamTypewriter from '@/component/StreamTypewriter'
import ReactMarkdown from 'react-markdown'
import RemarkGfm from 'remark-gfm'
import RemarkBreaks from 'remark-breaks'
import RemarkMath from 'remark-math'
import { uploadFile } from '@/api/template'
import './index.less'

const { Title, Text } = Typography

const token = import.meta.env['VITE_FINANCIAL_AUDIT_ASSISTANT'] || ''

const FinancialAuditAssistant = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [generating, setGenerating] = useState(false)
  const [result, setResult] = useState('')
  const [error, setError] = useState<string | null>(null)

  const beforeUpload = async (file: File) => {
    const ext = file.name
      .substring(file.name.lastIndexOf('.') + 1)
      .toLowerCase()
    if (!['pdf', 'doc', 'docx', 'xls', 'xlsx'].includes(ext)) {
      messageApi.error(
        '目前仅支持.pdf、.doc、.docx、.xls、.xlsx类型的文件，请您将文件转成这些格式后上传'
      )
      return false
    }
    if (uploadedFiles.length >= 10) {
      messageApi.error('最多只能上传10个文件')
      return false
    }
    messageApi.open({
      key: 'uploading',
      type: 'loading',
      content: '文件上传中',
    })
    setLoading(true)
    try {
      const response = await uploadFile(file, token)
      setLoading(false)
      messageApi.open({
        key: 'uploading',
        type: 'success',
        content: '文件上传成功',
        duration: 1,
      })
      if (response && response.id) {
        setUploadedFiles((prev) => [
          ...prev,
          { id: response.id, name: file.name },
        ])
      } else {
        messageApi.error('文件上传失败')
      }
    } catch (e) {
      setLoading(false)
      messageApi.error('文件上传失败')
    }
    return false
  }

  const handleDelete = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId))
  }

  const handleStart = useCallback(async () => {
    if (!uploadedFiles.length) {
      messageApi.error('请先上传文件')
      return
    }
    let accumulatedMessages = ''
    const fileIds = uploadedFiles.map((file) => file.id)
    setGenerating(true)
    setResult('')
    setError(null)
    try {
      await financialAuditAssistant(
        {
          files: fileIds.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x,
          })),
        },
        {
          onMessage: (text, finished) => {
            if (text) {
              accumulatedMessages += text
              console.log(accumulatedMessages, 'accumulatedMessages')
              setResult(accumulatedMessages)
              console.log(result, 'Result')
            }
            if (finished) setGenerating(false)
          },
          onError: (err) => {
            setError(err?.message || '分析失败')
            setGenerating(false)
          },
          onFinish: () => setGenerating(false),
        }
      )
    } catch (err: any) {
      setError(err?.message || '分析异常')
      setGenerating(false)
    }
  }, [uploadedFiles])

  return (
    <>
      {contextHolder}
      <Spin
        tip="加载中"
        spinning={loading || generating}
        fullscreen
        size="large"
      />
      <Flex className="toolbar" justify="center">
        <Typography.Text className="title-text">财务审计助手</Typography.Text>
      </Flex>
      <div className="financial-audit-content">
        <Card className="template-form">
          <Flex vertical gap="middle">
            <Upload.Dragger
              showUploadList={false}
              multiple={true}
              beforeUpload={beforeUpload}
              accept=".pdf,.doc,.docx,.xls,.xlsx"
            >
              <span className="financial-audit-upload-icon">
                {uploadedFiles.length > 0 ? (
                  <CheckCircleFilled />
                ) : (
                  <InboxOutlined />
                )}
              </span>
              <br />
              <p className="financial-audit-upload-text">
                点击或者将财报文件拖拽到这里上传
              </p>
              <br />
              {/* <span className="financial-audit-upload-hint">
                {uploadedFiles.length > 0
                  ? '点击或者将财报文件拖拽到这里上传'
                  : ''}
              </span> */}
            </Upload.Dragger>
            {uploadedFiles.length > 0 && (
              <div className="file-list-contract" style={{ margin: '12px 0' }}>
                {uploadedFiles.map((file) => (
                  <div
                    key={file.id}
                    className="file-item"
                    style={{ display: 'flex', alignItems: 'center', gap: 8 }}
                  >
                    <span>{file.name}</span>
                    <span
                      style={{ cursor: 'pointer', color: '#ff4d4f' }}
                      onClick={() => handleDelete(file.id)}
                    >
                      x
                    </span>
                  </div>
                ))}
              </div>
            )}
            <Button
              size="large"
              type="primary"
              disabled={uploadedFiles.length === 0 || generating}
              onClick={handleStart}
            >
              开 始 分 析
            </Button>
          </Flex>
        </Card>
        {(error || result) && (
          <Card className="preview-content" style={{ minHeight: 400 }}>
            {error ? (
              <Text type="danger">{error}</Text>
            ) : (
              // <StreamTypewriter
              //   text={result}
              //   remarkPlugins={[[RemarkMath], RemarkGfm, RemarkBreaks]}
              //   end={!generating}
              // />
              <ReactMarkdown
                className="markdown-body"
                remarkPlugins={[[RemarkMath], RemarkGfm, RemarkBreaks]}
              >
                {result}
              </ReactMarkdown>
            )}
          </Card>
        )}
      </div>
    </>
  )
}

export default FinancialAuditAssistant
