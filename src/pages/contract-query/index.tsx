import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Card, Typo<PERSON>, Button, Spin } from 'antd'
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons'
import ReactMarkdown from 'react-markdown'
import { NoData } from '@/component/NoData'
import { contractQuery } from '@/api/contractQuery'
import './index.less'

const { Title, Paragraph } = Typography
interface nameMapType {
  [key: string]: string
}

const nameMap: nameMapType = {
  contract_amount: '合同金额',
  contract_effective_date: '生效时间',
  customer_manager_name: '客户经理',
  contract_no: '合同编号',
  contract_type: '合同类型',
  contract_name: '合同名称',
  customer_name: '客户名称',
  customer_id: '客户ID'
}

interface ContractType {
  contract_amount: number
  contract_effective_date: number
  customer_manager_name: string
  contract_no: string
  contract_type: string
  contract_name: string
  customer_name: string
  customer_id: string
}

const contractData: ContractType[] = []

// 添加一个计算列数的函数
const getGridColumns = (fields: Array<keyof ContractType>): string => {
  const columnCount = fields.length || 1 // 如果没有字段，至少保持1列
  return `repeat(${columnCount}, 1fr)`
}

export const ContractQuery: React.FC = () => {
  const [generating, setGenerating] = useState<boolean>(false)
  const [thankText, setThankText] = useState<string>('')
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [viewMode, setViewMode] = useState<'card' | 'list'>('card')
  const [selectedFields, setSelectedFields] = useState<Array<keyof ContractType>>([])
  const [showFields, setShowFields] = useState<boolean>(false)
  const [filteredData, setFilteredData] = useState<ContractType[]>(contractData)

  const fieldRef = useRef<HTMLDivElement>(null)

  // 点击外部关闭字段选择
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent): void => {
      if (fieldRef.current && !fieldRef.current.contains(e.target as Node)) {
        setShowFields(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleGeneration = useCallback(async (str: string) => {
    setGenerating(true)
    let message = ''

    try {
      await contractQuery(str, {
        onMessage: (text, finished) => {
          if (text) {
            message += text
          }
          if (finished) {
            // 正则表达式获取<think>标签中的内容
            const regex = /<think>([\s\S]*?)<\/think>/
            const match = message.match(regex)

            if (match && match[1]) {
              const result = match[1].trim()
              setThankText(result)
            } else {
              console.log('没有找到<think>标签中的内容。')
              setThankText('')
            }

            // 正则表达式获取<think>标签之后的内容
            const regexArr = /<\/think>([\s\S]*)$/
            const matchArr = message.match(regexArr)

            if (matchArr && matchArr[1]) {
              // 去掉前面的空格或者换行，解析为数组的内容
              const jsonArrayString = matchArr[1].trim()
              // 尝试解析JSON
              try {
                const contracts = JSON.parse(jsonArrayString)
                if (contracts && contracts.length > 0) {
                  setFilteredData(contracts)
                }
              } catch (error) {
                console.error('JSON解析失败:', error)
                setFilteredData([])
              }
            } else {
              console.log('没有找到<think>标签后的JSON数组')
              setFilteredData([])
            }
            setGenerating(false)
          }
        },
        onError: () => {
          setGenerating(false)
        },
        onFinish: () => {}
      })
    } catch (err) {
      setGenerating(false)
    }
  }, [])

  // 字段选择处理
  const toggleField = (field: keyof ContractType): void => {
    setSelectedFields(prev => (prev.includes(field) ? prev.filter(f => f !== field) : [...prev, field]))
  }

  // 修改 renderContractItems 函数中的列表视图部分
  const renderContractItems = () => {
    return filteredData.map((contract, index) => {
      if (!contract) return null
      const availableFields = Object.keys(contract) as Array<keyof ContractType>
      const fieldsToShow =
        selectedFields.length > 0 ? availableFields.filter(field => selectedFields.includes(field)) : availableFields

      return (
        <div key={index} className={`contract-item ${viewMode === 'list' ? 'list-view' : ''}`}>
          {viewMode === 'card' ? (
            <>
              {fieldsToShow.map(field => {
                const fieldLabel = nameMap[field] || field
                if (field === 'contract_amount') {
                  return (
                    <p key={field}>
                      {fieldLabel}：<span className='contract-amount'>¥{contract[field].toLocaleString()}</span>
                    </p>
                  )
                }
                if (field === 'contract_effective_date') {
                  return (
                    <p key={field}>
                      {fieldLabel}：{new Date(contract[field]).toLocaleDateString()}
                    </p>
                  )
                }
                return (
                  <p key={field}>
                    {fieldLabel}：{contract[field]}
                  </p>
                )
              })}
            </>
          ) : (
            <div
              className='list-item-container'
              style={{
                gridTemplateColumns: getGridColumns(fieldsToShow)
              }}
            >
              {fieldsToShow.map(field => (
                <div key={field} className='list-item-field'>
                  <span className='field-label'>{nameMap[field] || field}</span>
                  <span className='field-value'>
                    {field === 'contract_amount'
                      ? `¥${contract[field].toLocaleString()}`
                      : field === 'contract_effective_date'
                      ? new Date(contract[field]).toLocaleDateString()
                      : contract[field]}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      )
    })
  }

  const [collapse, setCollapse] = useState(false)
  const handleCollapse = () => {
    // 处理收起逻辑
    setCollapse(!collapse)
  }

  return (
    <>
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      <div className='contract-query-container'>
        <h1>智能合同查询</h1>

        <div className='search-container'>
          <input
            type='text'
            value={searchTerm}
            onKeyDown={e => {
              if (e.key === 'Enter') {
                handleGeneration(searchTerm)
              }
            }}
            onChange={e => setSearchTerm(e.target.value)}
            placeholder='请输入合同查询需求，例如：25年软件合同金额大于3千万的合同'
          />
          <button onClick={() => handleGeneration(searchTerm)}>搜索</button>
        </div>

        <div className='controls'>
          <div className='field-selection' ref={fieldRef}>
            <button onClick={() => setShowFields(!showFields)}>自定义展示</button>
            <div className={`dropdown-content ${showFields ? 'show' : ''}`}>
              {Object.keys(filteredData[0] || {}).map(field => (
                <label key={field}>
                  <input
                    type='checkbox'
                    checked={selectedFields.includes(field as keyof ContractType)}
                    onChange={() => toggleField(field as keyof ContractType)}
                  />
                  {nameMap[field] || field}
                </label>
              ))}
            </div>
          </div>

          <div className='view-toggle'>
            <button className={viewMode === 'card' ? 'active' : ''} onClick={() => setViewMode('card')}>
              卡片视图
            </button>
            <button className={viewMode === 'list' ? 'active' : ''} onClick={() => setViewMode('list')}>
              列表视图
            </button>
          </div>
        </div>

        <div className='total-count'>共 {filteredData.length} 条合同</div>
        {/* 思考过程区域 */}
        {thankText && (
          <Card className='thinking-card'>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={4} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  fill='none'
                  viewBox='0 0 24 24'
                  stroke='currentColor'
                  style={{ marginRight: 8, width: 20, height: 20, color: '#1890ff' }}
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth='2'
                    d='M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z'
                  />
                </svg>
                查询思考过程
              </Title>
              <Button
                type='link'
                onClick={handleCollapse}
                icon={collapse ? <CaretDownOutlined /> : <CaretUpOutlined />}
              >
                {collapse ? '展开' : '收起'}
              </Button>
            </div>
            {!collapse && (
              <Paragraph
                style={{
                  marginTop: 16,
                  backgroundColor: '#f5f5f5',
                  padding: '8px',
                  borderRadius: '4px',
                  color: 'rgb(55, 65, 81)'
                }}
              >
                <ReactMarkdown className='markdown-body'>{thankText}</ReactMarkdown>
              </Paragraph>
            )}
          </Card>
        )}
        <div className={`contract-list ${viewMode === 'list' ? 'list-view' : ''}`}>
          {filteredData.length === 0 ? (
            <NoData description='未找到匹配的合同信息' marginTop='150px' />
          ) : (
            renderContractItems()
          )}
        </div>

        <div style={{ height: '20px' }}></div>
      </div>
    </>
  )
}

export default ContractQuery
