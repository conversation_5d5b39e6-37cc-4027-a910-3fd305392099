import { useEffect, useRef, useState, useMemo, useCallback } from 'react'
import { useLocation } from 'react-router-dom'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { dark } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { WebContainer } from '@webcontainer/api'
import { Terminal } from '@xterm/xterm'
import { Button, Flex, Space, Spin, Splitter, theme, Tooltip, Typography } from 'antd'
import {
  CheckCircleFilled,
  CopyOutlined,
  DownloadOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined
} from '@ant-design/icons'
import { REACT_APP } from '@projects/react-app'
import { VUE_APP } from '@projects/vue-app'
import { installDependencies, startDevServer } from '@/utils/dev-operations'
import { getCodeInfo } from '@/api/interpreter'
const { useToken } = theme

import './xterm.less'
import './index.less'

let containerInstance: WebContainer

export const Interpreter = () => {
  const { token } = useToken()
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const terminalRef = useRef<HTMLDivElement>(null)

  const [loading, setLoading] = useState(true)
  const [codeLoading, setCodeLoading] = useState(true)
  const [devServerStarted, setDevServerStarted] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [codeString, setCodeString] = useState('')
  const [codeType, setCodeType] = useState<string | null>(null)
  const [copyText, setCopyText] = useState('复制')
  const [copyIcon, setCopyIcon] = useState(<CopyOutlined className='color-white' />)
  const [downloadText, setDownloadText] = useState('下载')

  const { search } = useLocation()
  const searchParams = useMemo(() => new URLSearchParams(search), [search])
  const shortUrl = searchParams.get('shortUrl')
  const code = searchParams.get('code')     // 获取编码后的代码内容

  useEffect(() => {
    setCodeType(searchParams.get('type'))
  }, [searchParams])

  const initJsx = useCallback(async (value: string) => {
    if (containerInstance) return
    containerInstance = await WebContainer.boot()
    REACT_APP['src']['directory']['App.tsx'].file.contents = value
    setLoading(false)
    containerInstance
      .mount(REACT_APP)
      .then(() => {
        const terminal = new Terminal({ convertEol: true })
        if (terminalRef.current) terminal.open(terminalRef.current)
        return terminal
      })
      .then(terminal => {
        installDependencies(containerInstance, terminal).then(res => {
          if (res) {
            startDevServer(containerInstance, terminal, url => {
              if (iframeRef.current) {
                iframeRef.current.src = url
                setDevServerStarted(true)
              }
            })
          }
        })
      })
  }, [])

  const initVue = useCallback(async (value: string) => {
    if (containerInstance) return
    containerInstance = await WebContainer.boot()
    VUE_APP['src']['directory']['App.vue'].file.contents = value
    setLoading(false)
    containerInstance
      .mount(VUE_APP)
      .then(() => {
        const terminal = new Terminal({ convertEol: true })
        if (terminalRef.current) terminal.open(terminalRef.current)
        return terminal
      })
      .then(terminal => {
        installDependencies(containerInstance, terminal).then(res => {
          if (res) {
            startDevServer(containerInstance, terminal, url => {
              if (iframeRef.current) {
                iframeRef.current.src = url
                setDevServerStarted(true)
              }
            })
          }
        })
      })
  }, [])

  const initHtml = useCallback((value: string) => {
    if (iframeRef.current) {
      const doc = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document
      if (doc) {
        doc.open()
        doc.write(value)
        doc.close()
        setLoading(false)
        setDevServerStarted(true)
      }
    }
  }, [])

  useEffect(() => {
    if (shortUrl) {
      getCodeInfo(shortUrl).then(res => {
        if (res.code === 200) {
          const code = String(`${res.data}`).replace(/\n$/, '')
          setCodeString(code)
          setCodeLoading(false)
          const type = searchParams.get('type')
          if (type === 'html') initHtml(code)
          else if (type === 'jsx' || type === 'tsx') initJsx(code)
          else if (type === 'vue') initVue(code)
        } else {
          setCodeString('代码加载失败')
          setLoading(false)
          setCodeLoading(false)
        }
      })
    } else if (code) {  // 从 URL 参数中获取的代码
      try {
        const decodedCode = decodeURIComponent(atob(code));  // Base64 解码
        setCodeString(decodedCode)
        setCodeLoading(false)
        const type = searchParams.get('type')
        if (type === 'html') initHtml(decodedCode)
        else if (type === 'jsx' || type === 'tsx') initJsx(decodedCode)
        else if (type === 'vue') initVue(decodedCode)
      } catch (error) {
        setCodeString('代码加载失败')
        setLoading(false)
        setCodeLoading(false)
      }
    }
  }, [searchParams, shortUrl, code, initHtml, initJsx, initVue])

  const handleCopyClick = useCallback(() => {
    navigator.clipboard
      .writeText(codeString)
      .then(() => {
        setCopyText('已复制')
        setCopyIcon(<CheckCircleFilled className='color-white' />)
      })
      .finally(() => {
        setTimeout(() => {
          setCopyText('复制')
          setCopyIcon(<CopyOutlined className='color-white' />)
        }, 2000)
      })
  }, [codeString])

  const downloadFile = useCallback((text: string, filename: string) => {
    const blob = new Blob([text], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }, [])

  const handleDownloadClick = useCallback(() => {
    if (!codeType) return
    const filename = `index.${codeType}`
    downloadFile(codeString, filename)
    setDownloadText('已下载')
    setTimeout(() => {
      setDownloadText('下载')
    }, 2000)
  }, [codeString, codeType, downloadFile])

  return (
    <>
      <Spin tip='加载中' spinning={loading} fullscreen size='large' />
      <Flex className='interpreter-container' vertical>
        <Flex className='interpreter-container-toolbar' justify='space-between'>
          <Typography.Text className='title-text'>代码解释器</Typography.Text>
        </Flex>
        <Splitter style={{ height: 'calc(100% - 70px)' }}>
          <Splitter.Panel
            className='custom-scrollbar'
            defaultSize='40%'
            min='20%'
            max='70%'
            style={{ borderRadius: 8 }}
          >
            <Spin tip='代码加载中' spinning={codeLoading} size='large'>
              <Flex justify='space-between' align='center' className='code-header'>
                <Typography.Text className='code-header-name'>{codeType}</Typography.Text>
                <Space>
                  <Tooltip placement='bottom' title={copyText}>
                    <Button color='primary' variant='link' icon={copyIcon} onClick={handleCopyClick}></Button>
                  </Tooltip>
                  <Tooltip placement='bottom' title={downloadText}>
                    <Button
                      color='primary'
                      variant='link'
                      icon={<DownloadOutlined className='color-white' />}
                      onClick={handleDownloadClick}
                    ></Button>
                  </Tooltip>
                </Space>
              </Flex>
              <SyntaxHighlighter
                className='editor'
                language='html'
                showLineNumbers={true}
                wrapLines={true}
                style={dark}
                customStyle={{
                  border: 'none',
                  borderRadius: 0,
                  margin: 0,
                  background: '#001529'
                }}
                children={codeString}
              />
            </Spin>
          </Splitter.Panel>
          <Splitter.Panel>
            <Flex className={`preview ${isFullscreen ? 'fullscreen' : ''}`} vertical>
              <Tooltip
                title={isFullscreen ? '退出全屏' : '全屏'}
                getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
              >
                <Button
                  style={{
                    padding: token.paddingXXS,
                    backgroundColor: token.colorBgMask
                  }}
                  className='fullscreen-btn'
                  icon={
                    isFullscreen ? (
                      <FullscreenExitOutlined style={{ color: token.colorTextLightSolid, width: '12px' }} />
                    ) : (
                      <FullscreenOutlined style={{ color: token.colorTextLightSolid, width: '12px' }} />
                    )
                  }
                  onClick={() => setIsFullscreen(!isFullscreen)}
                />
              </Tooltip>
              <div ref={terminalRef} className={`terminal ${devServerStarted ? 'hidden' : ''}`} />
              <div className={`iframe-wrapper ${devServerStarted ? 'visible' : ''}`}>
                <iframe style={{ border: 0 }} ref={iframeRef} src='/loading.html' />
              </div>
            </Flex>
          </Splitter.Panel>
        </Splitter>
      </Flex>
    </>
  )
}

export default Interpreter;