.ant-splitter {
  padding: 12px 24px;
  gap: 12px;
  flex-grow: 1;

  .ant-splitter-panel {
    flex: 1;
  }
}

/** 控制代码块的高度撑满父容器 */
.ant-spin-nested-loading,
.ant-spin-container {
  height: 100%;
}

.interpreter-container-toolbar {
  background: linear-gradient(180deg, rgba(189, 225, 255, 0.4) 0%, rgba(224, 242, 255, 0) 100%);
  border-radius: 0.5rem 0.5rem 0 0;
  padding: 12px 24px;
  height: 70px;

  .title-text {
    color: transparent;
    background: linear-gradient(116deg, #1888ff 16%, #2f54eb 88%);
    background-clip: text;
    -webkit-background-clip: text;
    user-select: none;
    font-size: 30px;
    font-weight: bold;
  }
}

.interpreter-container {
  height: 100%;
  .code-header {
    background: #001f3d;
    padding: 0 16px;
    height: 64px;
    position: sticky;
    top: 0;
    z-index: 1;
    .code-header-name {
      color: #fff;
      font-size: 16px;
      font-weight: bold;
      padding: 0 16px;
    }
  }
  .fullscreen-btn {
    position: absolute;
    height: 32px;
    width: 32px;
    right: 16px;
    top: 16px;
    border: none;
  }
}

.color-white {
  color: white;
}

.ant-spin-container {
  .editor {
    position: relative;
    inset: 0;
    width: 100%;
    min-height: 100%;
    max-height: 1000px;
  }
}

.preview {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 0.5rem;
  background-color: #f7f7f7;

  .terminal {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 1;
    transition: opacity 0.5s ease;

    &.hidden {
      opacity: 0;
      pointer-events: none;
    }
  }

  .iframe-wrapper {
    width: 100%;
    height: 100%;
    overflow: hidden;
    opacity: 0;
    transition: opacity 0.5s ease;

    &.visible {
      opacity: 1;
    }

    iframe {
      width: 100%;
      height: 100%;
      border: none;
    }
  }

  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw; /* 视口宽度 */
    height: 100vh; /* 视口高度 */
    z-index: 9999; /* 确保在最上层 */
  }
}
