import { useState, useEffect, useCallback, useRef } from 'react'
import {
  Upload,
  Button,
  Typography,
  Card,
  Spin,
  message,
  Form,
  Flex,
} from 'antd'
import {
  CheckCircleFilled,
  InboxOutlined,
  DeleteOutlined,
} from '@ant-design/icons'
import { uploadFile } from '@/api/template'
import { financialanAlysis } from '@/api/financialanAlysis'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import { PdfUrlViewer } from '@/component/pdfViewer/PdfUrlViewer'
import StreamTypewriter from '@/component/StreamTypewriter'
import { NoData } from '@/component/NoData'
import { extractContent } from '@/utils/common'
import { renderAsync } from 'docx-preview'
import ThinkBlock from '@/component/think'
import './index.less'

function DocxPreview({ file }: any) {
  const previewRef = useRef<any>(null)

  useEffect(() => {
    if (!file) return

    // 渲染 .docx 文件
    renderAsync(file, previewRef.current)
      .then(() => console.log('DOCX 渲染成功'))
      .catch((err) => console.error('DOCX 渲染失败:', err))
  }, [file])

  return <div ref={previewRef} style={{ width: '100%', minHeight: '500px' }} />
}

const appKey = import.meta.env['VITE_FINANCIALAN_ALYSIS_TOKEN'] || ''

export const FinancialanAlysis = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [uploadedFiles, setUploadedFiles] = useState<
    { id: string; name: string }[]
  >([])
  const [startSending, setStartSending] = useState<boolean>(false)
  const [generating, setGenerating] = useState<boolean>(false)
  const [markdownTable, setMarkdownTable] = useState('')
  const [streamTypewriterKey, setStreamTypewriterKey] = useState(0)
  const [fileUrl, setFileUrl] = useState('')
  const [finished, setFinished] = useState<boolean>(false)
  const windowRef: any = useRef()

  useEffect(() => {
    if (uploadedFiles.length > 0) {
      messageApi.open({
        key: 'uploading',
        type: 'success',
        content: '文件上传成功',
        duration: 1,
      })
    }
  }, [uploadedFiles, messageApi])

  const beforeUpload = (file: File) => {
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf('.') + 1)
      ?.toLowerCase()
    if (['docx', 'doc', 'pdf'].includes(originalFileExt)) {
      setGenerating(true)
      setUploadedFiles([])
      setDocxFile(null)
      setFileUrl('')
      if (['docx', 'doc'].includes(originalFileExt)) {
        setDocxFile(file)
      } else if (originalFileExt === 'pdf') {
        const url = URL.createObjectURL(file)
        setFileUrl(url)
      }

      uploadFile(file, appKey).then(async (response) => {
        setGenerating(false)
        if (response.id) {
          setUploadedFiles([response])
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1,
          })
        } else {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件上传失败',
            duration: 1,
          })
        }
      })
    } else {
      messageApi.error(
        '目前仅支持.docx, .doc, .pdf 类型的文件，请您将文件转成这些格式后再次进行上传'
      )
    }
    return false
  }

  const handleDelete = () => {
    setUploadedFiles([])
    setDocxFile(null)
    setFileUrl('')
  }

  const handleGenerationStart = () => {
    if (uploadedFiles.length === 0) {
      messageApi.error('请上传至少一个文件')
      return
    }
    setStartSending(true)
    const fileIds = uploadedFiles.map((file) => file.id)
    handleGeneration(fileIds)
    // setMarkdownTable('<think>')
    setMarkdownTable('')
  }
  const handleGeneration = useCallback(async (fileIds: string[]) => {
    setGenerating(true)
    setFinished(false)
    let accumulatedMessages = ''
    setStreamTypewriterKey((prev) => prev + 1)

    try {
      await financialanAlysis(
        {
          files: fileIds.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x,
          })),
        },
        {
          onMessage: (text: string | null, finished: boolean) => {
            if (text) {
              if (accumulatedMessages.includes('</think>')) {
                accumulatedMessages += text
                setMarkdownTable((prev) => prev + text)
              } else {
                accumulatedMessages += text.replace(/\n/g, '<br />')
                setMarkdownTable((prev) => prev + text.replace(/\n/g, '<br />'))
              }
              setGenerating(false)
            }
            if (finished) {
              setFinished(true)
              const errorStr = extractContent(accumulatedMessages, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {
            setGenerating(false)
          },
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }, [])

  const [docxFile, setDocxFile] = useState<File | null>(null)

  return (
    <>
      {contextHolder}
      <Spin tip="处理中..." spinning={generating} fullscreen size="large" />
      <div className="financial-analysis-container">
        {/* 左侧面板 */}
        <div className="financial-analysis-left-panel">
          <Typography.Text className="title-text">财报分析助手</Typography.Text>
          <Flex align="center" justify="center">
            <Form layout="vertical" style={{ width: '100%' }}>
              <Form.Item label="上传文件">
                <Upload.Dragger
                  accept=".docx, .doc, .pdf"
                  multiple
                  showUploadList={false}
                  beforeUpload={beforeUpload}
                >
                  <div className="ant-upload-drag-icon">
                    {uploadedFiles.length > 0 ? (
                      <CheckCircleFilled />
                    ) : (
                      <InboxOutlined />
                    )}
                  </div>
                  <div className="ant-upload-hint">
                    <span>拖拽文件到此处上传</span>
                    <br />
                    <span style={{ fontSize: '12px', color: '#999' }}>
                      或点击选择文件
                    </span>
                  </div>
                </Upload.Dragger>

                {uploadedFiles.length > 0 && (
                  <div className="file-list-contract">
                    {uploadedFiles.map((file) => (
                      <div key={file.id} className="file-item">
                        <span style={{ flex: 1 }}>{file.name}</span>
                        <DeleteOutlined
                          onClick={() => handleDelete()}
                          style={{ cursor: 'pointer', flex: '0 0 20px' }}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </Form.Item>
              <Form.Item>
                <Button
                  type="primary"
                  disabled={uploadedFiles.length === 0 || generating}
                  onClick={handleGenerationStart}
                  style={{ width: '100%' }}
                >
                  智能分析
                </Button>
              </Form.Item>
            </Form>
          </Flex>

          {fileUrl && <PdfUrlViewer url={fileUrl} windowRef={windowRef} />}
          {docxFile && <DocxPreview file={docxFile} />}
        </div>

        {/* 右侧面板 */}
        <div className="financial-analysis-right-panel">
          {startSending && (
            <Flex
              justify="center"
              style={{
                marginTop: 15,
                overflow: 'auto',
                width: '100%',
              }}
            >
              {markdownTable ? (
                <Card style={{ width: '100%', overflow: 'auto' }}>
                  <StreamTypewriter
                    text={markdownTable}
                    key={streamTypewriterKey}
                    end={generating}
                    components={{
                      think: (props: any) => {
                        return (
                          <ThinkBlock finished={finished}>
                            {props.children}
                          </ThinkBlock>
                        )
                      },
                    }}
                  />
                </Card>
              ) : (
                <Flex justify="justify" align="center" vertical>
                  <Typography.Text>
                    {'正在提取文件信息，请不要关闭或刷新页面'}
                  </Typography.Text>
                </Flex>
              )}
            </Flex>
          )}
          {!startSending && (
            <NoData description=" " text="您的企业财报分析助手" />
          )}
        </div>
      </div>
    </>
  )
}

export default FinancialanAlysis
