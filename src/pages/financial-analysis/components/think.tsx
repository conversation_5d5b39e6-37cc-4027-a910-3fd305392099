import React, { useEffect, useState } from 'react'
import { Collapse } from 'antd'
import { BulbOutlined } from '@ant-design/icons'
const ThinkBlock: React.FC<{ children: React.ReactNode; finished: boolean }> = ({ children, finished }) => {
  const [activeKey, setActiveKey] = useState<string[] | undefined>(['1'])

  useEffect(() => {
    // 当内容完成时，延迟折叠面板
    if (finished) {
      const timer = setTimeout(() => {
        setActiveKey([]) // 折叠
      }, 300) // 可以加点延迟让过渡更自然
      return () => clearTimeout(timer)
    } else {
      // 未完成时保持展开状态
      setActiveKey(['1'])
    }
  }, [finished])

  const items = [
    {
      key: '1',
      label: (
        <span className='think-header'>
          <BulbOutlined style={{ marginRight: 8, color: '#faad14' }} />
          思考提示
        </span>
      ),
      children: <div className='think-content'>{children}</div>
    }
  ]

  return (
    <div className='think-block'>
      <Collapse
        bordered={false}
        expandIconPosition='end'
        className='think-collapse'
        activeKey={activeKey}
        onChange={key => setActiveKey(key as string[])}
        items={items}
      />
    </div>
  )
}

export default ThinkBlock
