.financial-analysis-container {
  display: flex;
  gap: 24px;
  margin: 0 auto;
  height: 100vh;
  overflow-x: hidden;
  // 左侧面板样式
  .financial-analysis-left-panel {
    width: 40%;
    min-width: 40%;
    background: #ffffff;
    padding: 24px;

    .title-text {
      font-size: 24px;
      font-weight: 600;
      text-align: center;
      color: #1a1a1a;
      padding-bottom: 8px;
      display: block;
    }
  }

  // 右侧面板样式
  .financial-analysis-right-panel {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
    position: relative;

    .markdown-body table {
      border-collapse: collapse;
      width: 100%;
    }

    .markdown-body th,
    .markdown-body td {
      border: 1px solid #ccc; /* 设置边框颜色 */
      padding: 8px; /* 设置单元格内边距 */
      text-align: left; /* 设置文本对齐方式 */
    }

    .markdown-body th {
      background-color: #f2f2f2; /* 设置表头背景色 */
    }
    .markdown-body tr td:nth-child(1) {
      min-width: 90px;
    }
  }
  .docx-wrapper {
    padding: 0;
    background-color: #fff;
  }
  .docx {
    padding: 20px 40px !important;
    width: 100% !important;
    margin: 0 !important;
    margin-bottom: 20px !important;
    border-radius: 4px;
  }
}

// 上传区域样式
.ant-upload-drag {
  padding: 24px;
  border: 2px dashed #e8e8e8;
  border-radius: 12px;
  background: #fafafa;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
    background: #f0f7ff;
  }

  .ant-upload-drag-icon {
    margin-bottom: 16px;
    font-size: 48px;
    color: #1890ff;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }
}

// 文件列表样式
.file-list-contract {
  margin-top: 16px;
  max-height: 200px;
  overflow-y: auto;
  padding-right: 4px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f5f5;
    border-radius: 3px;
  }

  .file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 8px;
    transition: all 0.3s ease;

    &:hover {
      background: #f0f2f5;
    }

    span {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 12px;
    }
  }
}

// 输入框样式
.ant-input {
  border-radius: 8px;

  &:hover,
  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }
}
