import React, { useState, useRef, useEffect } from 'react'
import {
  Upload,
  Button,
  message,
  Card,
  Typography,
  Spin,
  Layout,
  Flex,
  Tag,
} from 'antd'
import { CheckCircleFilled, InboxOutlined } from '@ant-design/icons'
import { documentSensitiveInspection } from '@/api/documentSensitiveInspection'
import { uploadFile } from '@/api/template'
import { extractContent } from '@/utils/common'
import StreamTypewriter from '@/component/StreamTypewriter'
import MarkdownIt from 'markdown-it'
import {
  Document,
  Packer,
  Paragraph,
  TextRun,
  HeadingLevel,
  TableCell,
  TableRow,
  Table,
  WidthType,
} from 'docx'
import { saveAs } from 'file-saver'
import './index.less'

const { Title, Text } = Typography
const token = import.meta.env['VITE_DOCUMENT_SENSITIVE_INSPECTION'] || ''

export const DocumentSensitiveInspection: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState(false)
  const [mk, setMk] = useState('')
  const [isShowMk, setIsShowMk] = useState(false)
  const [isUploadBtn, setIsUploadBtn] = useState(false)
  const [streamTypewriterKey, setStreamTypewriterKey] = useState(1)
  const [uploadedFile, setUploadedFile] = useState<{
    id: string
    name: string
  }>({ id: '', name: '' })
  const [uploadedFiles, setUploadedFiles] = useState<
    { id: string; name: string }[]
  >([])
  const scrollRef = React.useRef<HTMLDivElement>(null)
  const [docxFile, setDocxFile] = useState<File | null>(null)
  const [modifiedContent, setModifiedContent] = useState('')

  useEffect(() => {
    // 获取完整的 URL 哈希部分
    const hash = window.location.hash

    // 在哈希中查找查询字符串的起始位置（第一个 '?'）
    const queryStringIndex = hash.indexOf('?')

    let fileParamsValue = null

    if (queryStringIndex !== -1) {
      // 提取查询字符串部分
      const queryString = hash.substring(queryStringIndex + 1)

      // 使用 URLSearchParams 对象来方便解析参数
      const params = new URLSearchParams(queryString)

      // 使用 get() 方法获取 'fileParams' 参数的原始编码值
      const rawFileParamsValue = params.get('fileParams')

      // 如果获取到了值，则进行解码
      if (rawFileParamsValue !== null) {
        try {
          fileParamsValue = decodeURIComponent(rawFileParamsValue)
        } catch (error) {
          console.error('解码 fileParams 值失败:', error)
          // 如果解码失败，可以使用原始值或者设置为 null
          // fileParamsValue = rawFileParamsValue // 或者 null
        }
      }
    }

    // 检查是否获取到了值
    if (fileParamsValue) {
      console.log('从地址栏哈希中获取到并解码 fileParams:', fileParamsValue)
      processMarkdownLinkAndUpload(fileParamsValue)
    } else {
      console.log('地址栏哈希中没有找到 fileParams 参数')
    }
  }, []) // [] 作为依赖项表示只在组件挂载时运行一次

  const processMarkdownLinkAndUpload = async (markdownLink: string) => {
    console.log('正在处理文件链接:', markdownLink)
    // 解析字符串，提取文件名和 URL
    const match = markdownLink.match(/\[(.*?)\]\((.*?)\)/)

    if (!match || match.length < 3) {
      console.error('无效的 markdown 链接格式')
      messageApi.error('无效的文件链接格式')
      return
    }

    const fileName = match[1]
    const fileUrl = match[2] // 提取到的 URL
    // const fileUrl = `https://copilot.sino-bridge.com${match[2]}` // 本地调试 提取到的 URL

    // 下载文件内容
    try {
      const response = await fetch(fileUrl)
      console.log(response, 'response')

      if (!response.ok) {
        console.error(
          `HTTP error! status: ${response.status} for URL: ${fileUrl}`
        )
        const errorText = await response.text()
        console.error('响应内容:', errorText)
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const blob = await response.blob()
      console.log('下载到的 Blob 类型:', blob.type, '大小:', blob.size)
      // 检查下载的文件类型是否是预期的 .docx 或 .pdf
      const expectedTypes = [
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
        'application/pdf', // .pdf
      ]

      if (!expectedTypes.includes(blob.type)) {
        console.warn('下载的文件类型与预期不符:', blob.type)
        messageApi.warning(
          `下载的文件类型 ${blob.type} 与预期 (.docx/.pdf) 不符.`
        )
        // 决定是否继续处理非预期类型的文件
        return; // 如果不想处理非预期类型的文件，可以 uncomment 这行
      }

      // 创建 File 对象
      // 使用提取的文件名和下载到的 blob type
      const file = new File([blob], fileName, { type: blob.type })

      console.log('创建的 File 对象:', file)

      // 调用 beforeUpload 方法
      // 注意: beforeUpload 需要处理异步上传逻辑，并且返回 false 阻止默认行为
      beforeUpload(file)
    } catch (error: any) {
      console.error('下载或处理文件失败:', error)
      messageApi.open({
        key: 'downloading',
        type: 'error',
        content: `下载或处理文件 "${fileName}" 失败: ${error.message}`,
        duration: 3,
      })
    }
  }
  const beforeUpload = (file: File) => {
    console.log(file, '上传的文件')
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf('.') + 1)
      ?.toLowerCase()
    if (['pdf', 'docx'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中',
      })
      uploadFile(file, token).then(async (response) => {
        if (response.id) {
          setUploadedFiles([response])
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1,
          })
        } else {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件上传失败',
            duration: 1,
          })
        }
      })
      //   }
      // })
    } else {
      messageApi.error('目前仅支持.docx, .pdf类型的文件')
    }
    return false
  }

  const handleDelete = (fileId: string) => {
    setUploadedFiles((prevFiles) =>
      prevFiles.filter((file) => file.id !== fileId)
    )
    setIsUploadBtn(false)
  }

  const handleDownload = async (type: string) => {
    // if (!uploadedFile.id) {
    //   messageApi.error('文件未上传')
    //   return
    // }
    if (uploadedFiles.length <= 0) {
      messageApi.error('文件未上传')
      return
    }
    const fileIds = uploadedFiles.map((file) => file.id)
    if (type === '敏感词稽查') {
      setMk('')
      setModifiedContent('')
      setStreamTypewriterKey(streamTypewriterKey + 1)
    }
    setDocxFile(null)
    setIsShowMk(true)
    setGenerating(true)
    messageApi.success('开始校验，请稍候...')
    let res = ''
    try {
      await documentSensitiveInspection(
        {
          files: fileIds.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x,
          })),
          type: type,
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              if (type === '敏感词稽查') {
                console.log(res, 'res')
                const sensitiveMatch = res.match(
                  /<敏感词筛查>([\s\S]*?)<\/敏感词筛查>/
                )
                if (sensitiveMatch && sensitiveMatch[1]) {
                  setMk(sensitiveMatch[1])
                } else if (res.startsWith('<敏感词筛查>')) {
                  setMk(res.substring('<敏感词筛查>'.length))
                } else {
                  // setMk(res)
                }

                const modifiedMatch = res.match(/【修改后的完整内容】([\s\S]*)/)
                if (modifiedMatch && modifiedMatch[1]) {
                  setModifiedContent(modifiedMatch[1])
                } else {
                  // setModifiedContent('')
                }
              }
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
              setIsUploadBtn(true)
              if (type === '文件下载') {
                console.log(res, 'res')
                // 提取()中的内容
                const parenthesesContent = res.match(/\((.*?)\)/)
                const parenthesesResult = parenthesesContent
                  ? parenthesesContent[1]
                  : null

                // 提取[]中的内容
                const squareBracketsContent = res.match(/\[(.*?)\]/)
                const squareBracketsResult = squareBracketsContent
                  ? squareBracketsContent[1]
                  : null
                console.log(parenthesesResult, 'parenthesesResult')
                console.log(squareBracketsResult, 'squareBracketsResult')
                const link = document.createElement('a')
                // link.href = `https://copilot.sino-bridge.com${parenthesesResult}` // 本地调试
                link.href = parenthesesResult
                link.download = `${squareBracketsResult}`
                document.body.appendChild(link)
                link.click()
                link.remove()
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {
            setGenerating(false)
          },
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  const handleExport = () => {
    try {
      // let aa = '关于加强跨境金融业务管理的通知  \n银管发〔2025〕15号  \n各商业银行、支付机构及合作单位：  \n根据近期监管检查发现的共性问题，依据《金融机构跨境业务管理办法》要求，现就规范事项明确如下：  \n\n一、客户信息管理规范  \n1. 跨境业务客户身份识别需包含完整要素：  \n证件信息（如居民身份证号：****************）  \n常住地址（如广东省广州市**************）  \n2. 单笔超过等值50万美元的跨境汇款，需完整报送交易对手的SWIFT CODE：***********  \n\n二、金融产品信息披露  \n1. 理财产品宣传材料必须包含：  \n产品备案编码（如\"稳盈2025\"产品应标注监管备案号）  \n收益数据需注明统计周期（不得单独标注年化收益率6.5%，需补充统计周期）  \n2. 严禁在营销材料中出现以下表述：  \n\"资金安全无风险\"等绝对化承诺  \n\"P2P资金池\"等已废止业务模式  \n\n三、跨境业务风险控制  \n1. 禁止协助客户开展虚拟货币搬砖套利活动  \n2. 涉及中国香港特别行政区的跨境贸易结算，需执行外汇局2025年2号文  \n3. 发现疑似ML交易线索应立即报送（联系人：赵主管）  \n\n四、市场信息发布准则  \n| 违规表述示例 | 合规要求 |  \n| --- | --- |  \n| \"人民币汇率将突破7.5关口\" | 不得发布未经授权的汇率预测 |  \n| \"本产品保证年化8%收益\" | 禁止刚性兑付承诺 |  \n| \"机构投资者锁仓协议\" | 需披露限制流通安排 |  \n\n注：风险告知书需满足每200字至少1处风险提示的要求，当前模板存在多处缺失。  \n\n五、自查整改要求  \n1. 重点核查历史交易中的对冲交易记录  \n2. 客户对账单中的账户余额需进行脱敏处理  \n3. 清理通过第三方结算渠道的违规操作  \n\n请各单位于2025年6月30日前完成系统改造，确保满足：  \n(1) 身份证号等敏感字段自动脱敏  \n(2) 理财产品展示完整备案信息  \n(3) 消除所有刚性兑付表述  \n\n业务咨询：刘处长（010-87654321）  \n合规监督：王主任（13901234567）  \n\n中国人民银行跨境业务监管局  \n2025年5月28日'

      const md = new MarkdownIt({ html: true })
      const html = md.render(modifiedContent)

      // 解析 HTML 并转换为 docx 格式的段落
      const parser = new DOMParser()
      const docEl = parser.parseFromString(html, 'text/html').body
      const paragraphs: Paragraph[] = []
      console.log (docEl, 'docEl')
      // 遍历 HTML 节点并转换为 docx 段落
      docEl.childNodes.forEach((node) => {
        if (node.nodeType === Node.TEXT_NODE && node.textContent) {
          // 纯文本
          paragraphs.push(
            new Paragraph({
              children: [new TextRun(node.textContent)],
            })
          )
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement
          switch (element.tagName.toLowerCase()) {
            case 'html':
              // 如果需要处理整个 HTML 文档，可以在这里添加逻辑
              paragraphs.push(
                new Paragraph({
                  children: [new TextRun('HTML Document Start')],
                })
              )
              break
            case 'body':
              // 如果需要处理 body 标签内容，可以在这里添加逻辑
              paragraphs.push(
                new Paragraph({
                  children: [new TextRun('Body Content Start')],
                })
              )
              break
            case 'h1':
              paragraphs.push(
                new Paragraph({
                  heading: HeadingLevel.HEADING_1,
                  children: [new TextRun(element.textContent || '')],
                })
              )
              break
            case 'h2':
              paragraphs.push(
                new Paragraph({
                  heading: HeadingLevel.HEADING_2,
                  children: [new TextRun(element.textContent || '')],
                })
              )
              break
            case 'p':
              paragraphs.push(
                new Paragraph({
                  children: [new TextRun(element.textContent || '')],
                })
              )
              break
            case 'ul':
              element.querySelectorAll('li').forEach((li) => {
                paragraphs.push(
                  new Paragraph({
                    children: [
                      new TextRun({
                        text: `• ${li.textContent || ''}`,
                        bold: li.innerHTML.includes('<strong>'),
                      }),
                    ],
                    indent: { left: 720 },
                  })
                )
              })
              break
            case 'table':
              const rows = element.querySelectorAll('tr')
              const tableRows: TableRow[] = []

              rows.forEach((row) => {
                const cells = Array.from(row.cells)
                const tableCells: TableCell[] = []

                cells.forEach((cell) => {
                  const colspan = parseInt(
                    cell.getAttribute('colspan') || '1',
                    10
                  )
                  const rowspan = parseInt(
                    cell.getAttribute('rowspan') || '1',
                    10
                  )
                  const cellText = cell.textContent?.trim() || ''

                  // 创建表格单元格
                  tableCells.push(
                    new TableCell({
                      children: [new Paragraph(cellText)],
                      columnSpan: colspan > 1 ? colspan : undefined,
                      rowSpan: rowspan > 1 ? rowspan : undefined,
                    })
                  )
                })

                // 创建表格行
                tableRows.push(new TableRow({ children: tableCells }))
              })

              // 创建表格并添加到文档
              const table = new Table({
                rows: tableRows,
                width: { size: 100, type: WidthType.PERCENTAGE }, // 设置表格宽度为 100%
              })

              paragraphs.push(table as any)
              break
            default:
              paragraphs.push(
                new Paragraph({
                  children: [new TextRun(element.textContent || '')],
                })
              )
          }
        }
      })

      // 创建 Word 文档
      const doc = new Document({
        styles: {
          paragraphStyles: [
            {
              id: 'Heading1',
              name: 'Heading 1',
              run: {
                size: 32,
                bold: true,
                color: '2e6c80',
              },
            },
          ],
        },
        sections: [{ children: paragraphs }],
      })

      // 导出 Word
      Packer.toBlob(doc).then((blob) => {
        saveAs(blob, `${new Date().getTime()}.docx`)
      })
    } catch (error) {
      console.error('导出失败:', error)
    }
  }
  const handleCopy = () => {
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard
        .writeText(modifiedContent)
        .then(() => message.success('已复制到剪贴板'))
        .catch(() => fallbackCopyTextToClipboard(modifiedContent))
    } else {
      fallbackCopyTextToClipboard(modifiedContent)
    }
  }
  function fallbackCopyTextToClipboard(text: string) {
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.top = '0'
    textArea.style.left = '0'
    textArea.style.width = '2em'
    textArea.style.height = '2em'
    textArea.style.padding = '0'
    textArea.style.border = 'none'
    textArea.style.outline = 'none'
    textArea.style.boxShadow = 'none'
    textArea.style.background = 'transparent'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    try {
      const successful = document.execCommand('copy')
      if (successful) {
        message.success('已复制到剪贴板')
      } else {
        message.error('复制失败，请手动复制')
      }
    } catch (err) {
      message.error('复制失败，请手动复制')
    }
    document.body.removeChild(textArea)
  }

  return (
    <div className="document-format-verification-container">
      <Spin tip="加载中" spinning={generating} fullscreen size="large" />
      {contextHolder}
      <Layout style={{ height: '100%' }}>
        <Layout.Sider
          width={isShowMk ? '40%' : '100%'}
          style={{ backgroundColor: '#f0f2f5', padding: '0 20px' }}
        >
          <Card className="document-verification-card">
            <Title level={2} className="page-title">
              公文敏感词稽查
            </Title>
            <Text type="secondary" className="page-description">
              您好，我是您的 公文规范 助手，
            </Text>
            <Text
              type="secondary"
              className="page-description"
              style={{ marginTop: 0 }}
            >
              您可以上传文件，我会为您进行敏感词稽查。
            </Text>

            <Upload.Dragger
              multiple={false}
              accept=".doc,.docx,.pdf"
              showUploadList={false}
              beforeUpload={(file) => beforeUpload(file)}
              style={{ marginTop: '16px' }}
            >
              <div className="ant-upload-drag-icon">
                {uploadedFiles.length > 0 ? (
                  <CheckCircleFilled />
                ) : (
                  <InboxOutlined />
                )}
              </div>
              <div className="ant-upload-hint">
                <p style={{ margin: '0 10%' }}>
                  建议上传pdf、docx纯文档格式的文件、字数过多以及包含表格、图片等情况可能会影响效果
                </p>
                <p></p>
                <br />
              </div>
            </Upload.Dragger>

            {/* 文件列表 */}
            {uploadedFiles.length > 0 && (
              <div className="file-list">
                {uploadedFiles.map((x) => (
                  <p key={x.id}>
                    <Tag
                      closeIcon
                      style={{
                        marginTop: 4,
                        maxWidth: '100%',
                        whiteSpace: 'normal',
                        height: 'auto',
                        wordBreak: 'break-all',
                      }}
                      onClose={() => {
                        handleDelete(x.id)
                      }}
                    >
                      {x.name}
                    </Tag>
                  </p>
                ))}
              </div>
            )}

            <Button
              type="primary"
              onClick={() => {
                handleDownload('敏感词稽查')
              }}
              size="large"
              block
              style={{ marginTop: '20px' }}
            >
              敏感词稽查
            </Button>
          </Card>
        </Layout.Sider>
        {isShowMk && (
          <Layout.Content style={{ padding: 24, background: '#fff' }}>
            {/* <Flex align="center" justify="center" style={{ height: '50px' }}>
              <Title level={3}></Title>
            </Flex> */}

            <div
              className="scroll-container"
              ref={scrollRef}
              style={{ height: 'calc(100vh - 98px)', overflowY: 'auto' }}
            >
              {mk && (
                <>
                  <Flex
                    align="center"
                    justify="left"
                    style={{ height: '50px', marginTop: '20px' }}
                  >
                    <Title level={3}>稽查结果：</Title>
                  </Flex>
                  <Card>
                    <StreamTypewriter
                      key={streamTypewriterKey}
                      text={mk}
                      end={!generating}
                      onchange={() => {
                        scrollRef.current?.scrollTo({
                          top: scrollRef.current.scrollHeight,
                          behavior: 'smooth',
                        })
                      }}
                      components={{}}
                    />
                  </Card>
                </>
              )}

              {modifiedContent && (
                <>
                  <Flex
                    align="center"
                    justify="space-between"
                    style={{
                      height: '50px',
                      marginTop: '20px',
                      marginBottom: '12px',
                    }}
                  >
                    <Title level={3} style={{ marginBottom: 0 }}>
                      修正后的文章：
                    </Title>
                    <Button type="link" onClick={handleCopy}>
                      复制全文
                    </Button>
                  </Flex>
                  <Card>
                    <StreamTypewriter
                      key={`${streamTypewriterKey}-modified`}
                      text={modifiedContent}
                      end={!generating}
                      onchange={() => {
                        scrollRef.current?.scrollTo({
                          top: scrollRef.current.scrollHeight,
                          behavior: 'smooth',
                        })
                      }}
                      components={
                        {
                          // Define any necessary components here
                        }
                      }
                    />
                  </Card>
                </>
              )}
            </div>
            <Flex align="center" justify="center" gap="middle">
              <Button
                onClick={() => {
                  handleDownload('敏感词稽查')
                }}
                size="large"
                style={{ marginTop: '20px' }}
              >
                重新生成
              </Button>
              <Button
                type="primary"
                onClick={handleExport}
                size="large"
                style={{ marginTop: '20px' }}
              >
                下载文件
              </Button>
            </Flex>
          </Layout.Content>
        )}
      </Layout>
    </div>
  )
}

export default DocumentSensitiveInspection
