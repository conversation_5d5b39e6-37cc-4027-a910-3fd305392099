import { useState, useCallback, useRef } from 'react'
import GetK<PERSON> from '@/component/getKey'
import { uploadFile } from '@/api/template'
import { prdContractVer } from '@/api/prdContractVer'
import { extractContent } from '@/utils/common'
import StreamTypewriter from '@/component/StreamTypewriter'
import { Spin, Upload, Button, Card, Space, Typography, Tree, message, Row, Col } from 'antd'
import { UploadOutlined, FullscreenOutlined, DownloadOutlined, FilePdfOutlined } from '@ant-design/icons'
import { extractJSONFromString } from '@/utils/json-extractor'
import './index.less'

const { Title, Text } = Typography
const appkey = import.meta.env['VITE_PRD_CONTRACT_VER_TOKEN'] || ''

interface ValidationRuleResult {
  规则项: string
  检验结果: string
  分析内容: string
}

type ValidationResults = ValidationRuleResult[]
interface TreeNode {
  key: string
  title: string
  children?: TreeNode[]
}

export const PrdContractVer = () => {
  const [key, setKey] = useState('')
  const [open, setOpen] = useState(false)
  const [messageApi, contextHolder] = message.useMessage()
  const [uploadedFiles, setUploadedFiles] = useState<{ id: string; name: string }[]>([])
  const [fileUrl, setFileUrl] = useState('')
  const [validationResult, setValidationResult] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [expandedKeys, setExpandedKeys] = useState(['financial', 'contract'])
  const [checkedKeys, setCheckedKeys] = useState([
    'financial',
    'financial-totalAmount',
    'financial-paymentMethod',
    'financial-taxResponsibility',
    'financial-extraFees',
    'financial-priceAdjustment',
    'contract',
    'contract-downPayment',
    'contract-progressPayment',
    'contract-acceptancePayment',
    'contract-warrantyPayment',
    'contract-latePayment'
  ])

  // 校验规则树数据
  const [treeData] = useState([
    {
      key: 'financial',
      title: '付款金额',
      children: [
        {
          key: 'financial-totalAmount',
          title: '总金额是否明确',
          isLeaf: true
        },
        {
          key: 'financial-paymentMethod',
          title: '付款方式是否约定',
          isLeaf: true
        },
        {
          key: 'financial-taxResponsibility',
          title: '税费承担是否明确',
          isLeaf: true
        },
        {
          key: 'financial-extraFees',
          title: '额外费用处理是否约定',
          isLeaf: true
        },
        {
          key: 'financial-priceAdjustment',
          title: '价格调整机制是否明确',
          isLeaf: true
        }
      ]
    },
    {
      key: 'contract',
      title: '付款节点',
      children: [
        {
          key: 'contract-downPayment',
          title: '首付款条件是否明确',
          isLeaf: true
        },
        {
          key: 'contract-progressPayment',
          title: '进度款节点是否合理',
          isLeaf: true
        },
        {
          key: 'contract-acceptancePayment',
          title: '验收款比例是否合理',
          isLeaf: true
        },
        {
          key: 'contract-warrantyPayment',
          title: '质保金比例是否约定',
          isLeaf: true
        },
        {
          key: 'contract-latePayment',
          title: '付款延迟处理是否约定',
          isLeaf: true
        }
      ]
    }
  ])

  const getOrderedSelectedTitles = (treeData: TreeNode[], checkedKeys: string[]): string[] => {
    const titles: string[] = []

    // 递归遍历树节点并收集标题
    const traverse = (nodes: TreeNode[]) => {
      nodes.forEach(node => {
        // 检查当前节点是否应该包含其标题
        const shouldIncludeNode =
          // 节点被直接选中
          checkedKeys.includes(node.key) ||
          // 或者有子节点被选中(半选中状态)
          checkedKeys.includes(node.key) ||
          // 或者有子节点被选中但当前节点未被标记为半选中
          (node.children && node.children.some(child => checkedKeys.includes(child.key)))

        if (shouldIncludeNode) {
          titles.push(node.title)
        }

        // 递归处理子节点
        if (node.children) {
          traverse(node.children)
        }
      })
    }

    traverse(treeData)
    return titles
  }

  function jsonToMarkdownTable(data: ValidationResults): string {
    // 表头
    let table = `| 规则项 | 检验结果 | 分析内容 |\n`
    table += `|--------|----------|----------|\n`

    // 表格内容
    data.forEach(item => {
      table += `| ${item.规则项} | ${item.检验结果} | ${item.分析内容} |\n`
    })

    return table
  }

  const handleGeneration = useCallback(async () => {
    if (!key) {
      setOpen(true)
      return
    }
    if (uploadedFiles.length === 0) {
      messageApi.error('请上传至少一个文件')
      return
    }
    if (checkedKeys.length === 0) {
      messageApi.error('请勾选至少一条校验规则')
      return
    }
    const expandedTitles = getOrderedSelectedTitles(treeData, checkedKeys)
    setIsLoading(true)
    let messages = ''
    try {
      await prdContractVer(
        {
          key,
          query: expandedTitles.join(' '),
          files: uploadedFiles.map(x => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x.id
          }))
        },
        {
          onMessage: (text: string | null) => {
            if (text) {
              messages += text
            }
          },
          onError: () => {
            setIsLoading(false)
          },
          onFinish: () => {
            setIsLoading(false)
            const errorStr = extractContent(messages, 'error')
            if (errorStr) {
              messageApi.error(errorStr)
              setKey('')
              setOpen(true)
              return
            }
            const str = extractJSONFromString(messages)
            try {
              let arr = JSON.parse(str || '[]')
              let result = jsonToMarkdownTable(arr)
              setValidationResult(result)
            } catch (error) {
              setValidationResult('数据异常')
            }
          }
        }
      )
    } catch (err) {
      setIsLoading(false)
    }
  }, [key, uploadedFiles, checkedKeys])

  const pdfViewerRef = useRef(null)

  const onExpand = (keys: any) => {
    setExpandedKeys(keys)
  }

  const onCheck = (keys: any) => {
    setCheckedKeys(keys)
  }

  const downloadPDF = () => {
    const link = document.createElement('a')
    link.href = fileUrl
    link.download = '合同.pdf'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const uploadProps = {
    name: 'file',
    accept: '.pdf',
    showUploadList: false,
    beforeUpload: (file: File) => {
      if (!key) {
        setOpen(true)
        return false
      }
      const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)?.toLowerCase()
      if (['pdf'].includes(originalFileExt)) {
        setIsLoading(true)

        const url = URL.createObjectURL(file)

        setFileUrl(url)

        uploadFile(file, appkey).then(async response => {
          setIsLoading(false)
          if (response.id) {
            setUploadedFiles([response])
            messageApi.open({
              key: 'uploading',
              type: 'success',
              content: '文件上传成功',
              duration: 1
            })
          } else {
            messageApi.open({
              key: 'uploading',
              type: 'error',
              content: '文件上传失败',
              duration: 1
            })
          }
        })
      } else {
        messageApi.error('目前仅支持.pdf类型的文件，请您将文件转成这些格式后再次进行上传')
      }
      return false
    }
  }

  const toggleFullscreen = () => {
    if (pdfViewerRef.current) {
      if (!isFullscreen) {
        ;(pdfViewerRef?.current as any).requestFullscreen().then(() => {
          setIsFullscreen(true)
        })
      } else {
        document.exitFullscreen()
        setIsFullscreen(false)
      }
    }
  }

  const onSelect = (_selectedKeys: any, info: any) => {
    // 当点击树节点时，同步更新复选框状态
    const node = info.node
    const key = node.key
    let newCheckedKeys = { ...checkedKeys }

    if (checkedKeys.includes(key)) {
      // 如果已选中，则取消选中
      newCheckedKeys = checkedKeys.filter(k => k !== key)
    } else {
      // 如果未选中，则选中
      newCheckedKeys = [...checkedKeys, key]
    }

    setCheckedKeys(newCheckedKeys)
  }

  const scrollToBottom = () => {
    const root = document.getElementById('root')
    root?.scrollTo({
      top: root.scrollHeight, // 滚动到页面最底部
      behavior: 'smooth' // 可选：平滑滚动
    })
  }

  return (
    <>
      {contextHolder}
      <Spin tip='处理中...' spinning={isLoading} fullscreen size='large' />
      <GetKey open={open} onClose={setOpen} onChange={setKey} />

      <div
        style={{
          backgroundColor: '#f0f2f5',
          minHeight: '100vh',
          padding: '24px 0'
        }}
      >
        <div
          style={{
            maxWidth: 1200,
            margin: '0 auto',
            padding: '0 24px'
          }}
        >
          <Title level={2} style={{ textAlign: 'center', marginBottom: 24 }}>
            合同校验系统
          </Title>

          <Row gutter={24}>
            {/* 左侧：校验规则 */}
            <Col xs={24} md={12}>
              <Space direction='vertical' size='middle' style={{ width: '100%' }}>
                {/* 上传按钮 */}
                <Card
                  title='合同上传'
                  styles={{
                    header: { borderBottom: '1px solid #f0f0f0' }
                  }}
                >
                  <Space>
                    <Upload {...uploadProps}>
                      <Button type='primary' icon={<UploadOutlined />}>
                        上传合同
                      </Button>
                    </Upload>
                    <Text type='secondary'>仅支持 PDF 格式</Text>
                  </Space>
                </Card>

                {/* 校验规则选择 - 使用Tree组件 */}
                <Card
                  title='校验规则'
                  styles={{
                    header: { borderBottom: '1px solid #f0f0f0' }
                  }}
                >
                  <Tree
                    checkable
                    onExpand={onExpand}
                    expandedKeys={expandedKeys}
                    onCheck={onCheck}
                    checkedKeys={checkedKeys}
                    treeData={treeData}
                    height={400}
                    style={{
                      padding: '8px 0',
                      backgroundColor: '#fff'
                    }}
                    selectable={false}
                    onSelect={onSelect}
                  />
                </Card>

                <Button
                  type='primary'
                  onClick={handleGeneration}
                  loading={isLoading}
                  style={{
                    backgroundColor: '#52c41a',
                    borderColor: '#52c41a',
                    width: '100%',
                    height: 40
                  }}
                >
                  开始校验
                </Button>

                {/* 校验结果 */}
                <Card
                  title='校验结果'
                  styles={{
                    header: { borderBottom: '1px solid #f0f0f0' },
                    body: {
                      padding: validationResult ? '24px' : '48px 24px',
                      minHeight: 200
                    }
                  }}
                >
                  {validationResult ? (
                    <StreamTypewriter
                      key={validationResult}
                      text={validationResult}
                      end={!isLoading}
                      onchange={() => {
                        scrollToBottom()
                      }}
                    />
                  ) : (
                    <div style={{ textAlign: 'center' }}>
                      <Text type='secondary'>请上传文件并选择校验规则</Text>
                    </div>
                  )}
                </Card>
              </Space>
            </Col>

            {/* 右侧：PDF 预览 */}
            <Col xs={24} md={12}>
              <Card
                title='合同预览'
                extra={
                  fileUrl && (
                    <Space>
                      <Button icon={<FullscreenOutlined />} onClick={toggleFullscreen} title='全屏' type='text' />
                      <Button icon={<DownloadOutlined />} onClick={downloadPDF} title='下载' type='text' />
                    </Space>
                  )
                }
              >
                {fileUrl ? (
                  <div
                    style={{
                      minHeight: 'calc(100vh - 160px)'
                    }}
                    ref={pdfViewerRef}
                  >
                    <embed
                      style={{ width: '100%', height: '100%', minHeight: 'calc(100vh - 160px)' }}
                      type='application/pdf'
                      src={fileUrl + '#toolbar=0&navpanes=0&scrollbar=0'}
                    ></embed>
                  </div>
                ) : (
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                      minHeight: 'calc(100vh - 160px)',
                      color: '#bfbfbf'
                    }}
                  >
                    <FilePdfOutlined style={{ fontSize: 64, marginBottom: 16 }} />
                    <Text type='secondary' style={{ fontSize: 16 }}>
                      暂无文件预览
                    </Text>
                    <Text type='secondary'>请上传 PDF 格式的合同文件</Text>
                  </div>
                )}
              </Card>
            </Col>
          </Row>
        </div>
      </div>
    </>
  )
}

export default PrdContractVer
