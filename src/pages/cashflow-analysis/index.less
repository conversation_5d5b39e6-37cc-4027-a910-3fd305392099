.cashflow-analysis-container {
  height: 100vh;
  background-color: #f0f2f5;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;

  .ant-upload.ant-upload-btn {
    padding: 6px 10px;
  }
  .upload-drag-icon {
    font-size: 48px;
    color: #1890ff;
    transition: transform 0.3s ease;
  }
  .ant-upload-drag-icon:hover {
    transform: scale(1.05);
  }

  .ant-upload-text,
  .ant-upload-hint {
    font-size: 14px;
  }
  .btn-list {
    position: absolute;
    bottom: 20px;
    width: 100%;
    text-align: center;
  }

  /* 确保每个表格都有独立的滚动条 */
  .markdown-body table {
    display: block;
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    margin-bottom: 1rem; /* 添加间距 */
    border-collapse: collapse;
  }

  /* 可选：固定表头（如果需要） */
  .markdown-body thead {
    position: sticky;
    top: 0;
    background-color: white;
  }

  .markdown-body table::-webkit-scrollbar {
    height: 8px;
  }

  .markdown-body table::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .markdown-body table::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }

  .markdown-body table::-webkit-scrollbar-thumb:hover {
    background: #555;
  } /* 斑马条纹效果 */

  .markdown-body tr:nth-child(even) {
    background-color: #f2f2f2;
  }

  /* 单元格样式 */
  .markdown-body td,
  .markdown-body th {
    padding: 8px 12px;
    border: 1px solid #ddd;
    text-align: left;
  }

  /* 表头样式 */
  .markdown-body th {
    background-color: #f8f8f8;
    font-weight: bold;
  }
}

.cashflow-analysis-card {
  width: 100%;
  max-width: 600px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  transition: all 0.3s ease;
  margin: 20vh auto;
  flex: 1;

  .page-title {
    margin-bottom: 16px;
    color: #1f1f1f;
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      font-size: 24px;
      color: #1890ff;
    }
  }

  .page-description {
    display: block;
    margin: 16px 0;
    font-size: 14px;
  }

  .upload-form {
    .ant-form-item {
      margin-bottom: 24px;
    }

    .ant-upload {
      width: 100%;
    }

    .ant-btn {
      width: 100%;
      height: 40px;
    }

    .file-name {
      display: block;
      margin-top: 8px;
      font-size: 14px;
    }
  }

  .ant-upload-list {
    margin-top: 8px;
  }
}

.cashflow-analysis-toolbar {
  background: linear-gradient(180deg, rgba(189, 225, 255, 0.4) 0%, rgba(224, 242, 255, 0) 100%);
  border-radius: 0.5rem 0.5rem 0 0;
  padding: 12px 24px;

  .title-text {
    color: transparent;
    background: linear-gradient(116deg, #1888ff 16%, #2f54eb 88%);
    background-clip: text;
    -webkit-background-clip: text;
    user-select: none;
    font-size: 30px;
    font-weight: bold;
  }
}
