import React, { useRef, useState } from 'react'
import { Upload, Button, Form, message, Card, Typography, Spin, Flex, Row, Col, Tag } from 'antd'
import { FileTextOutlined, InboxOutlined } from '@ant-design/icons'
import { cashflowAnalysis } from '@/api/cashflowAnalysis'
import { uploadFile } from '@/api/template.ts'
import StreamTypewriter from '@/component/StreamTypewriter'
import { FullPageScroll, FullPageScrollHandle } from './FullPageScroll'
import ReactECharts from 'echarts-for-react'
import './index.less'

const { Title, Text } = Typography
const FEASIBILITY_REPORT_TOKEN = import.meta.env['VITE_FEASIBILITY_REPORT_TOKEN'] || ''

interface FileState {
  file: File | null
  name: string
  id: string | number
}

export const CashflowAnalysis: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<FileState[]>([])
  const [count, setCount] = useState(0)
  const [content1, setContent1] = useState('')
  const [content2, setContent2] = useState('')
  const childRef = useRef<FullPageScrollHandle>(null)

  const beforeUpload = (file: File) => {
    messageApi.open({
      key: 'uploading',
      type: 'loading',
      content: '文件上传中'
    })
    setGenerating(true)
    uploadFile(file, FEASIBILITY_REPORT_TOKEN)
      .then(response => {
        setGenerating(false)
        if (response && response.id) {
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
          setUploadedFiles(x => [
            ...x,
            {
              file: file,
              name: file.name,
              id: response.id
            }
          ]) // 更新文件列表
          return false // 阻止自动上传
        } else {
          throw new Error('上传失败：未收到有效的响应')
        }
      })
      .catch(error => {
        setGenerating(false)
        messageApi.open({
          key: 'uploading',
          type: 'error',
          content: `文件上传失败: ${error.message}`,
          duration: 2
        })
      })
    return false // 阻止自动上传
  }

  const handleGeneratedContent = async () => {
    if (uploadedFiles.length === 0) {
      messageApi.error('请上传所有必需文件')
      return
    }
    setContent1('')
    setContent2('')
    setCount(p => p + 1)
    childRef.current?.handleNavClick(1)
    setGenerating(true)
    messageApi.success('正在分析，请稍候...')
    let res = ''
    let forecastContent = '' // 存储【资金预测情况】相关内容
    let otherContent = '' // 存储其他内容
    let count = 0
    try {
      await cashflowAnalysis(
        {
          financial_report: uploadedFiles.map(x => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x.id
          }))
        },
        {
          onMessage: (message: string | null) => {
            if (message) {
              res += message || ''
              // 根据<财务预测>分类处理
              if (res.includes('<财务预测>')) {
                otherContent = res.split('<财务预测>')[1] || ''
                setContent2(otherContent)
                if (count === 0) {
                  forecastContent += res.split('<财务预测>')[0] || ''
                  setContent1(forecastContent)
                  count++
                }
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {
            setGenerating(false)
          }
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  const back = () => {
    childRef.current?.handleNavClick(0)
  }

  interface SectionItem {
    name: string
    children: React.ReactNode
  }

  const sections: SectionItem[] = [
    {
      name: '1',
      children: (
        <>
          <div style={{ position: 'sticky', zIndex: 1 }}>
            <Flex vertical className='cashflow-analysis-toolbar' justify='center' align='center'>
              <Typography.Title className='title-text'>企业现金流预测</Typography.Title>
            </Flex>
          </div>
          <Card className='cashflow-analysis-card'>
            <Title level={2} className='page-title'>
              <FileTextOutlined /> 企业现金流预测
            </Title>
            <Text type='secondary' className='page-description'>
              您好，我是您的财务分析师，建议上传近3年企业财报，进行未来企业现金流预测。
            </Text>

            <Form layout='vertical' className='upload-form'>
              <Form.Item label='文件上传' required>
                <Upload.Dragger
                  showUploadList={false}
                  beforeUpload={(file: File) => {
                    if (uploadedFiles.length >= 3) {
                      message.error('最多只能上传 3 个文件')
                      return Upload.LIST_IGNORE // 阻止文件上传
                    }
                    beforeUpload(file) // 调用原有的上传逻辑
                    return false // 阻止自动上传
                  }}
                >
                  <p className='upload-drag-icon'>
                    <InboxOutlined />
                  </p>
                  <p>请拖拽文件到此处或点击上传文件按钮</p>
                  <p>建议上传 pdf、docx 纯文档格式文件</p>
                </Upload.Dragger>
                {uploadedFiles.length > 0 && (
                  <>
                    {uploadedFiles.map(x => (
                      <p key={x.id} style={{ marginTop: 6 }}>
                        <Tag
                          closeIcon
                          style={{ marginTop: 4 }}
                          onClose={() => {
                            setUploadedFiles(prevList => prevList.filter(y => y.id !== x.id))
                            return false
                          }}
                        >
                          {x.name}
                        </Tag>
                      </p>
                    ))}
                  </>
                )}
              </Form.Item>

              <Form.Item>
                <Button type='primary' onClick={handleGeneratedContent} size='large' block>
                  下一步
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </>
      )
    },
    {
      name: '2',
      children: (
        <div style={{ background: '#fff', height: '100%' }}>
          <div style={{ position: 'sticky', zIndex: 1, top: 0, background: '#fff' }}>
            <Flex vertical className='cashflow-analysis-toolbar' justify='center' align='center'>
              <Typography.Title className='title-text'>企业现金流预测</Typography.Title>
            </Flex>
          </div>
          <div key={count} style={{ padding: '0 24px 24px 24px' }}>
            <Row gutter={48}>
              <Col xs={24} md={13}>
                <Title level={4} className='page-title'>
                  企业历史资金流分析
                </Title>
                <div
                  className='scroll-container'
                  style={{
                    border: '1px solid #ccc',
                    padding: '24px',
                    height: 'calc(100vh - 210px)',
                    overflowY: 'auto',
                    overflowX: 'hidden',
                    borderRadius: '8px'
                  }}
                >
                  <StreamTypewriter text={content1} end={!generating} />
                </div>
              </Col>
              <Col xs={24} md={11}>
                <Title level={4} className='page-title'>
                  企业未来资金流预测
                </Title>
                <div
                  className='scroll-container'
                  style={{
                    border: '1px solid #ccc',
                    padding: '24px',
                    height: 'calc(100vh - 210px)',
                    overflowY: 'auto',
                    overflowX: 'hidden',
                    borderRadius: '8px'
                  }}
                >
                  <StreamTypewriter
                    text={content2}
                    end={!generating}
                    components={{
                      code({ node, inline, className, children, ...props }: any) {
                        // 检查是否为 ```echarts 代码块
                        const match = /language-(\w+)/.exec(className || '')
                        if (match && match[1]) {
                          try {
                            // 解析 option
                            const option = JSON.parse((children as string).replace('```echarts', '').replace('```', ''))
                            return (
                              <ReactECharts option={option} style={{ height: 400, width: '100%', margin: '24px 0' }} />
                            )
                          } catch (e) {
                            return <pre style={{ color: 'red' }}>图表生成中...</pre>
                          }
                        }
                        // 其他代码块正常渲染
                        return (
                          <code className={className} {...props}>
                            {children}
                          </code>
                        )
                      }
                    }}
                    charsPerUpdate={10}
                  />
                </div>
              </Col>
            </Row>
          </div>

          <Flex className='btn-list' gap={24} justify='center'>
            <Button onClick={() => back()}>返回上一步</Button>
            <Button onClick={handleGeneratedContent}>重新生成</Button>
          </Flex>
        </div>
      )
    }
  ]

  return (
    <div className='cashflow-analysis-container'>
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      {contextHolder}

      <FullPageScroll sections={sections} ref={childRef} />
    </div>
  )
}

export default CashflowAnalysis
