import { useCallback, useState } from "react";
import { Button, <PERSON>po<PERSON>, Card, Spin, Flex, Input, message } from "antd";
import { industryChainMapContract } from "@/api/industryChainMap";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import "./index.less";

export const IndustryChainMap = () => {
  const [generating, setGenerating] = useState<boolean>(false);
  const [messages, setMessages] = useState<string>("");
  const [markdownTable, setMarkdownTable] = useState("");
  const list = [
    {
      id: "app-8bWoar9nhBpiaqDTiQgq4U7K",
      name: "政策解读",
    },
    {
      id: "app-V6ukWxRhBIIHz284rih9NshM",
      name: "产业链图谱",
    },
    {
      id: "app-0h2xigs6So4EtOto4wWwmwBf",
      name: "峰会分析",
    },
    {
      id: "3",
      name: "国家会议分析",
    },
    {
      id: "4",
      name: "舆情分析",
    },
  ];
  const [selectData, setSelectData] = useState<any>(list[0]);
  const [queryContent, setQueryContent] = useState<string>("");
  // 状态管理
  const [activeKey, setActiveKey] = useState(list[0].id);

  // Tabs切换事件处理
  const handleTabChange = (key: any) => {
    // 过滤不可用标签的切换
    const targetApp = list.find((app) => app.id === key);
    setSelectData(targetApp);
    setActiveKey(key);
  };
  const handleGenerationStart = () => {
    handleGeneration([]);
    setMarkdownTable("");
  };

  const handleGeneration = useCallback(
    async (fileIds: string[]) => {
      setGenerating(true);
      let accumulatedMessages = "";
      try {
        await industryChainMapContract(
          {
            query: queryContent,
            appData: selectData,
            files: fileIds.map((x) => ({
              type: "document",
              transfer_method: "local_file",
              upload_file_id: x,
            })),
          },
          {
            onMessage: (text: string | null, finished: boolean) => {
              if (text) {
                accumulatedMessages += text;
              }
              if (finished) {
                console.log(accumulatedMessages);
                setGenerating(false);
                try {
                  const cleanedData = accumulatedMessages.replace(
                    /^```markdown\s*|```$/g,
                    ""
                  );
                  setMarkdownTable(cleanedData);
                } catch (e) {
                  console.log(accumulatedMessages);
                  setMessages(accumulatedMessages);
                }
              }
            },
            onError: () => {
              setGenerating(false);
            },
            onFinish: () => {
              setGenerating(false);
            },
          }
        );
      } catch (err) {
        setGenerating(false);
      }
    },
    [queryContent, selectData]
  );
  return (
    <>
      {/* <Spin tip="加载中" spinning={generating} fullscreen size="large" /> */}

      <Flex className="toolbar" justify="center">
        <Typography.Text className="title-text">产业链分析助手</Typography.Text>
      </Flex>

      <div
        style={{
          width: "1200px",
          overflow: "hidden",
          padding: "0 20px",
          margin: "0px auto",
        }}
      >
        <div style={{ maxWidth: 1200, margin: "0 auto", padding: 20 }}>
          {/* 头部输入区域 */}
          <div style={{ marginBottom: 40 }}>
            <div style={{ display: "flex", gap: 16, marginBottom: 24 }}>
              <Input
                placeholder="这里输入政策名称、行业名称、公司名称进行查找或分析"
                style={{ flex: 1 }}
                v-model="queryContent"
                allowClear
                onChange={(e) => {
                  setQueryContent(e.target.value.trim());
                }}
              />
              <Button
                type="primary"
                size="large"
                style={{ width: 168 }}
                disabled={!queryContent || generating}
                onClick={handleGenerationStart}
              >
                开始分析
              </Button>
            </div>
          </div>

          {/* 导航标签 */}
          <div
            style={{
              display: "flex",
              gap: 8,
              marginBottom: 24,
            }}
          >
            {list.map((app, index) => (
              <Button
                key={app.id}
                type={activeKey === app.id ? "primary" : "link"}
                style={{
                  width: 120,
                  borderRadius: "8px",
                  border: activeKey === app.id ? "1x solid #1684FC" : "",
                  background:
                    activeKey === app.id ? "rgba(22, 132, 252, 0.25)" : "",
                  color:
                    index >= 3
                      ? "rgba(0,0,0,.25)"
                      : activeKey === app.id
                      ? "#165DFF"
                      : "rgba(0,0,0,.85)",
                }}
                onClick={() => handleTabChange(app.id)}
                disabled={index >= 3}
              >
                {app.name}
              </Button>
            ))}
          </div>

          {/* 说明区域 */}
        </div>
        <Flex align="center" justify="center" className="industry-content">
          {markdownTable ? (
            <Card style={{ width: "100%" }}>
              <ReactMarkdown
                className="markdown-body"
                remarkPlugins={[remarkGfm]}
              >
                {markdownTable}
              </ReactMarkdown>
            </Card>
          ) : (
            <Flex
              justify="justify"
              align="center"
              vertical
              style={{ marginTop: 50 }}
            >
              {generating && <Spin size="large" />}
              <Typography.Text>
                <div style={{ marginTop: 30 }}>
                  <p style={{ color: "#666" }}>
                    <strong>推荐输入</strong>
                    <br />
                    政策名称、行业名称，根据需要进行分析解读操作
                  </p>
                  <p style={{ color: "#666", marginTop: 16 }}>
                    <strong>公司分析</strong>
                    <br />
                    可以输入具体的公司名称，我们可根据您输入的公司名称，分析其所在行业、产业、产业链相关信息
                  </p>
                  <p style={{ color: "#999", marginTop: 16 }}>
                    输入内容后点击上方的开始按钮开始分析...
                  </p>
                </div>
              </Typography.Text>
            </Flex>
          )}
        </Flex>
      </div>
    </>
  );
};

export default IndustryChainMap;
