import React, { useCallback, useState } from 'react'
import { Spin, message, Flex, Typography, Steps, Button } from 'antd'
// import GetKey from '@/component/getKey'
import { Template } from './componment/template'
import Step2BasicInfo from './componment/Step2BasicInfo'
import Step3Analysis from './componment/Step3Analysis'
import { securitiesValuation } from '@/api/securitiesValuation'
import { extractJSONFromString } from '@/utils/json-extractor'
import { extractContent } from '@/utils/common'
import './index.less'

const { Step } = Steps

export const DueDiligenceReport: React.FC = () => {
  const [key, setKey] = useState('')
  const [open, setOpen] = useState(false)
  const [managerName, setManagerName] = useState<string>('')
  const [messageApi, contextHolder] = message.useMessage()
  const [step3message, setStep3message] = useState<string>('')
  const [step3Financialmessage, setStep3Financialmessage] = useState<string>('')
  const [generating, setGenerating] = useState<boolean>(false)
  const [templateData1, setTemplateData1] = useState<string>('')
  const [templateData2, setTemplateData2] = useState<string>('')
  const [uploadedFile, setUploadedFile] = useState<{ id: string; name: string }>({ id: '', name: '' })
  const [uploadedFileList, setUploadedFileList] = useState<{ id: string; name: string }[]>([])
  const [current, setCurrent] = useState(0)

  const steps = [
    {
      title: '上传模板',
      content: (
        <Template
          openTip={() => setOpen(true)}
          managerName={managerName}
          setManagerName={setManagerName}
          start={() => {
            start()
          }}
          onUpload={file => {
            setUploadedFile(file)
          }}
          onFileListChange={(list: any[]) => {
            setUploadedFileList(list)
          }}
          generating={generating}
          setGenerating={setGenerating}
        />
      )
    },
    {
      title: '模板解析',
      content: <Step2BasicInfo data={templateData1} generating={generating} />
    },
    {
      title: '网络检索',
      content: <Step3Analysis data={step3message} data1={step3Financialmessage} uploadedFileList={uploadedFileList} />
    }
  ]

  const handleGenerationStart = async () => {
    setStep3message('')
    if (uploadedFileList.length > 0) {
      setStep3Financialmessage('')
      handleGenerationStart2()
    }
    setGenerating(true)
    let res = ''
    try {
      await securitiesValuation(
        {
          type: '网络检索',
          Basic_information_securities: managerName
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              setStep3message(p => p + message)
            }
            if (finished) {
              setGenerating(false)
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                setKey('')
                setOpen(true)
                return
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  const handleGenerationStart2 = async () => {
    try {
      await securitiesValuation(
        {
          type: '财报分析',
          financial_statement: uploadedFileList.map(item => ({
            upload_file_id: item.id,
            type: 'document',
            transfer_method: 'local_file'
          })),
          template_file: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: uploadedFile.id
            }
          ]
        },
        {
          onMessage: (message: string | null) => {
            if (message) {
              setStep3Financialmessage(p => p + message)
            }
          },
          onError: () => {},
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  const nextStep = () => {
    // if (!key) {
    //   setOpen(true)
    //   return
    // }
    if (current === 1) {
      setCurrent(current + 1)
      if (step3message) return
      handleGenerationStart()
    } else {
      setCurrent(current + 1)
    }
  }

  const downloadReport = async () => {
    // if (!key) {
    //   setOpen(true)
    //   return
    // }
    setGenerating(true)
    const res2 = extractJSONFromString(templateData2)
    let res = ''

    try {
      await securitiesValuation(
        {
          type: '报告生成',
          info: res2 || '{}',
          key: key || '',
          files: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: uploadedFile.id
            }
          ],
          query: step3message + step3Financialmessage
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
              // 提取()中的内容
              const parenthesesContent = res.match(/\((.*?)\)/)
              const parenthesesResult = parenthesesContent ? parenthesesContent[1] : null

              // 提取[]中的内容
              const squareBracketsContent = res.match(/\[(.*?)\]/)
              const squareBracketsResult = squareBracketsContent ? squareBracketsContent[1] : null

              if (parenthesesResult && squareBracketsResult) {
                const link = document.createElement('a')
                link.href = `${parenthesesResult}`
                link.download = `证券估值报告${squareBracketsResult}`
                document.body.appendChild(link)
                link.click()
                link.remove()
              }
              setGenerating(false)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  const start = () => {
    handleAnalyzeTemplate()
  }

  const handleAnalyzeTemplate = useCallback(async () => {
    // if (!openKey) {
    //   openTip()
    //   return
    // }
    if (!managerName) {
      messageApi.open({
        key: 'uploading',
        type: 'error',
        content: '请输入股票编码或公司名称',
        duration: 1
      })
      return
    }
    setCurrent(1)
    setStep3message('')
    setTemplateData1('')
    setTemplateData2('')
    handleGenerationStart1()
    setGenerating(true)
    let res = ''
    try {
      await securitiesValuation(
        {
          type: '模板解析',
          template_Type: '页面结构',
          key: key || '',
          financial_statement: uploadedFileList.map(item => ({
            upload_file_id: item.id,
            type: 'document',
            transfer_method: 'local_file'
          })),
          template_file: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: uploadedFile.id
            }
          ]
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              setTemplateData1(res.replace(/^```markdown\s*|```|markdown$/g, ''))
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }, [uploadedFile, managerName])

  const handleGenerationStart1 = useCallback(async () => {
    let res = ''
    try {
      await securitiesValuation(
        {
          type: '模板解析',
          template_Type: '模板结构',
          key: key || '',
          financial_statement: uploadedFileList.map(item => ({
            upload_file_id: item.id,
            type: 'document',
            transfer_method: 'local_file'
          })),
          template_file: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: uploadedFile.id
            }
          ]
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              setTemplateData2(res)
              setGenerating(false)
            }
          },
          onError: () => {},
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }, [uploadedFile])

  return (
    <div className='securities-valuation-container'>
      {contextHolder}
      {/* <GetKey open={open} onClose={setOpen} onChange={setKey} /> */}
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      <Flex className='toolbar' justify='center'>
        <Typography.Text className='title-text'>证券估值报告生成</Typography.Text>
      </Flex>
      <div className='steps-container'>
        <Steps current={current} className='custom-steps'>
          {steps.map(item => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <div className='steps-content'>
          {steps.map((step, index) => (
            <div key={index} className={`step-content ${index === current ? 'active' : 'hidden'}`}>
              {step.content}
            </div>
          ))}
        </div>

        <div className='steps-action'>
          {current > 0 && (
            <Button
              style={{ marginRight: 8 }}
              onClick={() => {
                if (current === 1) {
                  handleAnalyzeTemplate()
                } else if (current === 2) {
                  handleGenerationStart()
                }
              }}
            >
              重新生成
            </Button>
          )}
          {current > 0 && (
            <Button style={{ marginRight: 8 }} onClick={() => setCurrent(current - 1)}>
              上一步
            </Button>
          )}
          {current < steps.length - 1 && current !== 0 && (
            <Button type='primary' onClick={nextStep}>
              下一步
            </Button>
          )}
          {current === steps.length - 1 && (
            <Button type='primary' onClick={() => downloadReport()}>
              下载报告
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

export default DueDiligenceReport
