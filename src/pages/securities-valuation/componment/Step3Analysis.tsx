import { Card, Tabs } from 'antd'
import type { TabsProps } from 'antd'
import { useEffect, useState } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import RemarkBreaks from 'remark-breaks'
import RemarkMath from 'remark-math'
import './Step3Analysis.less'

export const Step3Analysis: React.FC<{
  data: string
  data1: string
  uploadedFileList: { id: string; name: string }[]
}> = ({ data, data1, uploadedFileList }) => {
  const [activeKey, setActiveKey] = useState<string>('')
  const [content, setContent] = useState<string>('')
  const [content1, setContent1] = useState<string>('')

  useEffect(() => {
    setActiveKey('1')
    setContent(data)
  }, [data])

  useEffect(() => {
    setContent1(data1)
  }, [data1])

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '财报分析',
      children: (
        <ReactMarkdown
          className='markdown-body securities-valuation-container'
          remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}
        >
          {content1}
        </ReactMarkdown>
      )
    },
    {
      key: '2',
      label: '网络检索',
      children: (
        <ReactMarkdown
          className='markdown-body securities-valuation-container'
          remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}
        >
          {content}
        </ReactMarkdown>
      )
    }
  ]

  return (
    <Card title='分析结果' className='securities-valuation-card'>
      <Tabs
        defaultActiveKey='1'
        onChange={key => setActiveKey(key)}
        activeKey={activeKey}
        items={items.filter(item => item.key === '2' || uploadedFileList.length > 0)}
      />
    </Card>
  )
}

export default Step3Analysis
