import { Card, Flex } from 'antd'
import StreamTypewriter from '@/component/StreamTypewriter'
import './Step2BasicInfo.less'

export const Step2BasicInfo: React.FC<{
  data: any
  generating: boolean
}> = ({ data, generating }) => {
  return (
    <Card title='模板解析' className='securities-valuation2-card'>
      <Flex vertical style={{ padding: '0 20px 0 20px' }}>
        <StreamTypewriter text={data} key={data} end={!generating} />
      </Flex>
    </Card>
  )
}

export default Step2BasicInfo
