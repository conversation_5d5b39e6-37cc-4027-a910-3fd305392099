import { useState, useEffect, useCallback, useRef } from "react";
import {
  Upload,
  Button,
  Typography,
  Card,
  Spin,
  message,
  Form,
  Flex,
  Select,
} from "antd";
import {
  CheckCircleFilled,
  InboxOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import { uploadFile } from "@/api/template";
import { ChartGeneration } from "@/api/chartGeneration";
import { NoData } from "@/component/NoData";
import { extractContent } from "@/utils/common";
import ReactECharts from "echarts-for-react";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import "./index.less";
import TextArea from "antd/es/input/TextArea";
import ReactMarkdown from "react-markdown";
import ThinkBlock from "@/component/think";

const CONTRACT_TOOLS_XHLH_TOKEN =
  import.meta.env["VITE_CONTRACT_TOOLS_XHLH_TOKEN"] || "";
export const ExcelGeneration = () => {
  const [form] = Form.useForm();
  const [messageApi, contextHolder] = message.useMessage();
  const [uploadedFiles, setUploadedFiles] = useState<
    { id: string; name: string }[]
  >([]);
  const [startSending, setStartSending] = useState<boolean>(false);
  const [generating, setGenerating] = useState<boolean>(false);
  const [markdownTable, setMarkdownTable] = useState("");
  const [messagesEnd, setMessagesEnd] = useState<boolean>(false);
  const [descInfo, setDescInfo] = useState<string>("");
  const scrollRef = useRef<HTMLDivElement>(null);
  const [chartCache, setChartCache] = useState<JSX.Element | null>(null);
  const hasChartRendered = useRef(false);
  const [messages] = useState<string>("");

  useEffect(() => {
    if (uploadedFiles.length > 0) {
      messageApi.open({
        key: "uploading",
        type: "success",
        content: "文件上传成功",
        duration: 1,
      });
    }
  }, [uploadedFiles, messageApi]);

  // 上传文件
  const beforeUpload = (file: File) => {
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf(".") + 1)
      ?.toLowerCase();
    if (["xlsx"].includes(originalFileExt)) {
      messageApi.open({
        key: "uploading",
        type: "loading",
        content: "文件上传中",
      });

      uploadFile(file, CONTRACT_TOOLS_XHLH_TOKEN).then(async (response) => {
        if (response.id) {
          setUploadedFiles((prevFiles) => [...prevFiles, response]);
          messageApi.open({
            key: "uploading",
            type: "success",
            content: "文件上传成功",
            duration: 1,
          });
        } else {
          messageApi.open({
            key: "uploading",
            type: "error",
            content: "文件上传失败",
            duration: 1,
          });
        }
      });
    } else {
      messageApi.error(
        "目前仅支持.xlsx类型的文件，请您将文件转成这些格式后再次进行上传"
      );
    }
    return false;
  };

  //删除文件
  const handleDelete = (fileId: string) => {
    setUploadedFiles((prevFiles) =>
      prevFiles.filter((file) => file.id !== fileId)
    );
  };

  // 下拉框数据
  const options = [
    { value: "柱状图", label: "柱状图" },
    { value: "饼状图", label: "饼状图" },
    { value: "折线图", label: "折线图" },
  ];

  const handleGenerationStart = () => {
    if (uploadedFiles.length === 0) {
      messageApi.error("请上传至少一个文件");
      return;
    }
    setStartSending(true);
    setChartCache(null);
    hasChartRendered.current = false;
    const fileIds = uploadedFiles.map((file) => file.id);
    handleGeneration(fileIds);
    setMarkdownTable("");
  };
  // 调用dify返回
  const handleGeneration = useCallback(
    async (fileIds: string[]) => {
      setMessagesEnd(false);
      setGenerating(true);
      let accumulatedMessages = "";
      const values = form.getFieldsValue();
      try {
        await ChartGeneration(
          {
            query: descInfo,
            chartType: values.chartType,
            files: fileIds.map((x) => ({
              type: "document",
              transfer_method: "local_file",
              upload_file_id: x,
            })),
          },
          {
            onMessage: (text: string | null, finished: boolean) => {
              if (text) {
                setGenerating(false);
                accumulatedMessages += text || "";
                //处理markdown表格 跟think处理
                const cleanedData = accumulatedMessages.replace(
                  /^```markdown\s*|```$/g,
                  ""
                );
                const processedData = cleanedData.replace(
                  /\n\n<think>\n\n/g,
                  "\n\n<think>\n\n\n"
                );
                setMarkdownTable(processedData);
              }
              if (finished) {
                setMessagesEnd(true);
                const errorStr = extractContent(accumulatedMessages, "error");
                if (errorStr) {
                  messageApi.error(errorStr);
                  return;
                }
              }
            },
            onError: () => {
              setGenerating(false);
              setMessagesEnd(true);
            },
            onFinish: () => {
              setGenerating(false);
              setMessagesEnd(true);
            },
          }
        );
      } catch (err) {
        setGenerating(false);
        setMessagesEnd(true);
      }
    },
    [descInfo]
  );
  useEffect(() => {
    scrollRef.current?.scrollTo({
      top: scrollRef.current.scrollHeight,
      behavior: "smooth",
    });
  }, [markdownTable]);
  return (
    <>
      {contextHolder}
      <Spin tip="处理中..." spinning={generating} fullscreen size="large" />
      <div className="chart-generation-container">
        {/* 左侧面板 */}
        <div className="contract-left-panel">
          <Typography.Text className="title-text">
            Excel图表生成
          </Typography.Text>
          <Form
            layout="vertical"
            initialValues={{ chartType: "折线图" }}
            form={form}
          >
            <Form.Item
              label="上传文件"
              name="uploadedFiles"
              rules={[{ required: true, message: "请上传文件" }]}
            >
              <Upload.Dragger
                multiple
                showUploadList={false}
                beforeUpload={beforeUpload}
              >
                <div className="ant-upload-drag-icon">
                  {uploadedFiles.length > 0 ? (
                    <CheckCircleFilled />
                  ) : (
                    <InboxOutlined />
                  )}
                </div>
                <div className="ant-upload-hint">
                  <span>拖拽文件到此处上传</span>
                  <br />
                  <span style={{ fontSize: "12px", color: "#999" }}>
                    或点击选择文件(仅支持.xlsx)
                  </span>
                </div>
              </Upload.Dragger>

              {uploadedFiles.length > 0 && (
                <div className="file-list-contract">
                  {uploadedFiles.map((file) => (
                    <div key={file.id} className="file-item">
                      <span>{file.name}</span>
                      <DeleteOutlined
                        onClick={() => handleDelete(file.id)}
                        style={{ cursor: "pointer" }}
                      />
                    </div>
                  ))}
                </div>
              )}
            </Form.Item>
            <Form.Item
              label="图表类型"
              name="chartType"
              rules={[{ required: true, message: "请选择图表类型" }]}
            >
              <Select
                placeholder="请选择图表类型"
                style={{ width: "100%" }}
                options={options}
              />
            </Form.Item>
            <Form.Item
              label="需求描述"
              name="descInfo"
              rules={[{ required: true, message: "请输入需求描述" }]}
            >
              <TextArea
                showCount
                maxLength={200}
                value={descInfo}
                placeholder="请输入需求描述"
                style={{ height: 120, resize: "none" }}
                onChange={(e) => setDescInfo(e.target.value.trim())}
              />
            </Form.Item>
            <Form.Item>
              <Button
                type="primary"
                disabled={uploadedFiles.length === 0 || generating || !descInfo}
                onClick={handleGenerationStart}
                style={{ width: "100%" }}
              >
                点击生成
              </Button>
            </Form.Item>
          </Form>
        </div>
        {/* 右侧面板 */}
        <div className="contract-right-panel">
          {startSending && (
            <Flex
              ref={scrollRef}
              justify="center"
              style={{
                marginTop: 15,
                overflow: "auto",
                width: "100%",
              }}
            >
              {markdownTable ? (
                <Card style={{ width: "100%", overflow: "auto" }}>
                  <ReactMarkdown
                    rehypePlugins={[rehypeRaw]}
                    remarkPlugins={[remarkGfm]}
                    components={{
                      think: (props: any) => {
                        return (
                          <ThinkBlock finished={false}>
                            {props.children}
                          </ThinkBlock>
                        );
                      },
                      code({ className, children, ...props }) {
                        const match = /language-(\w+)/.exec(className || "");
                        if (match && match[1]) {
                          const cleaned = `${children}`.replace(
                            /^option\s*=\s*/,
                            ""
                          );
                          if (hasChartRendered.current && chartCache) {
                            // ✅ 已经渲染完过一次了，直接复用
                            return chartCache;
                          }
                          try {
                            const optionObj = new Function(
                              "return " + cleaned
                            )();
                            if (optionObj !== undefined && optionObj !== null) {
                              optionObj.animation = false;
                              const chartElement = (
                                <ReactECharts
                                  option={optionObj}
                                  onChartReady={(chartInstance) => {
                                    hasChartRendered.current = true;
                                    setChartCache(chartElement); // 缓存这个组件
                                  }}
                                  style={{
                                    height: 400,
                                    width: "100%",
                                    margin: "24px 0",
                                  }}
                                />
                              );
                              return chartElement;
                            }
                          } catch (e) {
                            return (
                              <pre style={{ color: "red" }}>图表生成中...</pre>
                            );
                          }
                        }
                        return (
                          <pre>
                            <Card style={{ width: "100%", overflow: "auto" }}>
                              <code className={className} {...props}>
                                {children}
                              </code>
                            </Card>
                          </pre>
                        );
                      },
                    }}
                  >
                    {markdownTable}
                  </ReactMarkdown>
                </Card>
              ) : (
                <Flex justify="justify" align="center" vertical>
                  <Typography.Text>
                    {messages || "正在提取文件信息，请不要关闭或刷新页面"}
                  </Typography.Text>
                </Flex>
              )}
            </Flex>
          )}
          {!startSending && <NoData description=" " text="暂无内容" />}
        </div>
      </div>
    </>
  );
};

export default ExcelGeneration;
