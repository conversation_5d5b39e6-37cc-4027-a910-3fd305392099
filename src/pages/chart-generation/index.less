
.chart-generation-container {
  display: flex;
  gap: 24px;
  // max-width: 1600px;
  margin: 0 auto;
  height: 100vh;
  .ant-card{
    overflow: initial !important;
    border: none !important;
  }
  .thinking-card {
    margin: 20px auto;
    background-color: rgb(249 250 251 / var(1, 1));
    .ant-card-body {
      padding: 16px;
    }
    p {
      margin: 0;
    }
    ul {
      margin: 0;
      padding: 6px 10px;
    }
    li {
      list-style: decimal;
    }
    ol ul li {
      list-style: disc;
    }
  }
  table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 0.9em;
    margin-top: 20px;
    font-family: system-ui, -apple-system, sans-serif;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
    table-layout: auto; /* 确保表格列宽度根据内容自动调整 */

    thead tr {
      background-color: #4f46e5;
      color: #ffffff;
      text-align: left;
    }

    th {
      padding: 12px 15px;
      white-space: nowrap; /* 防止表头文字换行 */
      width: auto; /* 根据内容自动调整宽度 */
    }

    td {
      padding: 12px 15px;
      word-wrap: break-word; /* 允许单元格内的长文本换行 */
    }

    tbody tr {
      border-bottom: 1px solid #dddddd;
    }

    tbody tr:nth-of-type(even) {
      background-color: #f8f8f8;
    }

    tbody tr:last-of-type {
      border-bottom: 2px solid #4f46e5;
    }

    tbody tr:hover {
      background-color: #f5f4ff;
      transition: all 0.2s ease;
    }
  }
  h2 {
    margin-top:20px;
    margin-bottom: 10px;
  }
  h3 {
    margin: 10px 0px;
  }
  ul {
    padding-left:26px;
    li {
      padding: 2px 0px;
    }
  }
  // 左侧面板样式
  .contract-left-panel {
    width: 25%;
    min-width: 25%;
    background: #ffffff;
    padding: 24px;
    
    .title-text {
      font-size: 24px;
      font-weight: 600;
      text-align: center;
      color: #1a1a1a;
      padding-bottom: 8px;
      display: block;
    }
  }

  // 右侧面板样式
  .contract-right-panel {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin: 32px 32px 32px 8px;
    position: relative;
    
    .contract-action-buttons{
      display: flex;
      gap: 12px;
      justify-content: center;
      
    }
    
    .contract-output-section {
      flex: 1;
      background: #fff;
      border-radius: 12px;
      // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

      .ant-card-body {
        // height: auto;
        padding: 24px;
        display: flex;
        flex-direction: column;
      }
    }
    .contract-markdown-body table {
      border-collapse: collapse;
      width: 100%;
    }
    
    .contract-markdown-body th,
    .contract-markdown-body td {
      border: 1px solid #ccc; /* 设置边框颜色 */
      padding: 8px; /* 设置单元格内边距 */
      text-align: left; /* 设置文本对齐方式 */
    }
    
    .contract-markdown-body th {
      background-color: #f2f2f2; /* 设置表头背景色 */
    }
    .contract-markdown-body tr td:nth-child(1){
      min-width: 90px;
    }
  }
}

.contract-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
  display: block;
}

// 上传区域样式
.ant-upload-drag {
  padding: 24px;
  border: 2px dashed #e8e8e8;
  border-radius: 12px;
  background: #fafafa;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
    background: #f0f7ff;
  }

  .ant-upload-drag-icon {
    margin-bottom: 16px;
    font-size: 48px;
    color: #1890ff;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }
}

// 文件列表样式
.file-list-contract {
  margin-top: 16px;
  max-height: 200px;
  overflow-y: auto;
  padding-right: 4px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f5f5;
    border-radius: 3px;
  }

  .file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 8px;
    transition: all 0.3s ease;

    &:hover {
      background: #f0f2f5;
    }

    span {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 12px;
    }
  }
}

// 输入框样式
.ant-input {
  border-radius: 8px;
  
  &:hover, &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }
}


