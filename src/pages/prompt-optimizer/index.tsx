import {useState} from "react";

import {<PERSON><PERSON>, <PERSON>, Collapse, Divider, Flex, Input, message, Modal, Select, Space, Switch, Typography} from "antd";
import {CopyOutlined, InfoCircleOutlined, SettingFilled} from "@ant-design/icons";

import {OptimizationPromptTemplate, PromptsIteration} from "@/pages/prompt-optimizer/types.ts";
import {DEFAULT_OPTIMIZATION_PROMPT_TEMPLATES} from "@/pages/prompt-optimizer/data.ts";
import PromptOutput from "@/pages/prompt-optimizer/components/prompt-output.tsx";

import "./index.less"
import {promptOptimizer} from "@/api/prompt-optimizer.ts";


const PromptOptimizer = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const [warningModelOpened, setWarningModelOpened] = useState(false);
  const [promptEditorOpened, setPromptEditorOpened] = useState(false);

  // 优化流程相关状态
  const [prompts, setPrompts] = useState<PromptsIteration>({original: '', new: '优化后的提示词将在这里展示'});
  const [optimizationPrompts, setOptimizationPrompts] = useState<OptimizationPromptTemplate[]>(DEFAULT_OPTIMIZATION_PROMPT_TEMPLATES);
  const [selectedPrompt, setSelectedPrompt] = useState<number | null>(null);
  const [optimizationGenerating, setOptimizationGenerating] = useState<boolean>(false);

  // 测试流程相关状态
  const [testInput, setTestInput] = useState<string>("");
  const [comparisonOpened, setComparisonOpened] = useState<boolean>(true);
  const [testGenerating, setTestGenerating] = useState<[boolean, boolean]>([false, false]);
  const [testResultOriginal, setTestResultOriginal] = useState<string>('原始结果将在这里输出');
  const [testResultNew, setTestResultNew] = useState<string>('优化后的结果将在这里输出');

  const handleOptimizationStart = () => {
    if (!prompts.original) {
      messageApi.warning("原始提示词不能为空")
      return;
    }
    if (selectedPrompt === null || selectedPrompt < 0) {
      messageApi.warning("必须选择一个优化提示词")
      return;
    }
    const optimizePromptText = optimizationPrompts[selectedPrompt].value;

    setOptimizationGenerating(true);
    setPrompts(prev => {
      return {
        ...prev,
        new: ''
      }
    })
    promptOptimizer(prompts.original, {
        phase: 0,
        optimize_prompt: optimizePromptText
      },
      {
        onMessage: (text, finished) => {
          if (text) {
            setPrompts(prev => {
              return {
                ...prev,
                new: prev.new + text
              }
            })
          }
          if (finished) {
            setOptimizationGenerating(false)
          }
        },
        onError: () => {
          setOptimizationGenerating(false)
        },
        onFinish: () => {
        }
      }
    );
  }

  const handleComparisonStart = () => {
    if (!prompts.new || !prompts.original) {
      messageApi.warning("请先进行提示词优化，再执行测试")
      return;
    }
    if (!testInput) {
      messageApi.warning("请输入测试内容")
      return;
    }
    setTestResultNew('')
    setTestResultOriginal('')
    setTestGenerating([false, false])
    // 原始提示词
    promptOptimizer(testInput, {
        phase: 1,
        test_prompt: prompts.original,
      },
      {
        onMessage: (text, finished) => {
          if (text) {
            setTestResultOriginal(prev => prev + text)
          }
          if (finished) {
            setTestGenerating(prev => {
              const next: [boolean, boolean] = [...prev]
              next[0] = false
              return next
            })
          }
        },
        onError: () => {
          setTestGenerating(prev => {
            const next: [boolean, boolean] = [...prev]
            next[0] = false
            return next
          })
        },
        onFinish: () => {
        }
      }
    );
    // 优化提示词
    promptOptimizer(testInput, {
        phase: 1,
        test_prompt: prompts.new,
      },
      {
        onMessage: (text, finished) => {
          if (text) {
            setTestResultNew(prev => prev + text)
          }
          if (finished) {
            setTestGenerating(prev => {
              const next: [boolean, boolean] = [...prev]
              next[1] = false
              return next
            })
          }
        },
        onError: () => {
          setTestGenerating(prev => {
            const next: [boolean, boolean] = [...prev]
            next[1] = false
            return next
          })
        },
        onFinish: () => {
        }
      }
    );
  }

  return <Flex className='container' vertical>
    {contextHolder}

    <Flex className='toolbar' justify='space-between'>
      <Typography.Text className='title-text'>提示词优化器</Typography.Text>
      <Button icon={<InfoCircleOutlined/>} size="large" type="primary" onClick={() => setWarningModelOpened(true)}>
        使用说明
      </Button>
      <Modal closable={false} title="提示词优化器" footer={null} maskClosable open={warningModelOpened}
             onCancel={() => setWarningModelOpened(false)}
      >
        <Typography.Paragraph>
          <Typography.Text code>
            工具版本: 1.0.0
          </Typography.Text>
        </Typography.Paragraph>
        <Typography.Paragraph>1.
          在界面左侧输入你的原始提示词，然后选择优化提示词（优化提示词可根据实际情况进行调整），选择完毕后，即可开始优化；</Typography.Paragraph>
        <Typography.Paragraph>2. 等待优化完成后，在界面右侧可以输入你的测试数据进行测试。</Typography.Paragraph>
        <Typography.Paragraph strong>注意：当前版本不支持自定义选择模型 </Typography.Paragraph>
        <Typography.Text type="secondary">点击弹窗外任意区域返回</Typography.Text>
      </Modal>
    </Flex>

    <Flex className="operation">
      <Flex vertical justify="stretch" className="panel left">
        <Typography.Title level={5}>
          原始提示词
        </Typography.Title>
        <Flex vertical gap={"middle"} justify="stretch">
          <Input.TextArea value={prompts.original}
                          onChange={(e) => {
                            setPrompts((prev) => {
                              return {
                                ...prev,
                                original: e.target.value,
                              }
                            })
                          }}
                          placeholder="请输入需要优化的提示词"
                          autoSize={{minRows: 6, maxRows: 6}}/>
          <Space>
            <Select placeholder="请选择提示词优化模型（暂不支持）" disabled></Select>
            <Select popupMatchSelectWidth={false} placeholder="请选择优化提示词"
                    showSearch
                    allowClear
                    options={optimizationPrompts}
                    optionRender={(option) => {
                      return <Card title={option.data.label} size="small">{option.data.description}</Card>
                    }}
                    dropdownRender={(originalNode) => {
                      return <Flex vertical gap="middle" style={{
                        padding: "0 0.5rem",
                      }}>
                        {originalNode}
                        <Button size="large" type="primary" icon={<SettingFilled/>}
                                onClick={() => setPromptEditorOpened(true)}>配置提示词
                        </Button>
                      </Flex>
                    }}
                    onSearch={(v) => {
                      return optimizationPrompts.filter(item => item.label.indexOf(v) == -1)
                    }}
                    onChange={(_, option) => setSelectedPrompt((option as OptimizationPromptTemplate)?.key)}
            ></Select>
          </Space>

          <Button disabled={optimizationGenerating} type="primary" onClick={handleOptimizationStart}>开始优化</Button>
        </Flex>
        <Divider/>
        <PromptOutput title="优化后的提示词"
                      content={prompts.new}
                      extra={<Button onClick={() => {
                        window.navigator.clipboard.writeText(prompts.new).then(() => {
                          messageApi.success("复制成功")
                        })
                      }} icon={<CopyOutlined/>}></Button>}/>

      </Flex>
      <Flex vertical style={{height: "100%"}} className="panel right">
        <Typography.Title level={5}>
          测试输入内容
        </Typography.Title>
        <Flex vertical gap={"middle"} justify="stretch">
          <Input.TextArea placeholder="请输入测试的内容"
                          value={testInput}
                          autoSize={{minRows: 6, maxRows: 6}}
                          onChange={(e) => setTestInput(e.target.value)}
          />
          <Space>
            <Select placeholder="请选择模型（暂不支持）" disabled></Select>
            <Switch checked={comparisonOpened}
                    checkedChildren="对比模式已开启"
                    unCheckedChildren="对比模式已关闭"
                    onChange={() => {
                      setComparisonOpened(prev => !prev)
                    }}/>
          </Space>
          <Button type="primary" onClick={handleComparisonStart}
                  disabled={testGenerating[0] || testGenerating[1]}>开始对比</Button>
        </Flex>
        <Divider/>
        <Flex>
          {
            comparisonOpened && <PromptOutput title="原始效果" content={testResultOriginal}/>
          }
          <PromptOutput title="优化效果" content={testResultNew}/>
        </Flex>
      </Flex>
    </Flex>
    <Modal
      width={"80%"}
      footer={null}
      title="优化提示词（编辑后立即生效）"
      open={promptEditorOpened}
      maskClosable={false}
      onCancel={() => setPromptEditorOpened(false)}
      onOk={() => setPromptEditorOpened(false)}
    >
      <Collapse accordion items={optimizationPrompts.map((item, index) => {
        return {
          key: index,
          label: item.label,
          children: <Input.TextArea
            value={optimizationPrompts[index].value}
            autoSize={{
              minRows: 12,
              maxRows: 12
            }}
            onChange={(e) => setOptimizationPrompts((prev) => {
              const next = [...prev]
              next[index].value = e.target.value
              return next
            })}/>
        }
      })}/>

    </Modal>
  </Flex>
}

export default PromptOptimizer
