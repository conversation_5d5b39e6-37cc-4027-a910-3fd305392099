import {Flex, Input, Typography} from "antd";
import {ReactNode} from "react";

const PromptOutput = ({title, content, extra}: { title: string, content: string, extra?: ReactNode }) => {
  return <Flex vertical className="output" gap="middle">
    <Flex justify="space-between">
      <Typography.Title level={5}>{title}</Typography.Title>
      {extra}
    </Flex>

    <Input.TextArea variant="filled" value={content} autoSize={{
      minRows: 25,
      maxRows: 25,
    }}></Input.TextArea>
  </Flex>
}

export default PromptOutput;
