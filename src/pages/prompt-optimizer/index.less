.container {
  height: 100%;

  .toolbar {
    background: linear-gradient(180deg, rgba(189, 225, 255, 0.4) 0%, rgba(224, 242, 255, 0) 100%);
    border-radius: 0.5rem 0.5rem 0 0;
    padding: 12px 24px;
    height: 70px;

    .title-text {
      color: transparent;
      background: linear-gradient(116deg, #1888ff 16%, #2f54eb 88%);
      background-clip: text;
      -webkit-background-clip: text;
      user-select: none;
      font-size: 30px;
      font-weight: bold;
    }
  }

  .operation {
    height: 100%;

    .panel {
      height: 100%;
      width: 50%;
      padding: 1em 2em;
    }
  }

  .output {
    flex: 1;
    padding: 0.5rem;
  }
}
