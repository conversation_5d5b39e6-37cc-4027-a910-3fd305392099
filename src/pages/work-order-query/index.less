:root {
  --primary-color: #3b82f6;
  --text-color: #333;
  --background-color: #f8fafc;
  --card-background: #ffffff;
  --border-color: #e2e8f0;
  --amount-color: #dc2626;
  --muted-color: #6b7280;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
}

.work-order-query-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;

  .search-container {
    display: flex;
    margin-bottom: 1.5rem;
    gap: 0.75rem;
  }

  .search-container input {
    flex-grow: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
  }

  .search-container input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .search-container button {
    padding: 0.75rem 1.25rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease;
    font-size: 14px;
  }

  .search-container button:hover {
    background-color: #2563eb;
  }

  .view-toggle {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 1rem;
  }

  .view-toggle button {
    background: none;
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    height: 37px;
  }

  .view-toggle button:first-child {
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
  }

  .view-toggle button:last-child {
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
  }

  .view-toggle button.active {
    background-color: var(--primary-color);
    color: white;
  }

  .contract-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
    position: relative;
  }

  .contract-list.list-view {
    grid-template-columns: 1fr;
  }

  .contract-item {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.25rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
  }

  .contract-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .contract-item.list-view {
    display: grid;
    align-items: center;
  }

  .contract-item h3 {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
    color: var(--primary-color);
  }

  .contract-item p {
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
  }

  .contract-amount {
    color: var(--amount-color);
    font-weight: bold;
  }

  .total-count {
    margin-bottom: 1rem;
    color: #6b7280;
    font-size: 0.95rem;
  }

  @media (max-width: 768px) {
    .contract-list {
      grid-template-columns: 1fr;
    }

    .contract-item.list-view {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }

    .view-toggle {
      justify-content: center;
    }
  }

  .field-selection {
    position: relative;
    display: inline-block;
  }

  .field-selection button {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 0.9rem;
    cursor: pointer;
  }

  .dropdown-content {
    display: none;
    position: absolute;
    background-color: #f9f9f9;
    min-width: 190px;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
  }

  .dropdown-content label {
    display: block;
  }

  .dropdown-content input {
    margin-right: 8px;
  }

  .controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .field-selection {
    position: relative;
    display: inline-block;
  }

  .field-selection button {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 0.9rem;
    cursor: pointer;
    height: 37px;
    background-color: rgba(240, 240, 240);
  }

  .field-selection.show .dropdown-content {
    display: block;
  }

  .view-toggle button {
    background: none;
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
  }

  .view-toggle button:first-child {
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
  }

  .view-toggle button:last-child {
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
  }

  .view-toggle button.active {
    background-color: var(--primary-color);
    color: white;
  }

  .show {
    display: block !important;
  }

  h1 {
    text-align: center;
    color: #0966d9;
    padding: 0 0 35px 0;
  }

  @media (max-width: 480px) {
    .contract-item.list-view {
      grid-template-columns: 1fr;
    }

    .contract-item.list-view > div {
      margin-bottom: 0.5rem;
    }

    .contract-item.list-view > div:last-child {
      margin-bottom: 0;
    }
  }
  .list-item-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    padding: 15px;
  }

  .list-item-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
  }

  .field-label {
    color: #666;
    margin-right: 10px;
  }

  .field-value {
    font-weight: 500;
  }
  .dropdown-content {
    position: absolute;
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    padding: 8px;
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;

    label {
      display: block;
      padding: 8px;
      cursor: pointer;

      &:hover {
        background-color: #f5f5f5;
      }

      input {
        margin-right: 8px;
      }
    }
  }
  .list-item-container {
    display: grid;
    gap: 10px;
    padding: 15px;
    width: 100%;
  }

  .list-item-field {
    display: flex;
    flex-direction: column;
    padding: 5px 10px;
    border-right: 1px solid #eee;

    &:last-child {
      border-right: none;
    }
  }

  .field-label {
    color: #666;
    font-size: 12px;
    margin-bottom: 4px;
  }

  .field-value {
    font-weight: 500;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .thinking-card {
    margin: 20px auto;
    background-color: rgb(249 250 251 / var(1, 1));
    .ant-card-body {
      padding: 16px;
    }
    p {
      margin: 0;
    }
    ul {
      margin: 0;
      padding: 6px 10px;
    }
    li {
      list-style: decimal;
    }
    ol ul li {
      list-style: disc;
    }
  }
}
