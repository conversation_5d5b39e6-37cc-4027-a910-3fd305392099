.document-summary {
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, rgba(189, 225, 255, 0.4) 0%, rgba(224, 242, 255, 0) 100%);
  .bg-transparent {
    background: transparent;
  }
}
.document-summary-toolbar {
  border-radius: 0.5rem 0.5rem 0 0;
  padding: 12px 24px;
  height: 72px;
  margin-bottom: 15px;

  .title-text {
    color: transparent;
    background: linear-gradient(116deg, #1888ff 16%, #2f54eb 88%);
    background-clip: text;
    -webkit-background-clip: text;
    user-select: none;
    font-size: 22px;
    font-weight: 600;
  }
}
.document-summary-content {
  padding: 24px;
  width: 80vw;
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px;
}
.document-summary-container {
  height: calc(100vh - 175px);
  overflow: auto;
  padding: 24px;
  border-radius: 8px;
  background-color: #fff;
}

.hidden-contain {
  contain: strict;
  content-visibility: hidden;
  width: 0;
  height: 0;
}