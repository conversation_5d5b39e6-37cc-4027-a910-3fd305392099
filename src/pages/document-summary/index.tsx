import React, { useCallback, useState } from 'react'
import { Input, Typography, Layout, Flex, List, Space, Spin } from 'antd'
import { FileWordOutlined } from '@ant-design/icons'
import StreamTypewriter from '@/component/StreamTypewriter'
import { documentSummary } from '@/api/documentSummary'
import { NoData } from '@/component/NoData'
import { getFileType } from '@/utils/file'
import './index.less'

const { Content, Header } = Layout

export const DocumentSummary: React.FC = () => {
  const [collapse, setCollapse] = useState<boolean>(false)
  const [generating, setGenerating] = useState<boolean>(false)
  const [messagesEnd, setMessagesEnd] = useState<boolean>(false)
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [messages, setMessages] = useState<string>('')
  const [data, setData] = useState<any[]>([])

  const handleGeneration = useCallback(
    async (query: string) => {
      if (!query) return
      setGenerating(true)
      setMessages('')
      setCollapse(false)
      setMessagesEnd(false)
      setSearchQuery(query)
      setData([])

      try {
        await documentSummary(searchQuery, {
          onMessage: (text, finished) => {
            setGenerating(false)
            if (text) {
              setMessages(prev => prev + text)
            }
            if (finished) {
              setMessagesEnd(true)
            }
          },
          onError: () => {
            setGenerating(false)
            setMessagesEnd(true)
          },
          onFinish: (data: any) => {
            setData(data.metadata?.retriever_resources || [])
          }
        })
      } catch (err) {
        setGenerating(false)
        setMessagesEnd(true)
      }
    },
    [searchQuery]
  )

  const downloadFile = useCallback((text: string, filename: string) => {
    const fileType = getFileType(filename)
    const blob = new Blob([text], { type: fileType })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }, [])

  const handleSearch = () => {
    handleGeneration(searchQuery)
  }
  return (
    <div className='document-summary'>
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      <Layout className='bg-transparent'>
        <Header className='bg-transparent'>
          <Flex className='document-summary-toolbar' justify='center'>
            <Typography.Text className='title-text'>过程性文档归纳总结</Typography.Text>
          </Flex>
        </Header>
        <Content className='document-summary-content'>
          <Flex style={{ marginBottom: '15px' }}>
            <Input.Search
              size='large'
              style={{ backgroundColor: '#fff', borderRadius: '8px' }}
              placeholder='请输入查询内容，例如:2014年1月16日调研内容?'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              onSearch={handleSearch}
              enterButton='查询'
            />
          </Flex>
          <Flex vertical className='document-summary-container scroll-container'>
            {messages && (
              <Flex vertical>
                <div className={collapse ? 'hidden-contain' : ''}>
                  <StreamTypewriter text={messages} end={messagesEnd} />
                </div>
              </Flex>
            )}

            {!messages && <NoData description='未找到匹配的文档信息' />}

            {data.length > 0 && (
              <List
                size='small'
                style={{ width: '650px' }}
                header={<div>引用</div>}
                bordered
                dataSource={data}
                renderItem={item => (
                  <List.Item>
                    <Space>
                      <FileWordOutlined style={{ color: '#1890ff' }} />
                      <Typography.Link
                        style={{ cursor: 'pointer' }}
                        onClick={() => downloadFile(item.content, item.document_name)}
                      >
                        {item.document_name}
                      </Typography.Link>
                    </Space>
                  </List.Item>
                )}
              />
            )}
          </Flex>
        </Content>
      </Layout>
    </div>
  )
}

export default DocumentSummary