import React, { useState, useCallback, useMemo } from 'react'
import {
  Card,
  Input,
  Select,
  Button,
  Typography,
  Spin,
  Row,
  Col,
  Flex,
  message,
  Pagination,
  Cascader,
  Modal,
  Upload,
  Space,
  Tag,
} from 'antd'
import { policyComplianceVerifier } from '@/api/policyComplianceVerifier'
// @ts-expect-error: 忽略less类型声明
import './index.less'
import {
  areaList, // 完整嵌套数据
} from '@vant/area-data'
import {
  CheckCircleTwoTone,
  PlusCircleTwoTone,
  InboxOutlined,
  CheckCircleFilled,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseOutlined,
} from '@ant-design/icons'
import { uploadFile } from '@/api/template'
import StreamTypewriter from '@/component/StreamTypewriter'
import VerificationResult from './component/VerificationResult'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import RemarkBreaks from 'remark-breaks'
import RemarkMath from 'remark-math'
import PolicyInterpretation from './component/PolicyInterpretation'
import policiesData from './data/policies.json'
const token = import.meta.env['VITE_POLICY_COMPLIANCE_VERIFIER'] || ''
const { Title, Text, Paragraph } = Typography

const industries = [
  { label: '农、林、牧、渔业', value: '农、林、牧、渔业' },
  { label: '采矿业', value: '采矿业' },
  { label: '制造业', value: '制造业' },
  {
    label: '电力、热力、燃气及水生产和供应业',
    value: '电力、热力、燃气及水生产和供应业',
  },
  { label: '建筑业', value: '建筑业' },
  { label: '交通运输、仓储和邮政业', value: '交通运输、仓储和邮政业' },
  {
    label: '信息传输、软件和信息技术服务业',
    value: '信息传输、软件和信息技术服务业',
  },
  { label: '批发和零售业', value: '批发和零售业' },
  { label: '住宿和餐饮业', value: '住宿和餐饮业' },
  { label: '金融业', value: '金融业' },
  { label: '房地产业', value: '房地产业' },
  { label: '租赁和商务服务业', value: '租赁和商务服务业' },
  { label: '科学研究和技术服务业', value: '科学研究和技术服务业' },
  { label: '水利、环境和公共设施管理业', value: '水利、环境和公共设施管理业' },
  { label: '居民服务、修理和其他服务业', value: '居民服务、修理和其他服务业' },
  { label: '教育', value: '教育' },
  { label: '卫生和社会工作', value: '卫生和社会工作' },
  { label: '文化、体育和娱乐业', value: '文化、体育和娱乐业' },
  {
    label: '公共管理、社会保障和社会组织',
    value: '公共管理、社会保障和社会组织',
  },
  { label: '国际组织', value: '国际组织' },
]
const policyType = [
  { label: '证券合规监管', value: '证券合规监管' },
  { label: '期货合规监管', value: '期货合规监管' },
  { label: '信托合规监管', value: '信托合规监管' },
  { label: '其他监管方向', value: '其他监管方向' },
  { label: '非监管类政策', value: '非监管类政策' },
]

const mockPolicies = [
  {
    title:
      '国家金融监督管理总局关于调整保险资金权益类资产监管比例有关事项的通知1',
    description:
      '保险资金运用监管政策安全性、流动性、收益性相结合的原则，合理调整保险资金权益类资产监管比例，完善风险防控，强化报告制度。',
    source: '国家金融监督管理总局',
    publishDate: '2025年1月1日',
    content:
      '1.政策标题=中华人民共和国期货和衍生品法 \n2.主管部门=国务院期货监督管理机构 \n3.发布日期=2022年4月20日 \n4.发文年份=2022年 \n5.适用行业=金融业 \n6.产业领域=金融 \n7.行政区划=全国 \n8.区划层级=国家级 \n9.政策背景=为了规范期货交易和衍生品交易行为，保障各方合法权益，维护市场秩序和社会公共利益，促进期货市场和衍生品市场服务国民经济，防范化解金融风险，维护国家经济安全。 \n10.政策目标=规范期货交易和衍生品交易活动，保障市场公平、公正、透明，防范系统性风险，维护交易者合法权益，促进期货市场和衍生品市场健康发展。 \n11.主要内容=本法共分为十三章，包括总则、期货交易和衍生品交易、期货结算与交割、期货交易者、期货经营机构、期货交易场所、期货结算机构、期货服务机构、期货业协会、监督管理、跨境交易与监管协作、法律责任和附则。主要内容包括：期货交易和衍生品交易的定义和范围；期货交易场所和结算机构的设立和管理；期货经营机构的设立和业务范围；期货交易者的权益保护；期货市场的监督管理和法律责任等。',
    tags: ['保险', '监管'],
    url: '#',
  },
  {
    title:
      '国家金融监督管理总局关于调整保险资金权益类资产监管比例有关事项的通知2',
    description:
      '保险资金运用监管政策安全性、流动性、收益性相结合的原则，合理调整保险资金权益类资产监管比例，完善风险防控，强化报告制度。',
    source: '国家金融监督管理总局',
    publishDate: '2025年1月1日',
    content:
      '1.政策标题=中华人民共和国证券法 \n2.主管部门=国务院证券监督管理机构 \n3.发布日期=2019年12月28日 \n4.发文年份=2019年 \n5.适用行业=金融业 \n6.产业领域=金融 \n7.行政区划=全国 \n8.区划层级=国家级 \n9.政策背景=为了规范证券发行和交易行为，保护投资者的合法权益，维护社会经济秩序和社会公共利益，促进社会主义市场经济的发展。 \n10.政策目标=规范证券市场，保护投资者权益，维护市场秩序，促进证券市场健康发展。 \n11.主要内容=本法规定了证券的发行、交易、上市公司的收购、信息披露、投资者保护、证券交易场所、证券公司、证券登记结算机构、证券服务机构、证券业协会、证券监督管理机构等方面的内容。具体包括证券发行的条件、程序、信息披露要求，证券交易的规则、禁止的交易行为，上市公司的收购程序，投资者保护措施，证券公司的设立、业务范围、风险管理，证券登记结算机构的职能，证券服务机构的职责，证券业协会的自律管理，以及证券监督管理机构的职责和权力等。\n【监管规则要求】\n（1）公开发行证券必须符合法律、行政法规规定的条件，并依法报经国务院证券监督管理机构或者国务院授权的部门注册。未经依法注册，任何单位和个人不得公开发行证券。（第九条）\n（2）非公开发行证券不得采用广告、公开劝诱和变相公开方式。（第九条）\n（3）发行人申请公开发行股票、可转换为股票的公司债券，依法采取承销方式的，应当聘请证券公司担任保荐人。（第十条）\n（4）公司首次公开发行新股应具备健全且运行良好的组织机构、持续经营能力，最近三年财务会计报告被出具无保留意见审计报告，且发行人及其控股股东、实际控制人最近三年不存在贪污、贿赂等刑事犯罪。（第十二条）\n（5）公开发行公司债券应具备健全且运行良好的组织机构，最近三年平均可分配利润足以支付公司债券一年的利息。（第十五条）\n（6）发行人报送的证券发行申请文件应真实、准确、完整，充分披露投资者作出价值判断和投资决策所必需的信息。（第十九条）\n（7）证券发行申请经注册后，发行人应在证券公开发行前公告公开发行募集文件，并置备于指定场所供公众查阅。（第二十三条）\n（8）证券公司承销证券应对公开发行募集文件的真实性、准确性、完整性进行核查，不得进行虚假或误导投资者的广告宣传。（第二十九条）\n（9）证券交易内幕信息的知情人和非法获取内幕信息的人在内幕信息公开前，不得买卖该公司的证券，或者泄露该信息，或者建议他人买卖该证券。（第五十三条）\n（10）禁止任何单位和个人编造、传播虚假信息或者误导性信息，扰乱证券市场。（第五十六条）\n（11）信息披露义务人披露的信息应真实、准确、完整，简明清晰，通俗易懂，不得有虚假记载、误导性陈述或者重大遗漏。（第七十八条）\n（12）上市公司应在每一会计年度结束之日起四个月内报送并公告年度报告，上半年结束之日起二个月内报送并公告中期报告。（第七十九条）\n（13）证券公司向投资者销售证券、提供服务时应充分了解投资者情况，如实说明证券、服务内容，充分揭示投资风险。（第八十八条）\n（14）证券公司不得将客户的交易结算资金和证券归入其自有财产，禁止任何单位或者个人以任何形式挪用客户的交易结算资金和证券。（第一百三十一条）\n（15）证券服务机构制作、出具的文件有虚假记载、误导性陈述或者重大遗漏的，应与委托人承担连带赔偿责任。（第一百六十三条）\n（16）违反本法规定擅自公开或者变相公开发行证券的，责令停止发行，处以非法所募资金金额百分之五以上百分之五十以下的罚款。（第一百八十条）\n（17）发行人公告的证券发行文件中隐瞒重要事实或者编造重大虚假内容的，处以非法所募资金金额百分之十以上一倍以下的罚款。（第一百八十一条）\n（18）证券公司违反规定为客户提供融资融券服务的，没收违法所得，并处以融资融券等值以下的罚款。（第二百零二条）\n（19）证券服务机构未勤勉尽责导致文件存在虚假记载的，没收业务收入，并处以业务收入一倍以上十倍以下的罚款。（第二百一十三条）\n（20）违反本法规定构成犯罪的，依法追究刑事责任。（第二百一十九条）',
    tags: ['保险', '监管'],
    url: '#',
  },
  {
    title:
      '国家金融监督管理总局关于调整保险资金权益类资产监管比例有关事项的通知3',
    description:
      '保险资金运用监管政策安全性、流动性、收益性相结合的原则，合理调整保险资金权益类资产监管比例，完善风险防控，强化报告制度。',
    source: '国家金融监督管理总局',
    publishDate: '2025年1月1日',
    content:
      '【合规监管类政策】\n【信托监管方向】\n【监管规则】  \n（1）设立信托必须采用书面形式（包括信托合同、遗嘱或法律、行政法规规定的其他书面文件）。信托合同签订时信托成立；其他书面形式设立信托的，受托人承诺时信托成立（第八条）。  \n（2）信托书面文件必须载明：信托目的；委托人、受托人姓名/名称及住所；受益人范围；信托财产范围、种类及状况；受益人取得信托利益的形式方法（第九条第一款）。  \n（3）法律、行政法规规定需办理登记手续的信托财产，应当依法办理信托登记。未登记且不补办的，信托不产生效力（第十条）。  \n（4）禁止以下信托设立情形：目的违法或损害公共利益；信托财产不确定；以非法财产设立信托；专以诉讼/讨债为目的；受益人范围不确定（第十一条）。  \n（5）信托财产需与委托人未设立信托的其他财产、受托人固有财产相区别，不得归入受托人固有财产（第十五条、第十六条）。  \n（6）受托人必须恪尽职守，履行诚实、信用、谨慎、有效管理义务，为受益人最大利益处理信托事务（第二十五条）。  \n（7）禁止受托人利用信托财产为自己谋利（第二十六条），禁止将信托财产转为固有财产（第二十七条），禁止固有财产与信托财产交易（第二十八条，信托文件另有规定或经委托人/受益人同意且公平交易的除外）。  \n（8）受托人必须将信托财产与固有财产、不同委托人的信托财产分别管理、分别记账（第二十九条）。  \n（9）共同受托人处理信托事务对第三人负债务时承担连带责任（第三十二条）。  \n（10）受托人应保存完整信托事务记录，定期向委托人和受益人报告信托财产管理运用情况（第三十三条）。  \n（11）公益信托的设立及受托人确定需经公益事业管理机构批准（第六十二条），其信托财产及收益不得用于非公益目的（第六十三条）。  \n（12）公益信托必须设置信托监察人（第六十四条），受托人未经批准不得辞任（第六十六条），每年需向管理机构提交事务处理及财产状况报告（第六十七条）。  \n【政策信息】  \n1.政策标题=中华人民共和国信托法  \n2.主管部门=全国人民代表大会常务委员会  \n3.发布日期=2001年4月28日  \n4.发文年份=2001年  \n5.适用行业=金融业  \n6.产业领域=金融  \n7.行政区划=全国  \n8.区划层级=国家级  \n9.政策背景=为了调整信托关系，规范信托行为，保护信托当事人的合法权益，促进信托事业的健康发展，制定本法。信托作为一种财产管理制度，在我国经济发展中逐渐显现其重要性。随着市场经济的发展，信托活动日益增多，但缺乏统一的法律规范，导致信托关系不明确、信托行为不规范，信托当事人的合法权益难以得到有效保护。因此，制定《中华人民共和国信托法》成为必要，以明确信托的定义、信托当事人的权利义务、信托财产的独立性等核心内容，为信托事业的健康发展提供法律保障。  \n10.政策目标=本法的目标是规范信托行为，保护信托当事人的合法权益，促进信托事业的健康发展。通过明确信托的定义、信托的设立条件、信托财产的管理与处分、信托当事人的权利义务等内容，确保信托活动的合法性和有效性。同时，本法还特别规定了公益信托的相关条款，鼓励发展公益信托，促进社会公益事业的发展。通过法律的规范，旨在建立一个公平、透明、高效的信托市场，推动信托行业的有序发展。  \n11.主要内容=本法共七章七十四条，主要内容包括：  \n第一章总则：明确了信托的定义、适用范围、基本原则等。信托是指委托人基于对受托人的信任，将其财产权委托给受托人，由受托人按委托人的意愿以自己的名义，为受益人的利益或者特定目的，进行管理或者处分的行为。信托当事人包括委托人、受托人和受益人。  \n第二章信托的设立：规定了信托设立的条件、形式、书面文件应载明的事项以及信托无效的情形。设立信托必须有合法的信托目的、确定的信托财产，并采取书面形式。信托无效的情形包括信托目的违法、信托财产不能确定等。  \n第三章信托财产：明确了信托财产的独立性，信托财产与委托人、受托人的其他财产相区别，不得强制执行，但法律另有规定的除外。  \n第四章信托当事人：详细规定了委托人、受托人和受益人的权利义务。委托人有权了解信托财产的管理情况，受托人必须为受益人的最大利益处理信托事务，受益人享有信托受益权。  \n第五章信托的的变更与终止：规定了信托变更、终止的情形及信托财产的处理方式。  \n第六章公益信托：特别规定了公益信托的设立、管理、监督及终止等内容，鼓励发展公益信托。  \n第七章附则：规定了本法的施行日期。',
    tags: ['保险', '监管'],
    url: '#',
  },
]
// 访问所有政策
const allPolicies = policiesData.policies
console.log(allPolicies, 'allPolicies')

const PolicyComplianceVerifier: React.FC = () => {
  const allPolicies = policiesData.policies
  const [policies, setPolicies] = useState<any[]>(allPolicies)
  const [searchForm, setSearchForm] = useState({
    title: '',
    direction: undefined as string | undefined,
  })
  const [form, setForm] = useState({
    name: '',
    regionCodes: undefined as string[] | undefined,
    regionLabels: undefined as string[] | undefined,
    industry: undefined as string | undefined,
  })
  const [step, setStep] = useState<
    'form' | 'result' | 'verification' | 'interpret'
  >('form')
  const [loading, setLoading] = useState(false)
  const [selectedKeys, setSelectedKeys] = useState<
    Array<{ key: string; policyInfo: string }>
  >([])
  const [modalOpen, setModalOpen] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<
    { id: string; name: string }[]
  >([])
  const [interpretList, setInterpretList] = useState<any>({})
  const [verificationData, setVerificationData] = useState('')
  const [selectVerification, setSelectVerification] = useState<any>({})

  // 处理查询
  const handleSearch = () => {
    const filteredPolicies = allPolicies.filter((policy) => {
      const titleMatch =
        !searchForm.title ||
        policy.title.toLowerCase().includes(searchForm.title.toLowerCase())
      const directionMatch =
        !searchForm.direction || policy.direction === searchForm.direction
      return titleMatch && directionMatch
    })
    console.log('Search form:', searchForm)
    console.log('Filtered policies:', filteredPolicies)
    setPolicies(filteredPolicies)
  }

  // 处理重置
  const handleSearchReset = () => {
    setSearchForm({
      title: '',
      direction: undefined,
    })
    setPolicies(allPolicies)
  }

  const handleChange = (key: string, value: any) => {
    setForm((prev) => ({ ...prev, [key]: value }))
  }

  // 通用流式接口调用方法
  const handleGeneration = useCallback(
    async (type: string, policyText: any) => {
      setLoading(true)
      let accumulatedMessages = ''

      try {
        let params: any = {
          type,
        }

        // 根据不同类型设置不同的参数
        switch (type) {
          // case '政策查询':
          //   params = {
          //     ...params,
          //     query: form.name,
          //     node: form.regionCodes,
          //     industry: form.industry,
          //   }
          //   break
          case '政策解读':
            params = {
              ...params,
              company: form.name,
              jieduzhengce: policyText,
            }
            break
          case '制度校验':
            params = {
              ...params,
              heguizhidu: [
                {
                  type: 'document',
                  transfer_method: 'local_file',
                  upload_file_id: uploadedFiles[0].id,
                },
              ],
              files: [
                {
                  type: 'document',
                  transfer_method: 'local_file',
                  upload_file_id: uploadedFiles[0].id,
                },
              ],
              jiaoyanguizw: policyText,
            }
            break
          default:
            break
        }

        await policyComplianceVerifier(params, {
          onMessage: (text: string | null, finished: boolean) => {
            if (text) {
              accumulatedMessages += text
              console.log(accumulatedMessages, 'accumulatedMessages--------')
              if (type === '制度校验') {
                setVerificationData(accumulatedMessages)
              }
            }
            if (finished) {
              setLoading(false)
              console.log(accumulatedMessages, 'accumulatedMessages')
              switch (type) {
                case '政策查询':
                  setPolicies(JSON.parse(accumulatedMessages))
                  break
                case '政策解读':
                  setInterpretList(JSON.parse(accumulatedMessages))
                  break
                // case '制度校验':
                //   // setVerification(JSON.parse(accumulatedMessages))
                //   setVerification(accumulatedMessages)
                //   break
                default:
                  break
              }
            }
          },
          onError: () => {
            setLoading(false)
          },
          onFinish: () => {
            setLoading(false)
          },
        })
      } catch (error) {
        console.error('Error:', error)
        setLoading(false)
      }
    },
    [form, uploadedFiles]
  )

  const handleQuery = async () => {
    // if (!form.name || !form.regionCodes || !form.industry) {
    if (!form.name) {
      message.warning('请填写完整信息')
      return
    }
    setLoading(true)
    setSearchForm({
      title: '',
      direction: undefined,
    })
    setTimeout(() => {
      setStep('result')
      setPolicies(allPolicies)
      setLoading(false)
    }, 1000)

    return
    // handleGeneration()
    let responseMessage = ''
    // try {
    //   await policyComplianceVerifier(
    //     {
    //       query: form.name,
    //       node: form.regionLabels?.join(''), // 你可以根据接口需要调整
    //       files: [],
    //     },
    //     {
    //       onMessage: (text: string | null, finished: boolean) => {
    //         if (text) {
    //           responseMessage += text
    //         }
    //         if (finished) {
    //           setLoading(false)
    //           try {
    //             // 假设返回是一个 JSON 数组字符串
    //             const data = JSON.parse(responseMessage)
    //             setPolicies(Array.isArray(data) ? data : [])
    //           } catch (e) {
    //             setPolicies([])
    //             message.error('解析政策数据失败')
    //           }
    //           setStep('result')
    //         }
    //       },
    //       onError: () => {
    //         setLoading(false)
    //         message.error('查询失败，请稍后重试')
    //       },
    //       onFinish: () => {
    //         setLoading(false)
    //       },
    //     }
    //   )
    // } catch (err) {
    //   setLoading(false)
    //   message.error('查询异常')
    // }
  }
  const handleInterpret = (
    type: string,
    currentItem?: { key: string; policyInfo: string }
  ) => {
    let jieduzhengce: any = []
    if (type === 'single' && currentItem) {
      jieduzhengce = [
        {
          title: currentItem.key,
          content: currentItem.policyInfo,
          // policyInfo:
          //   '1.政策标题=中华人民共和国期货和衍生品法 \n2.主管部门=国务院期货监督管理机构 \n3.发布日期=2022年4月20日 \n4.发文年份=2022年 \n5.适用行业=金融业 \n6.产业领域=金融 \n7.行政区划=全国 \n8.区划层级=国家级 \n9.政策背景=为了规范期货交易和衍生品交易行为，保障各方合法权益，维护市场秩序和社会公共利益，促进期货市场和衍生品市场服务国民经济，防范化解金融风险，维护国家经济安全。 \n10.政策目标=规范期货交易和衍生品交易活动，保障市场公平、公正、透明，防范系统性风险，维护交易者合法权益，促进期货市场和衍生品市场健康发展。 \n11.主要内容=本法共分为十三章，包括总则、期货交易和衍生品交易、期货结算与交割、期货交易者、期货经营机构、期货交易场所、期货结算机构、期货服务机构、期货业协会、监督管理、跨境交易与监管协作、法律责任和附则。主要内容包括：期货交易和衍生品交易的定义和范围；期货交易场所和结算机构的设立和管理；期货经营机构的设立和业务范围；期货交易者的权益保护；期货市场的监督管理和法律责任等。',
        },
      ]
    } else if (type === 'multiple') {
      jieduzhengce = selectedKeys.map((item) => ({
        title: item.key,
        content: item.policyInfo,
      }))
    }

    if (jieduzhengce.length === 0) {
      message.warning('请选择需要解读的政策')
      return
    }
    console.log(jieduzhengce, 'jieduzhengce')
    // return
    // let jieduzhengce = [
    //   {
    //     title: '111111111111111111111',
    //     content:
    //       '1.政策标题=中华人民共和国期货和衍生品法 \n2.主管部门=国务院期货监督管理机构 \n3.发布日期=2022年4月20日 \n4.发文年份=2022年 \n5.适用行业=金融业 \n6.产业领域=金融 \n7.行政区划=全国 \n8.区划层级=国家级 \n9.政策背景=为了规范期货交易和衍生品交易行为，保障各方合法权益，维护市场秩序和社会公共利益，促进期货市场和衍生品市场服务国民经济，防范化解金融风险，维护国家经济安全。 \n10.政策目标=规范期货交易和衍生品交易活动，保障市场公平、公正、透明，防范系统性风险，维护交易者合法权益，促进期货市场和衍生品市场健康发展。 \n11.主要内容=本法共分为十三章，包括总则、期货交易和衍生品交易、期货结算与交割、期货交易者、期货经营机构、期货交易场所、期货结算机构、期货服务机构、期货业协会、监督管理、跨境交易与监管协作、法律责任和附则。主要内容包括：期货交易和衍生品交易的定义和范围；期货交易场所和结算机构的设立和管理；期货经营机构的设立和业务范围；期货交易者的权益保护；期货市场的监督管理和法律责任等。',
    //   },
    //   {
    //     title: '222222222222222222222222222222222',
    //     content:
    //       '1.政策标题=中华人民共和国证券法 \n2.主管部门=国务院证券监督管理机构 \n3.发布日期=2019年12月28日 \n4.发文年份=2019年 \n5.适用行业=金融业 \n6.产业领域=金融 \n7.行政区划=全国 \n8.区划层级=国家级 \n9.政策背景=为了规范证券发行和交易行为，保护投资者的合法权益，维护社会经济秩序和社会公共利益，促进社会主义市场经济的发展。 \n10.政策目标=规范证券市场，保护投资者权益，维护市场秩序，促进证券市场健康发展。 \n11.主要内容=本法规定了证券的发行、交易、上市公司的收购、信息披露、投资者保护、证券交易场所、证券公司、证券登记结算机构、证券服务机构、证券业协会、证券监督管理机构等方面的内容。具体包括证券发行的条件、程序、信息披露要求，证券交易的规则、禁止的交易行为，上市公司的收购程序，投资者保护措施，证券公司的设立、业务范围、风险管理，证券登记结算机构的职能，证券服务机构的职责，证券业协会的自律管理，以及证券监督管理机构的职责和权力等。\n【监管规则要求】\n（1）公开发行证券必须符合法律、行政法规规定的条件，并依法报经国务院证券监督管理机构或者国务院授权的部门注册。未经依法注册，任何单位和个人不得公开发行证券。（第九条）\n（2）非公开发行证券不得采用广告、公开劝诱和变相公开方式。（第九条）\n（3）发行人申请公开发行股票、可转换为股票的公司债券，依法采取承销方式的，应当聘请证券公司担任保荐人。（第十条）\n（4）公司首次公开发行新股应具备健全且运行良好的组织机构、持续经营能力，最近三年财务会计报告被出具无保留意见审计报告，且发行人及其控股股东、实际控制人最近三年不存在贪污、贿赂等刑事犯罪。（第十二条）\n（5）公开发行公司债券应具备健全且运行良好的组织机构，最近三年平均可分配利润足以支付公司债券一年的利息。（第十五条）\n（6）发行人报送的证券发行申请文件应真实、准确、完整，充分披露投资者作出价值判断和投资决策所必需的信息。（第十九条）\n（7）证券发行申请经注册后，发行人应在证券公开发行前公告公开发行募集文件，并置备于指定场所供公众查阅。（第二十三条）\n（8）证券公司承销证券应对公开发行募集文件的真实性、准确性、完整性进行核查，不得进行虚假或误导投资者的广告宣传。（第二十九条）\n（9）证券交易内幕信息的知情人和非法获取内幕信息的人在内幕信息公开前，不得买卖该公司的证券，或者泄露该信息，或者建议他人买卖该证券。（第五十三条）\n（10）禁止任何单位和个人编造、传播虚假信息或者误导性信息，扰乱证券市场。（第五十六条）\n（11）信息披露义务人披露的信息应真实、准确、完整，简明清晰，通俗易懂，不得有虚假记载、误导性陈述或者重大遗漏。（第七十八条）\n（12）上市公司应在每一会计年度结束之日起四个月内报送并公告年度报告，上半年结束之日起二个月内报送并公告中期报告。（第七十九条）\n（13）证券公司向投资者销售证券、提供服务时应充分了解投资者情况，如实说明证券、服务内容，充分揭示投资风险。（第八十八条）\n（14）证券公司不得将客户的交易结算资金和证券归入其自有财产，禁止任何单位或者个人以任何形式挪用客户的交易结算资金和证券。（第一百三十一条）\n（15）证券服务机构制作、出具的文件有虚假记载、误导性陈述或者重大遗漏的，应与委托人承担连带赔偿责任。（第一百六十三条）\n（16）违反本法规定擅自公开或者变相公开发行证券的，责令停止发行，处以非法所募资金金额百分之五以上百分之五十以下的罚款。（第一百八十条）\n（17）发行人公告的证券发行文件中隐瞒重要事实或者编造重大虚假内容的，处以非法所募资金金额百分之十以上一倍以下的罚款。（第一百八十一条）\n（18）证券公司违反规定为客户提供融资融券服务的，没收违法所得，并处以融资融券等值以下的罚款。（第二百零二条）\n（19）证券服务机构未勤勉尽责导致文件存在虚假记载的，没收业务收入，并处以业务收入一倍以上十倍以下的罚款。（第二百一十三条）\n（20）违反本法规定构成犯罪的，依法追究刑事责任。（第二百一十九条）',
    //   },
    //   {
    //     title: '33333333333333333333333333333333333',
    //     content:
    //       '【合规监管类政策】\n【信托监管方向】\n【监管规则】  \n（1）设立信托必须采用书面形式（包括信托合同、遗嘱或法律、行政法规规定的其他书面文件）。信托合同签订时信托成立；其他书面形式设立信托的，受托人承诺时信托成立（第八条）。  \n（2）信托书面文件必须载明：信托目的；委托人、受托人姓名/名称及住所；受益人范围；信托财产范围、种类及状况；受益人取得信托利益的形式方法（第九条第一款）。  \n（3）法律、行政法规规定需办理登记手续的信托财产，应当依法办理信托登记。未登记且不补办的，信托不产生效力（第十条）。  \n（4）禁止以下信托设立情形：目的违法或损害公共利益；信托财产不确定；以非法财产设立信托；专以诉讼/讨债为目的；受益人范围不确定（第十一条）。  \n（5）信托财产需与委托人未设立信托的其他财产、受托人固有财产相区别，不得归入受托人固有财产（第十五条、第十六条）。  \n（6）受托人必须恪尽职守，履行诚实、信用、谨慎、有效管理义务，为受益人最大利益处理信托事务（第二十五条）。  \n（7）禁止受托人利用信托财产为自己谋利（第二十六条），禁止将信托财产转为固有财产（第二十七条），禁止固有财产与信托财产交易（第二十八条，信托文件另有规定或经委托人/受益人同意且公平交易的除外）。  \n（8）受托人必须将信托财产与固有财产、不同委托人的信托财产分别管理、分别记账（第二十九条）。  \n（9）共同受托人处理信托事务对第三人负债务时承担连带责任（第三十二条）。  \n（10）受托人应保存完整信托事务记录，定期向委托人和受益人报告信托财产管理运用情况（第三十三条）。  \n（11）公益信托的设立及受托人确定需经公益事业管理机构批准（第六十二条），其信托财产及收益不得用于非公益目的（第六十三条）。  \n（12）公益信托必须设置信托监察人（第六十四条），受托人未经批准不得辞任（第六十六条），每年需向管理机构提交事务处理及财产状况报告（第六十七条）。  \n【政策信息】  \n1.政策标题=中华人民共和国信托法  \n2.主管部门=全国人民代表大会常务委员会  \n3.发布日期=2001年4月28日  \n4.发文年份=2001年  \n5.适用行业=金融业  \n6.产业领域=金融  \n7.行政区划=全国  \n8.区划层级=国家级  \n9.政策背景=为了调整信托关系，规范信托行为，保护信托当事人的合法权益，促进信托事业的健康发展，制定本法。信托作为一种财产管理制度，在我国经济发展中逐渐显现其重要性。随着市场经济的发展，信托活动日益增多，但缺乏统一的法律规范，导致信托关系不明确、信托行为不规范，信托当事人的合法权益难以得到有效保护。因此，制定《中华人民共和国信托法》成为必要，以明确信托的定义、信托当事人的权利义务、信托财产的独立性等核心内容，为信托事业的健康发展提供法律保障。  \n10.政策目标=本法的目标是规范信托行为，保护信托当事人的合法权益，促进信托事业的健康发展。通过明确信托的定义、信托的设立条件、信托财产的管理与处分、信托当事人的权利义务等内容，确保信托活动的合法性和有效性。同时，本法还特别规定了公益信托的相关条款，鼓励发展公益信托，促进社会公益事业的发展。通过法律的规范，旨在建立一个公平、透明、高效的信托市场，推动信托行业的有序发展。  \n11.主要内容=本法共七章七十四条，主要内容包括：  \n第一章总则：明确了信托的定义、适用范围、基本原则等。信托是指委托人基于对受托人的信任，将其财产权委托给受托人，由受托人按委托人的意愿以自己的名义，为受益人的利益或者特定目的，进行管理或者处分的行为。信托当事人包括委托人、受托人和受益人。  \n第二章信托的设立：规定了信托设立的条件、形式、书面文件应载明的事项以及信托无效的情形。设立信托必须有合法的信托目的、确定的信托财产，并采取书面形式。信托无效的情形包括信托目的违法、信托财产不能确定等。  \n第三章信托财产：明确了信托财产的独立性，信托财产与委托人、受托人的其他财产相区别，不得强制执行，但法律另有规定的除外。  \n第四章信托当事人：详细规定了委托人、受托人和受益人的权利义务。委托人有权了解信托财产的管理情况，受托人必须为受益人的最大利益处理信托事务，受益人享有信托受益权。  \n第五章信托的的变更与终止：规定了信托变更、终止的情形及信托财产的处理方式。  \n第六章公益信托：特别规定了公益信托的设立、管理、监督及终止等内容，鼓励发展公益信托。  \n第七章附则：规定了本法的施行日期。',
    //   },
    // ]
    // let jieduzhengce = [
    //   "1.政策标题=中华人民共和国期货和衍生品法 \n2.主管部门=国务院期货监督管理机构 \n3.发布日期=2022年4月20日 \n4.发文年份=2022年 \n5.适用行业=金融业 \n6.产业领域=金融 \n7.行政区划=全国 \n8.区划层级=国家级 \n9.政策背景=为了规范期货交易和衍生品交易行为，保障各方合法权益，维护市场秩序和社会公共利益，促进期货市场和衍生品市场服务国民经济，防范化解金融风险，维护国家经济安全。 \n10.政策目标=规范期货交易和衍生品交易活动，保障市场公平、公正、透明，防范系统性风险，维护交易者合法权益，促进期货市场和衍生品市场健康发展。 \n11.主要内容=本法共分为十三章，包括总则、期货交易和衍生品交易、期货结算与交割、期货交易者、期货经营机构、期货交易场所、期货结算机构、期货服务机构、期货业协会、监督管理、跨境交易与监管协作、法律责任和附则。主要内容包括：期货交易和衍生品交易的定义和范围；期货交易场所和结算机构的设立和管理；期货经营机构的设立和业务范围；期货交易者的权益保护；期货市场的监督管理和法律责任等。",
    //   "1.政策标题=中华人民共和国证券法 \n2.主管部门=国务院证券监督管理机构 \n3.发布日期=2019年12月28日 \n4.发文年份=2019年 \n5.适用行业=金融业 \n6.产业领域=金融 \n7.行政区划=全国 \n8.区划层级=国家级 \n9.政策背景=为了规范证券发行和交易行为，保护投资者的合法权益，维护社会经济秩序和社会公共利益，促进社会主义市场经济的发展。 \n10.政策目标=规范证券市场，保护投资者权益，维护市场秩序，促进证券市场健康发展。 \n11.主要内容=本法规定了证券的发行、交易、上市公司的收购、信息披露、投资者保护、证券交易场所、证券公司、证券登记结算机构、证券服务机构、证券业协会、证券监督管理机构等方面的内容。具体包括证券发行的条件、程序、信息披露要求，证券交易的规则、禁止的交易行为，上市公司的收购程序，投资者保护措施，证券公司的设立、业务范围、风险管理，证券登记结算机构的职能，证券服务机构的职责，证券业协会的自律管理，以及证券监督管理机构的职责和权力等。\n【监管规则要求】\n（1）公开发行证券必须符合法律、行政法规规定的条件，并依法报经国务院证券监督管理机构或者国务院授权的部门注册。未经依法注册，任何单位和个人不得公开发行证券。（第九条）\n（2）非公开发行证券不得采用广告、公开劝诱和变相公开方式。（第九条）\n（3）发行人申请公开发行股票、可转换为股票的公司债券，依法采取承销方式的，应当聘请证券公司担任保荐人。（第十条）\n（4）公司首次公开发行新股应具备健全且运行良好的组织机构、持续经营能力，最近三年财务会计报告被出具无保留意见审计报告，且发行人及其控股股东、实际控制人最近三年不存在贪污、贿赂等刑事犯罪。（第十二条）\n（5）公开发行公司债券应具备健全且运行良好的组织机构，最近三年平均可分配利润足以支付公司债券一年的利息。（第十五条）\n（6）发行人报送的证券发行申请文件应真实、准确、完整，充分披露投资者作出价值判断和投资决策所必需的信息。（第十九条）\n（7）证券发行申请经注册后，发行人应在证券公开发行前公告公开发行募集文件，并置备于指定场所供公众查阅。（第二十三条）\n（8）证券公司承销证券应对公开发行募集文件的真实性、准确性、完整性进行核查，不得进行虚假或误导投资者的广告宣传。（第二十九条）\n（9）证券交易内幕信息的知情人和非法获取内幕信息的人在内幕信息公开前，不得买卖该公司的证券，或者泄露该信息，或者建议他人买卖该证券。（第五十三条）\n（10）禁止任何单位和个人编造、传播虚假信息或者误导性信息，扰乱证券市场。（第五十六条）\n（11）信息披露义务人披露的信息应真实、准确、完整，简明清晰，通俗易懂，不得有虚假记载、误导性陈述或者重大遗漏。（第七十八条）\n（12）上市公司应在每一会计年度结束之日起四个月内报送并公告年度报告，上半年结束之日起二个月内报送并公告中期报告。（第七十九条）\n（13）证券公司向投资者销售证券、提供服务时应充分了解投资者情况，如实说明证券、服务内容，充分揭示投资风险。（第八十八条）\n（14）证券公司不得将客户的交易结算资金和证券归入其自有财产，禁止任何单位或者个人以任何形式挪用客户的交易结算资金和证券。（第一百三十一条）\n（15）证券服务机构制作、出具的文件有虚假记载、误导性陈述或者重大遗漏的，应与委托人承担连带赔偿责任。（第一百六十三条）\n（16）违反本法规定擅自公开或者变相公开发行证券的，责令停止发行，处以非法所募资金金额百分之五以上百分之五十以下的罚款。（第一百八十条）\n（17）发行人公告的证券发行文件中隐瞒重要事实或者编造重大虚假内容的，处以非法所募资金金额百分之十以上一倍以下的罚款。（第一百八十一条）\n（18）证券公司违反规定为客户提供融资融券服务的，没收违法所得，并处以融资融券等值以下的罚款。（第二百零二条）\n（19）证券服务机构未勤勉尽责导致文件存在虚假记载的，没收业务收入，并处以业务收入一倍以上十倍以下的罚款。（第二百一十三条）\n（20）违反本法规定构成犯罪的，依法追究刑事责任。（第二百一十九条）",
    //   "【合规监管类政策】\n【信托监管方向】\n【监管规则】  \n（1）设立信托必须采用书面形式（包括信托合同、遗嘱或法律、行政法规规定的其他书面文件）。信托合同签订时信托成立；其他书面形式设立信托的，受托人承诺时信托成立（第八条）。  \n（2）信托书面文件必须载明：信托目的；委托人、受托人姓名/名称及住所；受益人范围；信托财产范围、种类及状况；受益人取得信托利益的形式方法（第九条第一款）。  \n（3）法律、行政法规规定需办理登记手续的信托财产，应当依法办理信托登记。未登记且不补办的，信托不产生效力（第十条）。  \n（4）禁止以下信托设立情形：目的违法或损害公共利益；信托财产不确定；以非法财产设立信托；专以诉讼/讨债为目的；受益人范围不确定（第十一条）。  \n（5）信托财产需与委托人未设立信托的其他财产、受托人固有财产相区别，不得归入受托人固有财产（第十五条、第十六条）。  \n（6）受托人必须恪尽职守，履行诚实、信用、谨慎、有效管理义务，为受益人最大利益处理信托事务（第二十五条）。  \n（7）禁止受托人利用信托财产为自己谋利（第二十六条），禁止将信托财产转为固有财产（第二十七条），禁止固有财产与信托财产交易（第二十八条，信托文件另有规定或经委托人/受益人同意且公平交易的除外）。  \n（8）受托人必须将信托财产与固有财产、不同委托人的信托财产分别管理、分别记账（第二十九条）。  \n（9）共同受托人处理信托事务对第三人负债务时承担连带责任（第三十二条）。  \n（10）受托人应保存完整信托事务记录，定期向委托人和受益人报告信托财产管理运用情况（第三十三条）。  \n（11）公益信托的设立及受托人确定需经公益事业管理机构批准（第六十二条），其信托财产及收益不得用于非公益目的（第六十三条）。  \n（12）公益信托必须设置信托监察人（第六十四条），受托人未经批准不得辞任（第六十六条），每年需向管理机构提交事务处理及财产状况报告（第六十七条）。  \n【政策信息】  \n1.政策标题=中华人民共和国信托法  \n2.主管部门=全国人民代表大会常务委员会  \n3.发布日期=2001年4月28日  \n4.发文年份=2001年  \n5.适用行业=金融业  \n6.产业领域=金融  \n7.行政区划=全国  \n8.区划层级=国家级  \n9.政策背景=为了调整信托关系，规范信托行为，保护信托当事人的合法权益，促进信托事业的健康发展，制定本法。信托作为一种财产管理制度，在我国经济发展中逐渐显现其重要性。随着市场经济的发展，信托活动日益增多，但缺乏统一的法律规范，导致信托关系不明确、信托行为不规范，信托当事人的合法权益难以得到有效保护。因此，制定《中华人民共和国信托法》成为必要，以明确信托的定义、信托当事人的权利义务、信托财产的独立性等核心内容，为信托事业的健康发展提供法律保障。  \n10.政策目标=本法的目标是规范信托行为，保护信托当事人的合法权益，促进信托事业的健康发展。通过明确信托的定义、信托的设立条件、信托财产的管理与处分、信托当事人的权利义务等内容，确保信托活动的合法性和有效性。同时，本法还特别规定了公益信托的相关条款，鼓励发展公益信托，促进社会公益事业的发展。通过法律的规范，旨在建立一个公平、透明、高效的信托市场，推动信托行业的有序发展。  \n11.主要内容=本法共七章七十四条，主要内容包括：  \n第一章总则：明确了信托的定义、适用范围、基本原则等。信托是指委托人基于对受托人的信任，将其财产权委托给受托人，由受托人按委托人的意愿以自己的名义，为受益人的利益或者特定目的，进行管理或者处分的行为。信托当事人包括委托人、受托人和受益人。  \n第二章信托的设立：规定了信托设立的条件、形式、书面文件应载明的事项以及信托无效的情形。设立信托必须有合法的信托目的、确定的信托财产，并采取书面形式。信托无效的情形包括信托目的违法、信托财产不能确定等。  \n第三章信托财产：明确了信托财产的独立性，信托财产与委托人、受托人的其他财产相区别，不得强制执行，但法律另有规定的除外。  \n第四章信托当事人：详细规定了委托人、受托人和受益人的权利义务。委托人有权了解信托财产的管理情况，受托人必须为受益人的最大利益处理信托事务，受益人享有信托受益权。  \n第五章信托的的变更与终止：规定了信托变更、终止的情形及信托财产的处理方式。  \n第六章公益信托：特别规定了公益信托的设立、管理、监督及终止等内容，鼓励发展公益信托。  \n第七章附则：规定了本法的施行日期。"
    // ]

    handleGeneration('政策解读', JSON.stringify(jieduzhengce))
    setStep('interpret')
  }
  const openModal = (data: any) => {
    setModalOpen(true)
    setUploadedFiles([])
    setSelectVerification(data)
  }

  const handleVerification = () => {
    setModalOpen(false)
    handleGeneration(
      '制度校验',
      JSON.stringify(selectVerification.regulatoryRules)
    )
    setStep('verification')
  }

  const handleReset = () => {
    setForm({ name: '', region: undefined, industry: undefined })
    setStep('form')
    setPolicies([])
  }
  // 1. 省份
  const provinces = Object.entries(areaList.province_list).map(
    ([provinceCode, provinceName]) => ({
      value: provinceCode,
      label: provinceName,
      children: [],
    })
  )

  // 2. 城市
  const cities = Object.entries(areaList.city_list).map(
    ([cityCode, cityName]) => ({
      value: cityCode,
      label: cityName,
      provinceCode: cityCode.slice(0, 2) + '0000', // 省份前缀
      children: [],
    })
  )

  // 3. 区县
  const districts = Object.entries(areaList.county_list).map(
    ([districtCode, districtName]) => ({
      value: districtCode,
      label: districtName,
      cityCode: districtCode.slice(0, 4) + '00', // 城市前缀
    })
  )

  // 4. 组装嵌套结构
  cities.forEach((city) => {
    city.children = districts.filter((d) => d.cityCode === city.value)
  })
  provinces.forEach((province) => {
    province.children = cities.filter((c) => c.provinceCode === province.value)
  })

  // 5. 最终可用于 Antd Cascader 的 options
  const regionOptions = provinces

  // 选中/取消选中
  const handleSelect = (key: string, policyInfo: string) => {
    console.log(key, 'key')
    setSelectedKeys((prev) => {
      const existingIndex = prev.findIndex((item) => item.key === key)
      if (existingIndex !== -1) {
        // 如果已存在，则移除
        return prev.filter((item) => item.key !== key)
      } else if (prev.length < 3) {
        // 如果不存在且未超过3个，则添加
        return [...prev, { key, policyInfo }]
      } else {
        message.warning('最多只能选择3个解读')
        return prev
      }
    })
  }

  // 上传文件前处理（仿contract-review）
  const beforeUpload = (file: File) => {
    const ext = file.name
      .substring(file.name.lastIndexOf('.') + 1)
      ?.toLowerCase()
    if (!['docx', 'pdf'].includes(ext)) {
      message.error(
        '目前仅支持.docx, .pdf类型的文件，请您将文件转成这些格式后再次进行上传'
      )
      return false
    }
    message.loading({ key: 'uploading', content: '文件上传中' })
    uploadFile(file, token).then(async (response) => {
      message.destroy('uploading')
      if (response.id) {
        setUploadedFiles([{ id: response.id, name: file.name }])
        message.success('文件上传成功')
      } else {
        message.error('文件上传失败')
      }
    })
    return false
  }

  const handleDelete = (fileId: string) => {
    setUploadedFiles((prevFiles) =>
      prevFiles.filter((file) => file.id !== fileId)
    )
  }

  return (
    <Spin spinning={loading}>
      <div className="policy-compliance-verifier-container">
        {/* <div className='policy-compliance-toolbar'>
          <div className='policy-compliance-title'>政策查询</div>
        </div> */}
        <Flex className="toolbar" justify="center">
          <Typography.Text className="title-text">
            {step === 'form' || step === 'result'
              ? '政策查询'
              : step === 'verification'
              ? '智能校验'
              : step === 'interpret'
              ? '政策解读'
              : ''}
          </Typography.Text>
          {step === 'result' && (
            <Button
              onClick={() => setStep('form')}
              style={{ position: 'absolute', left: '3%' }}
            >
              返回
            </Button>
          )}
        </Flex>
        {step === 'form' && (
          <Card style={{ maxWidth: 600, margin: '0 auto', padding: 32 }}>
            <Flex vertical gap={32}>
              <div>
                <Text>企业名称</Text>
                <Input
                  placeholder="请输入"
                  value={form.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  style={{ marginTop: 8 }}
                />
              </div>
              <div>
                <Text>企业注册地</Text>
                <Cascader
                  options={regionOptions}
                  placeholder="请选择省/市/区"
                  value={form.regionCodes}
                  onChange={(codes, selectedOptions) => {
                    setForm((prev) => ({
                      ...prev,
                      regionCodes: codes,
                      regionLabels: selectedOptions.map((opt) => opt.label),
                    }))
                  }}
                  style={{ width: '100%', marginTop: 8 }}
                />
              </div>
              <div>
                <Text>行业类型</Text>
                <Select
                  placeholder="请选择"
                  value={form.industry}
                  options={industries}
                  onChange={(v) => handleChange('industry', v)}
                  style={{ width: '100%', marginTop: 8 }}
                />
              </div>
              <Button type="primary" block size="large" onClick={handleQuery}>
                政策查询
              </Button>
            </Flex>
          </Card>
        )}
        {step === 'result' && (
          <div style={{ maxWidth: 1200, margin: '0 auto' }}>
            <Flex
              align="center"
              justify="center"
              gap={16}
              style={{ margin: '0 0 24px 0' }}
            >
              <Input
                placeholder="请输入政策标题关键词"
                style={{ width: 320 }}
                value={searchForm.title}
                onChange={(e) =>
                  setSearchForm((prev) => ({ ...prev, title: e.target.value }))
                }
              />
              <Select
                placeholder="政策类型"
                style={{ width: 180 }}
                allowClear
                options={policyType}
                value={searchForm.direction}
                onChange={(value) =>
                  setSearchForm((prev) => ({ ...prev, direction: value }))
                }
              />
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleSearchReset}>重置</Button>
            </Flex>
            {/* 批量解读按钮 */}
            <Flex align="center" gap={16} style={{ margin: '24px 0 8px 0' }}>
              <Button
                type="primary"
                onClick={() => handleInterpret('multiple')}
              >
                批量解读 ({selectedKeys.length}/3)
              </Button>
            </Flex>
            {selectedKeys.length > 0 && (
              <div style={{ marginBottom: 10 }}>
                {selectedKeys.map((item) => (
                  <Flex
                    align="center"
                    gap={12}
                    justify="flex-start"
                    key={item.key}
                  >
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {item.key}
                    </Text>
                    <CloseOutlined
                      style={{
                        marginLeft: 50,
                        color: 'rgba(0,0,0,0.45)',
                        fontSize: 12,
                      }}
                      onClick={() => handleSelect(item.key, item.policyInfo)}
                    />
                  </Flex>
                ))}
              </div>
            )}
            <Row gutter={[0, 16]}>
              {policies.map((item, idx) => {
                const key = item.title
                const content = item.content
                const policyInfo = item.policyInfo
                const checked = selectedKeys.some(
                  (selected) => selected.key === key
                )
                return (
                  <Col span={24} key={key}>
                    <Card style={{ marginBottom: 8 }}>
                      <Flex align="center" gap={12}>
                        <span
                          style={{ cursor: 'pointer', fontSize: 22 }}
                          onClick={() => handleSelect(key, policyInfo)}
                        >
                          {checked ? (
                            <CheckCircleTwoTone twoToneColor="#1677ff" />
                          ) : (
                            <CheckCircleOutlined style={{ color: '#d9d9d9' }} />
                          )}
                        </span>
                        <div style={{ flex: 1 }}>
                          <div>
                            <Flex align="center" gap={2}>
                              <Tag
                                bordered={false}
                                color="processing"
                                style={{ fontSize: 10 }}
                              >
                                {item.level}
                              </Tag>
                              <Title level={5} style={{ margin: 0 }}>
                                {item.title}
                              </Title>
                            </Flex>
                            <Paragraph
                              type="secondary"
                              style={{ margin: '8px 0' }}
                              ellipsis={{ rows: 2, expandable: false }}
                            >
                              {item.content}
                            </Paragraph>
                          </div>
                          <Flex
                            justify="space-between"
                            align="center"
                            style={{ flex: 1, marginTop: '15px' }}
                          >
                            <Text type="secondary">
                              {item.department} | {item.publishDate}
                            </Text>
                            <div>
                              <Button
                                type="link"
                                onClick={() =>
                                  handleInterpret('single', {
                                    key: item.title,
                                    policyInfo: item.policyInfo,
                                  })
                                }
                              >
                                政策解读
                              </Button>
                              {item.direction != '非监管类政策' && (
                                <Button
                                  type="link"
                                  onClick={() => openModal(item)}
                                >
                                  制度合规校验
                                </Button>
                              )}
                            </div>
                          </Flex>
                        </div>
                      </Flex>
                    </Card>
                  </Col>
                )
              })}
            </Row>
            {/* 分页 */}
            <Flex justify="center" style={{ margin: '24px 0' }}>
              <Pagination
                current={1}
                pageSize={10}
                total={policies.length}
                showSizeChanger={false}
              />
            </Flex>
          </div>
        )}
        {step === 'verification' && (
          <div style={{ maxWidth: 1300, margin: '0 auto', padding: 32 }}>
            <Flex justify="flex-end" align="center">
              <Button onClick={() => setStep('result')}>关闭</Button>
            </Flex>

            {verificationData ? (
              <Card
                style={{ width: '100%', overflow: 'auto', marginTop: '10px' }}
              >
                {/* <StreamTypewriter text={verificationData} end={false} /> */}
                <ReactMarkdown
                  className="markdown-body"
                  remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}
                  rehypePlugins={[rehypeRaw]}
                >
                  {verificationData}
                </ReactMarkdown>
              </Card>
            ) : (
              <Flex justify="center" align="center" vertical>
                <Typography.Text>正在生成校验结果，请稍候...</Typography.Text>
              </Flex>
            )}
          </div>
        )}
        {step === 'interpret' && (
          <div>
            <Flex
              justify="flex-end"
              align="center"
              style={{ margin: '0 32px' }}
            >
              <Button onClick={() => setStep('result')}>关闭</Button>
            </Flex>
            <PolicyInterpretation interpretList={interpretList} />
          </div>
        )}
        {/* 弹窗 */}
        <Modal
          title={
            <span style={{ fontWeight: 600, fontSize: 20 }}>上传管理制度</span>
          }
          open={modalOpen}
          onCancel={() => setModalOpen(false)}
          footer={null}
          width={500}
          centered
          destroyOnClose
        >
          <Upload.Dragger
            showUploadList={false}
            beforeUpload={beforeUpload}
            multiple={false}
            accept=".pdf,.docx"
            style={{ margin: '32px 0' }}
          >
            <div className="ant-upload-drag-icon">
              {uploadedFiles.length > 0 ? (
                <CheckCircleFilled />
              ) : (
                <InboxOutlined />
              )}
            </div>
            <div className="ant-upload-hint">
              <span>拖拽文件到此处上传</span>
              <br />
              <span style={{ fontSize: '12px', color: '#999' }}>
                或点击选择文件
              </span>
            </div>
          </Upload.Dragger>
          {uploadedFiles.length > 0 && (
            <div className="file-list-contract" style={{ margin: '12px 0' }}>
              {uploadedFiles.map((file) => (
                <div
                  key={file.id}
                  className="file-item"
                  style={{ display: 'flex', alignItems: 'center', gap: 8 }}
                >
                  <span>{file.name}</span>
                  <DeleteOutlined
                    onClick={() => handleDelete(file.id)}
                    style={{ cursor: 'pointer' }}
                  />
                </div>
              ))}
            </div>
          )}
          <Button
            type="primary"
            block
            size="large"
            disabled={uploadedFiles.length === 0}
            style={{ marginTop: 16 }}
            onClick={handleVerification}
          >
            智能校验
          </Button>
        </Modal>
      </div>
    </Spin>
  )
}

export default PolicyComplianceVerifier
