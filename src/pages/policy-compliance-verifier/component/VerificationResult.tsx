import React from 'react'
import { Typography } from 'antd'

const { Title, Paragraph, Text } = Typography
interface VerificationResultProps {
  verification: any
}
const VerificationResult: React.FC<VerificationResultProps> = (
  verification
) => (
  <div style={{ background: '#f7fbff', minHeight: '100vh', padding: '32px 0' }}>
    <div
      style={{
        maxWidth: 1100,
        margin: '0 auto',
        background: '#fff',
        borderRadius: 8,
        padding: 32,
      }}
    >
      {/* <Title level={3} style={{ textAlign: 'center', marginBottom: 32, color: '#409eff', fontWeight: 600 }}>智能校验</Title> */}

      {/* 合法性与合规性 */}
      {/* <Title level={5} style={{ color: '#1677ff', marginTop: 32 }}>合法性与合规性</Title>
      <Paragraph style={{ marginBottom: 8 }}><Text strong>【具体校验内容及结果】</Text></Paragraph>
      <Paragraph style={{ marginBottom: 8 }}>
        合同条款：公司治理结构应当包括股东会或者股东大会（符合），但未列明的权责分为其他机构名称（不符合第十条、第十三条）。<br />
        合同条款：公司章程应当对股东权利义务及其转让作出规定（符合第十二条），但未载明公司治理结构相关的约定（不符合第十二条第二款）。<br />
        合同条款：公司应当依法设立监事会（符合），但未规定监事会的具体职责和权利（不符合第十三条）。
      </Paragraph>
      <Paragraph style={{ marginBottom: 8 }}><Text strong>【调整建议】</Text></Paragraph>
      <Paragraph style={{ marginBottom: 8 }}>
        补充完善监事会的职责和权利描述，明确其在公司治理中的地位（参照第十七条）。<br />
        补充完善公司章程中关于股东权利义务的规定，并补充章程中对监事会职责的约定（参照第十二条第二款）。
      </Paragraph>
      <Paragraph style={{ marginBottom: 8 }}><Text strong>【可优化点】</Text></Paragraph>
      <Paragraph>将相关条款用"应当"而非"可以"或"参考"表述（参照第二十条），而不采用"重大决策"。</Paragraph> */}

      {/* 目标与导向一致性 */}
      {/* <Title level={5} style={{ color: '#1677ff', marginTop: 32 }}>目标与导向一致性</Title>
      <Paragraph style={{ marginBottom: 8 }}><Text strong>【具体校验内容及结果】</Text></Paragraph>
      <Paragraph style={{ marginBottom: 8 }}>
        公司提出"合规治理结构"目标（符合第十条），但未体现"全员主体合规"的具体措施（如责任制约）。<br />
        合同条款未明确责任归属（符合第六条），未体现激励与约束机制（不符合第十六条创优要求）。
      </Paragraph>
      <Paragraph style={{ marginBottom: 8 }}><Text strong>【调整建议】</Text></Paragraph>
      <Paragraph>补充完善目标指向和具体措施的责任分与考评标准。新增相应中间目标。</Paragraph>
      <Paragraph style={{ marginBottom: 8 }}><Text strong>【可优化点】</Text></Paragraph>
      <Paragraph>引入"合规文化"标语和机制（参照第十一条第六款），定期建立文化交流效果。</Paragraph> */}

      {/* 逻辑性与衔接完整性 */}
      {/* <Title level={5} style={{ color: '#1677ff', marginTop: 32 }}>逻辑性与衔接完整性</Title>
      <Paragraph style={{ marginBottom: 8 }}><Text strong>【具体校验内容及结果】</Text></Paragraph>
      <Paragraph>文本</Paragraph>
      <Paragraph style={{ marginBottom: 8 }}><Text strong>【调整建议】</Text></Paragraph>
      <Paragraph>文本</Paragraph>
      <Paragraph style={{ marginBottom: 8 }}><Text strong>【可优化点】</Text></Paragraph>
      <Paragraph>文本</Paragraph> */}

      {/* 风险揭示与防控措施 */}
      {/* <Title level={5} style={{ color: '#1677ff', marginTop: 32 }}>风险揭示与防控措施</Title>
      <Paragraph style={{ marginBottom: 8 }}><Text strong>【具体校验内容及结果】</Text></Paragraph>
      <Paragraph>文本</Paragraph>
      <Paragraph style={{ marginBottom: 8 }}><Text strong>【调整建议】</Text></Paragraph>
      <Paragraph>文本</Paragraph>
      <Paragraph style={{ marginBottom: 8 }}><Text strong>【可优化点】</Text></Paragraph>
      <Paragraph>文本</Paragraph> */}

      {/* 信息披露与透明度 */}
      {/* <Title level={5} style={{ color: '#1677ff', marginTop: 32 }}>信息披露与透明度</Title>
      <Paragraph style={{ marginBottom: 8 }}><Text strong>【具体校验内容及结果】</Text></Paragraph>
      <Paragraph>文本</Paragraph>
      <Paragraph style={{ marginBottom: 8 }}><Text strong>【调整建议】</Text></Paragraph>
      <Paragraph>文本</Paragraph>
      <Paragraph style={{ marginBottom: 8 }}><Text strong>【可优化点】</Text></Paragraph>
      <Paragraph>文本</Paragraph> */}
    </div>
  </div>
)

export default VerificationResult
