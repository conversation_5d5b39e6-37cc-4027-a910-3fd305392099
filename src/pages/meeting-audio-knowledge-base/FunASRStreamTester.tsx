import { useState, useRef, useEffect } from 'react'
import { Card, Select, Button, Progress, message, Space, Typography, Upload, UploadProps } from 'antd'
import {
  UploadOutlined,
  PlayCircleOutlined,
  StopOutlined,
  DeleteOutlined,
  CopyOutlined,
  CloudUploadOutlined,
  <PERSON>Outlined,
  SettingOutlined,
  CommentOutlined,
  ConsoleSqlOutlined
} from '@ant-design/icons'
import { copyText } from '@/utils/clipboard'
import { getKnowledgeData } from '@/api/contractSceneSet'

const { Text, Link } = Typography

type LogType = 'secondary' | 'success' | 'warning' | 'danger' | ''

type props = {
  Tenantid: string
  Token: string
  handleStart: (personalLibs: string[], query: string) => void
  generating: boolean
}

const FunASRStreamTester: React.FC<props> = ({ Tenantid, Token, handleStart, generating }) => {
  const [apiUrl, setApiUrl] = useState<string>('/funasr/recognize/stream')
  const [mode, setMode] = useState<'offline' | 'online' | '2pass'>('offline')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isProcessing, setIsProcessing] = useState<boolean>(false)
  const [progress, setProgress] = useState<number>(0)
  const [status, setStatus] = useState<'idle' | 'connecting' | 'processing' | 'completed' | 'error'>('idle')
  const [resultText, setResultText] = useState<string>('等待开始识别...')
  const [logs, setLogs] = useState<{ value: string; type: LogType }[]>([])
  const abortControllerRef = useRef<AbortController | null>(null)
  const [cardData, setCardData] = useState<any[]>([])
  const [personalLibs, setPersonalLibs] = useState<string[]>([])

  const getAllListData = () => {
    getKnowledgeData(
      {
        pageNum: 1,
        pageSize: 999999,
        entity: {
          libName: ''
        }
      },
      Tenantid,
      Token
    ).then(res => {
      if (res.code === 200) {
        setCardData(res.data.records)
      }
    })
  }

  useEffect(() => {
    getAllListData()
  }, [])

  type Status = 'idle' | 'connecting' | 'processing' | 'completed' | 'error'

  const statusMap: Record<Status, { text: string; color: 'default' | 'processing' | 'success' | 'error' }> = {
    idle: { text: '待机', color: 'default' },
    connecting: { text: '连接中', color: 'processing' },
    processing: { text: '识别中', color: 'processing' },
    completed: { text: '完成', color: 'success' },
    error: { text: '错误', color: 'error' }
  }

  const handleFileChange = (info: { file: File & { status?: string; name: string; size: number; type: string } }) => {
    const file = info.file
    if (file.status === 'removed') {
      setSelectedFile(null)
      addLog('已移除文件', '')
      return
    }

    const supportedTypes = [
      'audio/mpeg',
      'audio/mp3',
      'audio/mp4',
      'audio/m4a',
      'audio/wav',
      'audio/amr',
      'audio/aac',
      'audio/flac',
      'audio/ogg',
      'audio/mpga'
    ]

    if (!supportedTypes.some(type => file.type.includes(type.split('/')[1]))) {
      addLog(`不支持的文件格式: ${file.type}`, 'danger')
      message.error('不支持的文件格式')
      return
    }

    setSelectedFile(file)
    addLog(`文件已选择: ${file.name} (${formatFileSize(file.size)})`, 'success')
  }
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const startRecognition = async () => {
    if (!selectedFile) {
      addLog('请先选择音频文件', 'danger')
      message.error('请先选择音频文件')
      return
    }

    setIsProcessing(true)
    setStatus('connecting')
    setProgress(0)
    addLog('开始连接到服务器...', '')

    try {
      abortControllerRef.current = new AbortController()

      const formData = new FormData()
      formData.append('audio', selectedFile)
      formData.append('mode', mode)

      setStatus('processing')
      addLog('连接成功，开始处理音频...', 'success')

      const response = await fetch(apiUrl, {
        method: 'POST',
        body: formData,
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      setStatus('processing')
      addLog('连接成功，开始处理音频...', 'success')

      await processStream(response)
    } catch (error: any) {
      if (error.name === 'AbortError') {
        addLog('识别已被用户停止', 'warning')
        setStatus('idle')
      } else {
        addLog(`识别失败: ${error.message}`, 'danger')
        setStatus('error')
        message.error('识别失败: ' + error.message)
      }
    } finally {
      setIsProcessing(false)
    }
  }

  const processStream = async (response: any) => {
    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let buffer = ''

    try {
      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          addLog('数据流读取完成', 'success')
          break
        }

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || '' // 保留不完整的行

        for (const line of lines) {
          if (line.trim() && line.startsWith('data: ')) {
            try {
              const eventData: StreamEvent = JSON.parse(line.slice(6))
              handleStreamEvent(eventData)
            } catch (e) {
              addLog(`解析事件数据失败: ${(e as Error).message}`, 'danger')
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }
  }

  type StreamEvent = {
    type: string
    progress?: number
    text?: string
    data?: { text: string }
    message?: string
    is_partial?: boolean
  }

  const handleStreamEvent = (eventData: StreamEvent) => {
    console.log('Received event:', eventData)
    const { type, progress, text, data, message } = eventData

    addLog(`收到事件: ${type}`, '')

    switch (type) {
      case 'connected':
        setStatus('processing')
        addLog(message || '已连接到服务器', 'success')
        break

      case 'progress':
        if (typeof progress === 'number') {
          setProgress(progress)
          addLog(`处理进度: ${progress}%`, '')
        }
        break

      case 'text_update':
        if (text) {
          appendText(text)
        }
        break

      case 'final':
        setProgress(100)
        setStatus('completed')
        if (data && data.text) {
          addLog(`最终识别结果: ${data.text}`, 'success')
        }
        break

      case 'error':
        setStatus('error')
        addLog(`识别错误: ${message || '未知错误'}`, 'danger')
        break

      case 'end':
        setStatus('completed')
        addLog('识别完成', 'success')
        break
    }
  }

  const appendText = (text: string) => {
    setResultText(prev => prev + text)
  }

  const stopRecognition = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    addLog('正在停止识别...', 'warning')
  }

  const clearResults = () => {
    setResultText('等待开始识别...')
    setProgress(0)
    setStatus('idle')
    addLog('结果已清空', '')
  }

  const copyResult = async () => {
    if (!resultText || resultText === '等待开始识别...') {
      addLog('识别结果为空，无法复制', 'warning')
      message.warning('识别结果为空')
      return
    }
    copyText(resultText)
  }

  const addLog = (logMessage: string, type: LogType = '') => {
    const timestamp = new Date().toLocaleTimeString()
    const logEntry = {
      value: `[${timestamp}] ${logMessage}`,
      type
    }

    setLogs(prev => [...prev, logEntry])

    // 限制日志条数
    if (logs.length > 50) {
      setLogs(prev => prev.slice(1))
    }
  }

  const uploadProps: UploadProps = {
    name: 'audio',
    multiple: false,
    showUploadList: false,
    beforeUpload: (file: File) => {
      handleFileChange({ file })
      return false
    },
    accept: '.mp3,.m4a,.wav,.amr,.aac,.flac,.ogg,.mpga'
  }

  return (
    <div
      style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        minHeight: '100vh',
        padding: '24px'
      }}
    >
      <Card
        title={
          <div style={{ textAlign: 'center' }}>
            <PlayCircleOutlined style={{ marginRight: 8 }} />
            <span>会议录音知识库</span>
            <div style={{ fontSize: 14, fontWeight: 'normal' }}>
              智能生成会议纪要，支持多种音频格式的实时语音识别,一键下载保存
            </div>
          </div>
        }
        style={{
          maxWidth: 800,
          margin: '0 auto',
          borderRadius: 15,
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
          border: 'none'
        }}
        styles={{
          header: {
            background: 'linear-gradient(45deg, #667eea, #764ba2)',
            color: 'white',
            borderRadius: '15px 15px 0 0',
            padding: '24px'
          }
        }}
      >
        {/* 配置区域 */}
        <div style={{ marginBottom: 24 }}>
          <div style={{ marginBottom: 12 }}>
            <Text type='danger'>
              注：知识库和本地上传仅选一种方式 <br />
              1、如选知识库，则请先维护知识库，上传录音或文本到知识库中后再选取
              <br />
              2、如选上传会议录音，则上传本地的录音文件，最大50M
            </Text>
          </div>
          <Space direction='vertical' style={{ width: '100%' }}>
            {/* <div>
              <Text strong>
                <LinkOutlined style={{ marginRight: 8 }} />
                API 接口地址
              </Text>
              <Input
                value={apiUrl}
                onChange={e => setApiUrl(e.target.value)}
                placeholder='输入流式识别API地址'
                style={{ marginTop: 8 }}
              />
            </div> */}

            {/* <div>
              <Text strong>
                <SettingOutlined style={{ marginRight: 8 }} />
                识别模式
              </Text>
              <Select value={mode} onChange={setMode} style={{ width: '100%', marginTop: 8 }}>
                <Option value='offline'>离线模式 (高精度)</Option>
                <Option value='online'>在线模式 (低延迟)</Option>
                <Option value='2pass'>2pass模式 (推荐)</Option>
              </Select>
            </div> */}
            <div>
              <Text strong>
                <LinkOutlined style={{ marginRight: 8 }} />
                知识库
              </Text>
              <Select
                showSearch
                disabled={selectedFile !== null}
                style={{ width: '100%', marginTop: 8 }}
                filterOption={(input, option) => option?.label.toLowerCase().includes(input.toLowerCase())}
                mode='multiple'
                placeholder='请选择知识库'
                options={cardData.map(x => ({ value: x.id, label: x.libName }))}
                onChange={value => {
                  setPersonalLibs(value)
                }}
              ></Select>
            </div>
          </Space>
        </div>

        {/* 文件上传区域 */}
        <div style={{ marginBottom: 24 }}>
          <Text strong>
            <UploadOutlined style={{ marginRight: 8 }} />
            选择音频文件
          </Text>
          <div
            style={{
              border: '2px dashed #667eea',
              borderRadius: 10,
              padding: 24,
              textAlign: 'center',
              marginTop: 8,
              cursor: 'pointer'
            }}
          >
            <Upload {...uploadProps} disabled={personalLibs.length > 0}>
              <div>
                <CloudUploadOutlined style={{ fontSize: 32, color: '#888', marginBottom: 16 }} />
                <p>点击选择文件或拖拽文件到此处</p>
                <Text type='secondary'>支持 MP3, M4A, WAV, AMR, AAC, FLAC, OGG 格式</Text>
              </div>
            </Upload>
          </div>
          {selectedFile && (
            <div style={{ marginTop: 8 }}>
              <Text type='secondary'>
                <Text>{selectedFile.name}</Text> ({formatFileSize(selectedFile.size)})
                <Link>
                  <DeleteOutlined
                    onClick={() => {
                      clearResults()
                      setSelectedFile(null)
                    }}
                    style={{ marginLeft: 8 }}
                  />
                </Link>
              </Text>
            </div>
          )}
        </div>

        {/* 控制按钮 */}
        <Space style={{ marginBottom: 24 }}>
          <Button
            type='primary'
            icon={<PlayCircleOutlined />}
            onClick={() => {
              clearResults()
              startRecognition()
            }}
            disabled={!selectedFile || isProcessing}
          >
            开始识别
          </Button>
          <Button danger icon={<StopOutlined />} onClick={stopRecognition} disabled={!isProcessing}>
            停止识别
          </Button>
          <Button icon={<DeleteOutlined />} onClick={clearResults}>
            清空结果
          </Button>
          <Button
            type='primary'
            onClick={() => {
              handleStart(personalLibs, resultText.slice(9))
            }}
            disabled={generating || !(personalLibs.length > 0 || (resultText !== '等待开始识别...' && !isProcessing))}
          >
            生成会议纪要
          </Button>
        </Space>

        {selectedFile && (
          <>
            {/* 进度条 */}
            {isProcessing && (
              <div style={{ marginBottom: 24 }}>
                <Text strong>上传进度</Text>
                <Progress
                  percent={progress}
                  status={status === 'error' ? 'exception' : 'normal'}
                  strokeColor={status === 'completed' ? '#52c41a' : '#1890ff'}
                  style={{ marginTop: 8 }}
                />
              </div>
            )}

            {/* 识别结果 */}
            <div
              style={{
                background: '#f8f9fa',
                borderRadius: 10,
                padding: 24,
                marginBottom: 24,
                border: '2px dashed #dee2e6',
                position: 'relative'
              }}
            >
              <div
                style={{
                  position: 'absolute',
                  top: 10,
                  left: 10,
                  padding: '4px 12px',
                  borderRadius: 20,
                  background:
                    statusMap[status]?.color === 'default'
                      ? '#888'
                      : statusMap[status]?.color === 'processing'
                      ? '#1890ff'
                      : statusMap[status]?.color === 'success'
                      ? '#52c41a'
                      : statusMap[status]?.color === 'error'
                      ? '#ff4d4f'
                      : '#888',
                  color: 'white',
                  fontSize: 12,
                  fontWeight: 600
                }}
              >
                {statusMap[status]?.text}
              </div>

              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: 16
                }}
              >
                <Text strong>
                  <CommentOutlined style={{ marginRight: 8 }} />
                  识别结果
                </Text>
                <Button
                  icon={<CopyOutlined />}
                  onClick={copyResult}
                  disabled={!resultText || resultText === '等待开始识别...'}
                >
                  一键复制
                </Button>
              </div>

              <div
                className='scroll-container'
                style={{
                  height: 300,
                  overflowY: 'auto',
                  paddingRight: 10,
                  whiteSpace: 'pre-wrap',
                  wordWrap: 'break-word'
                }}
              >
                {resultText}
              </div>
            </div>

            {/* 日志区域 */}
            <div>
              <Text strong>
                <ConsoleSqlOutlined style={{ marginRight: 8 }} />
                运行日志
              </Text>
              <div
                className='scroll-container'
                style={{
                  background: '#2d3748',
                  color: '#e2e8f0',
                  borderRadius: 10,
                  padding: 16,
                  maxHeight: 200,
                  overflowY: 'auto',
                  fontFamily: 'monospace',
                  fontSize: 14,
                  marginTop: 8
                }}
              >
                {logs.map((log, index) => (
                  <div key={index} style={{ marginBottom: 8 }}>
                    {log.type ? <Text type={log.type}>{log.value}</Text> : log.value}
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </Card>
    </div>
  )
}

export default FunASRStreamTester
