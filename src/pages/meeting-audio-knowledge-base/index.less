.review-material-container {
  height: 100vh;
  background-color: #f0f2f5;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;

  .page-title {
    color: #1f1f1f;
    margin-bottom: 6px;
  }

  .page-description {
    font-size: 14px;
  }
}

.review-material-card {
  width: 100%;
  max-width: 600px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  transition: all 0.3s ease;
  margin: 0 auto;
  flex: 1;
}
