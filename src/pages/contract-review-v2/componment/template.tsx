import React, { useState, useCallback, useRef, useEffect } from 'react'
import { message, Flex, Card, Upload, Button, Typography } from 'antd'
import { uploadFile } from '@/api/template'
import { reviewContrat } from '@/api/reviewContratV2'
import { CheckCircleFilled, InboxOutlined } from '@ant-design/icons'
import './template.less'

const appKey = import.meta.env['VITE_REVIEW_CONTRAT_TOKEN'] || ''
interface TemplateProps {
  file: any
  onUpload: (data: string) => void
  onUploadFile: (data: { name: string; id: string }) => void
  onUploadGenFile: (data: { name: string; id: string }) => void
  setCurrent: (current: number) => void
  generating: boolean
  setGenerating: (generating: boolean) => void
}
export const Template: React.FC<TemplateProps> = ({
  file,
  onUpload,
  onUploadFile,
  onUploadGenFile,
  setCurrent,
  generating,
  setGenerating
}) => {
  const [messageApi, contextHolder] = message.useMessage()
  const [uploadedFile, setUploadedFile] = useState<{ name: string; id: string }>()
  const [uploadedGenFile, setUploadedGenFile] = useState<{ name: string; id: string }>()

  const resValue = useRef('')
  useEffect(() => {
    if (!file) {
      setUploadedFile(undefined)
    }
  }, [file])
  const beforeUpload = (file: File) => {
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)
    resValue.current = ''
    if (['docx'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中'
      })
      setGenerating(true)
      uploadFile(file, appKey).then(async response => {
        setGenerating(false)
        if (response.id) {
          setUploadedFile(response)
          onUploadFile(response)
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
        } else {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件上传失败',
            duration: 1
          })
        }
      })
    } else {
      messageApi.error('目前仅支持.docx类型的文件，请重新上传')
    }
    return false
  }

  const handleGenerationStart = useCallback(async () => {
    if (resValue.current) {
      setCurrent(1)
      return
    }
    resValue.current = ''
    setCurrent(1)
    setGenerating(true)
    let res = ''
    try {
      await reviewContrat(
        {
          type: '模版解析',
          files: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: uploadedFile?.id
            }
          ]
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              resValue.current = res
              onUpload(res)
              setGenerating(false)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }, [uploadedFile])

  const beforeGenUpload = (file: File) => {
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)
    if (['docx'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中'
      })
      uploadFile(file, appKey).then(async response => {
        if (response.id) {
          setUploadedGenFile(response)
          onUploadGenFile(response)
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
        } else {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件上传失败',
            duration: 1
          })
        }
      })
    } else {
      messageApi.error('目前仅支持.docx类型的文件，请重新上传')
    }
    return false
  }

  return (
    <>
      {contextHolder}
      <Flex gap='large'>
        <Card className='contract-diligence-report-form'>
          <Flex vertical gap='4'>
            <Typography.Title level={5}>上传模板</Typography.Title>
            <Upload.Dragger showUploadList={false} multiple={false} beforeUpload={beforeUpload}>
              <p className='ant-upload-drag-icon'>{uploadedFile ? <CheckCircleFilled /> : <InboxOutlined />}</p>
              <p className='ant-upload-text'>{uploadedFile ? uploadedFile.name : '点击或者将文件拖拽到这里上传'}</p>
              <p className='ant-upload-hint'>
                {uploadedFile ? (
                  '点击或者将文件拖拽到这里重新上传'
                ) : (
                  <>
                    <span>在这里上传您的合同模板</span> <br />
                    <span>目前仅支持上传一个文件，支持.docx类型</span>
                  </>
                )}
              </p>
            </Upload.Dragger>
            <Typography.Title level={5} style={{ marginTop: '0.5em' }}>
              上传合同
            </Typography.Title>
            <Upload.Dragger showUploadList={false} multiple={false} beforeUpload={beforeGenUpload}>
              <p className='ant-upload-drag-icon'>{uploadedGenFile ? <CheckCircleFilled /> : <InboxOutlined />}</p>
              <p className='ant-upload-text'>
                {uploadedGenFile ? uploadedGenFile.name : '点击或者将文件拖拽到这里上传'}
              </p>
              <p className='ant-upload-hint'>
                {uploadedGenFile ? (
                  '点击或者将文件拖拽到这里重新上传'
                ) : (
                  <>
                    <span>在这里上传您的合同</span> <br />
                    <span>目前仅支持上传一个文件，支持.docx类型</span>
                  </>
                )}
              </p>
            </Upload.Dragger>
            <Button
              size='large'
              type='primary'
              style={{ marginTop: '0.5em' }}
              disabled={!uploadedGenFile || generating}
              onClick={handleGenerationStart}
            >
              {resValue.current ? '下 一 步' : '开 始 解 析'}
            </Button>
          </Flex>
        </Card>
      </Flex>
    </>
  )
}

export default Template
