import React, { useCallback, useState } from 'react'
import { Input, Typography, Layout, Flex, Spin, Table, Tooltip } from 'antd'
import { documentSummary } from '@/api/announcementInquiry'
import { NoData } from '@/component/NoData'
import './index.less'

const { Content } = Layout

export const AnnouncementInquiry: React.FC = () => {
  const [collapse, setCollapse] = useState<boolean>(false)
  const [generating, setGenerating] = useState<boolean>(false)
  const [messagesEnd, setMessagesEnd] = useState<boolean>(false)
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [messages, setMessages] = useState<any[]>([])

  const handleGeneration = useCallback(
    async (query: string) => {
      if (!query) return
      let accumulatedMessages = ''
      setGenerating(true)
      setMessages([])
      setCollapse(false)
      setMessagesEnd(false)
      setSearchQuery(query)

      try {
        await documentSummary(searchQuery, {
          onMessage: (text, finished) => {
            setGenerating(false)
            if (text) {
              accumulatedMessages += text
            }
            if (finished) {
              try {
                const arr = JSON.parse(accumulatedMessages)
                setMessages(Array.isArray(arr) ? arr : [])
              } catch {
                setMessages([])
              }
              setMessagesEnd(true)
            }
          },
          onError: () => {
            setGenerating(false)
            setMessagesEnd(true)
          },
          onFinish: () => {
            setGenerating(false)
            setMessagesEnd(true)
          },
        })
      } catch (err) {
        setGenerating(false)
        setMessagesEnd(true)
      }
    },
    [searchQuery]
  )

  const handleSearch = () => {
    handleGeneration(searchQuery)
  }

  // 表格列定义
  const columns = [
    {
      title: '公告标题',
      dataIndex: 'title',
      key: 'title',
      // ellipsis: true,
      // render: (text: string) => (
      //   <Tooltip placement="topLeft" title={text}>
      //     {text}
      //   </Tooltip>
      // ),
    },
    {
      title: '公告来源',
      dataIndex: 'source',
      key: 'source',
      width: 220,
      align: 'center' as const,
    },
  ]

  return (
    <div className="announcement-inquiry">
      <Spin tip="加载中" spinning={generating} fullscreen size="large" />
      <Flex className="announcement-inquiry-toolbar toolbar" justify="center">
        <Typography.Text className="title-text">公告查询</Typography.Text>
      </Flex>
      <Content className="announcement-inquiry-content">
        <Flex style={{ marginBottom: '15px' }}>
          <Input.Search
            size="large"
            style={{ backgroundColor: '#fff', borderRadius: '8px' }}
            placeholder="请输入查询内容"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onSearch={handleSearch}
            enterButton="查询"
          />
        </Flex>
        <Flex
          vertical
          className="announcement-inquiry-container scroll-container"
        >
          {Array.isArray(messages) && messages.length > 0 ? (
            <Table
              columns={columns}
              dataSource={messages}
              rowKey={(record) => record.id || record.title}
              pagination={false}
              onRow={(record) => ({
                onClick: () => {
                  if (record.url) {
                    window.open(record.url, '_blank') // 新窗口打开
                  }
                },
                style: { cursor: 'pointer' },
              })}
            />
          ) : (
            <NoData description="未找到匹配的公告信息" />
          )}
        </Flex>
      </Content>
    </div>
  )
}

export default AnnouncementInquiry
