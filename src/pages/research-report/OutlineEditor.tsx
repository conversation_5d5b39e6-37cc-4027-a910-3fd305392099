import React, { useState, useEffect } from 'react'
import { Input, Dropdown } from 'antd'
import type { MenuProps } from 'antd'
import { generateShortId } from '@/utils/common'

interface OutlineItem {
  id: string
  content: string
  lv: number
  title?: boolean
}

interface OutlineEditorProps {
  value: string
  onChange?: (value: string) => void
}

export const OutlineEditor: React.FC<OutlineEditorProps> = ({ value, onChange }) => {
  const [data, setData] = useState<OutlineItem[]>([])
  const [activeItemId, setActiveItemId] = useState<string>('')

  // 解析Markdown为数据结构
  useEffect(() => {
    const lines = value.split('\n')
    const result: OutlineItem[] = []

    for (const line of lines) {
      if (!line.trim()) continue

      const headerMatch = line.match(/^(#+)\s*(.*)/)
      const listMatch = line.match(/^-\s*(.*)/)

      if (headerMatch) {
        const lv = headerMatch[1].length
        const content = headerMatch[2]
        result.push({
          id: generateShortId(),
          content,
          title: true,
          lv
        })
      } else if (listMatch) {
        const content = listMatch[1]
        result.push({
          id: generateShortId(),
          content,
          lv: 4
        })
      } else {
        result.push({
          id: generateShortId(),
          content: line.trim(),
          lv: 4
        })
      }
    }
    setData(result)
  }, [value])

  // 数据变化时生成Markdown
  useEffect(() => {
    let markdown = ''
    const prefixTitle = '#'
    const prefixItem = '-'

    data.forEach((item, index) => {
      if (index > 0 && item.lv !== 1) markdown += '\n'
      if (item.title) {
        markdown += `${prefixTitle.repeat(item.lv)} ${item.content}`
      } else {
        markdown += `${prefixItem} ${item.content}`
      }
    })

    onChange?.(markdown)
  }, [data, onChange])

  const handleFocus = (id: string) => {
    setActiveItemId(id)
  }

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>, item: OutlineItem) => {
    setActiveItemId('')
    const value = e.target.value
    setData(prev => prev.map(_item => (_item.id === item.id ? { ..._item, content: value } : _item)))
  }

  const handleEnter = (e: React.KeyboardEvent<HTMLInputElement>, item: OutlineItem) => {
    if (e.key !== 'Enter') return
    const value = (e.target as HTMLInputElement).value
    if (!value) return

    setActiveItemId('')

    if (!item.title) {
      const index = data.findIndex(_item => _item.id === item.id)
      const newItemId = generateShortId()
      const newData = [...data]
      newData.splice(index + 1, 0, { id: newItemId, content: '', lv: 4 })
      setData(newData)
      setActiveItemId(newItemId)
    }
  }

  const handleBackspace = (e: React.KeyboardEvent<HTMLInputElement>, item: OutlineItem) => {
    if (e.key !== 'Backspace') return
    const value = (e.target as HTMLInputElement).value
    if (!value && !item.title) {
      deleteItem(item.id)
    }
  }

  const addItem = (itemId: string, pos: 'next' | 'prev', content: string) => {
    const index = data.findIndex(_item => _item.id === itemId)
    const item = data[index]
    if (!item) return

    const id = generateShortId()
    let lv = 4
    let insertIndex = pos === 'prev' ? index : index + 1
    let title = false

    if (item.lv === 1) lv = 2
    else if (item.lv === 2) {
      lv = pos === 'prev' ? 2 : 3
    } else if (item.lv === 3) {
      lv = pos === 'prev' ? 3 : 4
    }

    if (lv < 4) title = true

    const newData = [...data]
    newData.splice(insertIndex, 0, { id, content, lv, title })
    setData(newData)
    setActiveItemId(id)
  }

  const deleteItem = (itemId: string, isTitle?: boolean) => {
    if (isTitle) {
      const index = data.findIndex(item => item.id === itemId)
      const targetIds = [itemId]
      const item = data[index]

      for (let i = index + 1; i < data.length; i++) {
        const afterItem = data[i]
        if (afterItem && afterItem.lv > item.lv) {
          targetIds.push(afterItem.id)
        } else break
      }

      setData(prev => prev.filter(item => !targetIds.includes(item.id)))
    } else {
      setData(prev => prev.filter(item => item.id !== itemId))
    }
  }

  const getContextMenu = (item: OutlineItem): MenuProps['items'] => {
    if (item.lv === 1) {
      return [
        {
          key: 'add-child',
          label: '添加子级大纲（章）',
          onClick: () => addItem(item.id, 'next', '新的一章')
        }
      ]
    } else if (item.lv === 2) {
      return [
        {
          key: 'add-sibling-before',
          label: '上方添加同级大纲（章）',
          onClick: () => addItem(item.id, 'prev', '新的一章')
        },
        {
          key: 'add-child',
          label: '添加子级大纲（节）',
          onClick: () => addItem(item.id, 'next', '新的一节')
        },
        { type: 'divider' },
        {
          key: 'delete',
          label: '删除此章',
          onClick: () => deleteItem(item.id, true)
        }
      ]
    } else if (item.lv === 3) {
      return [
        {
          key: 'add-sibling-before',
          label: '上方添加同级大纲（节）',
          onClick: () => addItem(item.id, 'prev', '新的一节')
        },
        {
          key: 'add-child',
          label: '添加子级大纲（项）',
          onClick: () => addItem(item.id, 'next', '新的一项')
        },
        { type: 'divider' },
        {
          key: 'delete',
          label: '删除此节',
          onClick: () => deleteItem(item.id, true)
        }
      ]
    }
    return [
      {
        key: 'add-sibling-before',
        label: '上方添加同级大纲（项）',
        onClick: () => addItem(item.id, 'prev', '新的一项')
      },
      {
        key: 'add-sibling-after',
        label: '下方添加同级大纲（项）',
        onClick: () => addItem(item.id, 'next', '新的一项')
      },
      { type: 'divider' },
      {
        key: 'delete',
        label: '删除此项',
        onClick: () => deleteItem(item.id)
      }
    ]
  }

  const getFlagText = (lv: number) => {
    switch (lv) {
      case 1:
        return '主题'
      case 2:
        return '章'
      case 3:
        return '节'
      default:
        return ''
    }
  }

  return (
    <div className='outline-editor' style={{ padding: '0 10px', paddingLeft: '40px' }}>
      {data.map(item => (
        <Dropdown key={item.id} menu={{ items: getContextMenu(item) }} trigger={['contextMenu']}>
          <div
            className={`item ${item.title ? 'title' : ''} lv-${item.lv}`}
            style={{
              height: 32,
              position: 'relative',
              fontSize: item.lv === 1 ? 22 : item.lv === 2 ? 17 : item.lv === 3 ? 15 : 13,
              fontWeight: item.title ? 700 : 'normal',
              paddingLeft: item.lv === 4 ? 20 : 0
            }}
          >
            {activeItemId === item.id ? (
              <Input
                autoFocus
                value={item.content}
                onChange={e => {
                  setData(prev =>
                    prev.map(_item => (_item.id === item.id ? { ..._item, content: e.target.value } : _item))
                  )
                }}
                onBlur={e => handleBlur(e, item)}
                onKeyDown={e => {
                  handleEnter(e, item)
                  handleBackspace(e, item)
                }}
                style={{ width: '100%' }}
              />
            ) : (
              <div
                className='text'
                onClick={() => handleFocus(item.id)}
                style={{
                  height: '100%',
                  padding: '0 11px',
                  lineHeight: '32px',
                  borderRadius: 4,
                  cursor: 'pointer',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
              >
                {item.content}
              </div>
            )}

            <div
              className='flag'
              style={{
                width: 32,
                height: 32,
                position: 'absolute',
                top: '50%',
                left: -40,
                marginTop: -16,
                zIndex: 1,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}
            >
              <div
                style={{
                  width: '1px',
                  height: '100%',
                  position: 'absolute',
                  left: '50%',
                  backgroundColor: 'rgba(24, 144, 255, 0.1)'
                }}
              />
              {item.lv < 4 && (
                <div
                  style={{
                    width: 32,
                    height: 22,
                    borderRadius: 2,
                    backgroundColor: '#fff',
                    border: '1px solid #1890ff',
                    color: '#1890ff',
                    position: 'relative',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    fontSize: 12,
                    fontWeight: 400
                  }}
                >
                  {getFlagText(item.lv)}
                </div>
              )}
            </div>
          </div>
        </Dropdown>
      ))}
    </div>
  )
}

export default OutlineEditor
