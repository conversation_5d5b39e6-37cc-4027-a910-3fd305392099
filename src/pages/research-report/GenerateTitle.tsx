import React, { useState } from 'react'
import { Upload, Button, message, Card, Typography, Form, Input, Spin } from 'antd'
import { CheckCircleFilled, DeleteOutlined, InboxOutlined } from '@ant-design/icons'
import { researchReport } from '@/api/researchReport'
import { uploadFile } from '@/api/template'
import './index.less'

const appKey = import.meta.env['VITE_RESEARCH_REPORT_TOKEN'] || ''
export interface FileItem {
  id: string
  name: string
}
export const GenerateTitle: React.FC<{
  onStart?: (obj: any) => void
  onFinish?: (message: string) => void
  onFileChange?: (files: FileItem[]) => void
}> = ({ onStart, onFinish, onFileChange }) => {
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<FileItem[]>([])
  const [form] = Form.useForm()

  const beforeUpload = (file: File) => {
    uploadFile(file, appKey).then(async response => {
      if (response.id) {
        setUploadedFiles(prevFiles => {
          const newFiles = [...prevFiles, { id: response.id, name: file.name }]
          onFileChange?.(newFiles)
          return newFiles
        })
        messageApi.open({
          key: 'uploading',
          type: 'success',
          content: '文件上传成功',
          duration: 1
        })
      } else {
        messageApi.open({
          key: 'uploading',
          type: 'error',
          content: '文件上传失败',
          duration: 1
        })
      }
    })
    return false
  }

  const handleStart = async () => {
    form.validateFields().then(() => {
      let params = { ...form.getFieldsValue(), appKey, type: '生成标题' }
      onStart?.(params)
      setGenerating(true)
      messageApi.success('开始生成，请稍候...')
      let res = ''
      try {
        researchReport(params, {
          onMessage: (message: string | null) => {
            if (message) {
              res += message || ''
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {
            onFinish?.(res)
            setGenerating(false)
          }
        })
      } catch (err) {
        setGenerating(false)
      }
    })
  }

  const handleDelete = (fileId: string) => {
    setUploadedFiles(prevFiles => prevFiles.filter(file => file.id !== fileId))
  }

  return (
    <div style={{ height: '100%', paddingTop: '100px' }}>
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      {contextHolder}
      <Card className='research-report-card' title=''>
        <Typography.Title level={2} className='page-title'>
          行研报告生成
        </Typography.Title>

        <Typography.Text type='secondary' className='page-description'>
          您好，我是您的 研报生成 助手， <br />
          您可以在下面输入主题，我们会按照您提供的主题生成标题
        </Typography.Text>

        <Form layout='vertical' form={form}>
          <Form.Item name='theme' rules={[{ required: true, message: '请输入报告主题' }]} label='报告主题'>
            <Input placeholder='请输入主题，例如：人工智能' maxLength={48} />
          </Form.Item>

          <Form.Item label='资料文件'>
            <Upload.Dragger multiple showUploadList={false} beforeUpload={beforeUpload}>
              <div className='ant-upload-drag-icon'>
                {uploadedFiles.length > 0 ? <CheckCircleFilled /> : <InboxOutlined />}
              </div>
              <div className='ant-upload-hint'>
                <span>拖拽文件到此处或点击选择文件上传</span>
                <br />
                <span style={{ fontSize: '12px', color: '#999' }}>
                  建议上传pdf、docx纯文档格式文件、字数过多以及包含表格、图片等情况可能会影响效果。
                </span>
              </div>
            </Upload.Dragger>

            {uploadedFiles.length > 0 && (
              <div className='file-list-contract'>
                {uploadedFiles.map(file => (
                  <div key={file.id} className='file-item'>
                    <span>{file.name}</span>
                    <DeleteOutlined
                      onClick={() => handleDelete(file.id)}
                      style={{ cursor: 'pointer', flex: '0 0 20px' }}
                    />
                  </div>
                ))}
              </div>
            )}
          </Form.Item>
        </Form>

        <Button
          type='primary'
          onClick={handleStart}
          size='large'
          loading={generating}
          block
          style={{ marginTop: '20px' }}
        >
          生成标题
        </Button>
      </Card>
    </div>
  )
}
