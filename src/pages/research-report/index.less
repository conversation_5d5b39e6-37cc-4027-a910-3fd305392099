.research-report-card {
  width: 100%;
  max-width: 600px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  transition: all 0.3s ease;
  flex: 1;
  background-color: #fff;
  margin: 0 auto;

  .page-title {
    margin-bottom: 16px;
    color: #1f1f1f;
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      font-size: 24px;
      color: #1890ff;
    }
  }

  .page-description {
    display: block;
    margin: 16px 0;
    font-size: 14px;
  }
  .markdown-body ol,
  .markdown-body ul {
    padding-left: 20px;
  }

  .ant-upload-drag-icon {
    font-size: 48px;
    color: #1890ff;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  // 文件列表样式
  .file-list-contract {
    margin-top: 16px;
    max-height: 200px;
    overflow-y: auto;
    padding-right: 4px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f5f5;
      border-radius: 3px;
    }

    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: #f8f9fa;
      border-radius: 6px;
      margin-bottom: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: #f0f2f5;
      }

      span {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.outline-container {
  height: 100%;
  overflow: hidden;
  .outline-scroll {
    width: 100vw;
    min-width: 1000px;
    max-width: 1200px;
    padding: 0 24px;
    margin: 24px 24px 0;
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 0px;
    }
  }

  .markdown-body p {
    white-space: pre-line;
  }
}

.research-report-toolbar {
  background: linear-gradient(180deg, rgba(189, 225, 255, 0.4) 0%, rgba(224, 242, 255, 0) 100%);
  border-radius: 0.5rem 0.5rem 0 0;
  padding: 12px 24px;

  .title-text {
    color: transparent;
    background: linear-gradient(116deg, #1888ff 16%, #2f54eb 88%);
    background-clip: text;
    -webkit-background-clip: text;
    user-select: none;
    font-size: 30px;
    font-weight: bold;
  }
}
