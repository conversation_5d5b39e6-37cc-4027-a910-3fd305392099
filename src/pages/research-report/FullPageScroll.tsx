import { Flex, StepProps, Steps, Typography } from 'antd'
import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react'

// 类型定义
interface SectionItem {
  name: string | React.ReactNode
  children: React.ReactNode
  status: StepProps['status']
}

interface FullPageScrollProps {
  sections: SectionItem[]
}

export interface FullPageScrollHandle {
  changeCurrent: (i: number) => void
}

export const FullPageScroll = forwardRef<FullPageScrollHandle, FullPageScrollProps>(({ sections }, ref) => {
  const [currentSection, setCurrentSection] = useState<number>(0)
  const sectionsRef = useRef<(HTMLDivElement | null)[]>([])

  // 导航点击处理
  const handleNavClick = (i: number) => {
    if (sections[i].status === 'finish') {
      setCurrentSection(i)
      sectionsRef.current[i]?.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
      })
    }
  }

  const changeCurrent = (i: number) => {
    setCurrentSection(i)
    sectionsRef.current[i]?.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest'
    })
  }

  useImperativeHandle(ref, () => ({
    changeCurrent
  }))

  const [windowWidth, setWindowWidth] = useState(window.innerWidth)
  const [windowHeight, setWindowHeight] = useState(window.innerHeight)
  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
      setWindowHeight(window.innerHeight)
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  return (
    <div style={{ overflow: 'hidden', height: '100vh' }}>
      <div style={{ position: 'absolute', width: '100%', zIndex: 100, top: 0, background: '#fff', height: '110px' }}>
        <Flex vertical className='research-report-toolbar' justify='center' align='center'>
          <Typography.Title className='title-text'>行业研究报告生成</Typography.Title>
        </Flex>
        <Steps
          style={{ margin: '0 100px', width: 'calc(100% - 200px)' }}
          size='small'
          current={currentSection}
          onChange={handleNavClick}
          items={sections.map(x => ({ status: x.status, title: x.name }))}
        ></Steps>
      </div>

      {/* 内容区块 */}
      {sections.map((section, index) => (
        <div
          key={index}
          ref={el => (sectionsRef.current[index] = el)}
          style={{
            width: `${windowWidth}px`, // 使用当前窗口宽度
            minWidth: `${windowWidth}px`, // 确保最小宽度匹配
            scrollSnapAlign: 'start',
            flexShrink: 0,
            height: `${windowHeight}px`,
            paddingTop: 110
          }}
        >
          {section.children}
        </div>
      ))}
    </div>
  )
})
