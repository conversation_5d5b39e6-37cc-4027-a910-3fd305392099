import React, { useState, useRef } from 'react'
import { Spin, Flex, Button } from 'antd'
import type { StepProps } from 'antd'
import { FullPageScroll, FullPageScrollHandle } from './FullPageScroll'
import { researchReport } from '@/api/researchReport'
import StreamTypewriter from '@/component/StreamTypewriter'
import { getMdContent } from '@/utils/common'
import { GenerateTitle, FileItem } from './GenerateTitle'
import { OutlineEditor } from './OutlineEditor'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import SelectTitle from './SelectTitle'
import './index.less'
import { UndoOutlined } from '@ant-design/icons'

const appKey = import.meta.env['VITE_RESEARCH_REPORT_TOKEN'] || ''

const WeeklyReportMenu: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [generating, setGenerating] = useState(false)
  const [generating1, setGenerating1] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<{ id: string; name: string }[]>([])
  const [title, setTitle] = useState('')
  const [key1, setkey1] = useState(1)
  const [key2, setkey2] = useState(1)
  const [info1, setInfo1] = useState('')
  const [newinfo1, setNewinfo1] = useState('')
  const [info2, setInfo2] = useState('')
  const [searchParams, setSearchParams] = useState('')
  const [options, setOptions] = useState<{ value: string; label: string }[]>([])

  const generateOutline = async (title: string) => {
    setLoading(true)
    setGenerating(true)
    setInfo1('')
    setkey1(k => k + 1)
    let res = ''
    try {
      researchReport(
        {
          appKey,
          type: '生成大纲',
          title
        },
        {
          onMessage: (message: string | null) => {
            if (message) {
              res += message || ''
              setInfo1(p => p + message || '')
            }
          },
          onError: () => {
            setGenerating(false)
            setLoading(false)
          },
          onFinish: () => {
            setGenerating(false)
            setLoading(false)
          }
        }
      )
    } catch (err) {
      setGenerating(false)
      setLoading(false)
    }
  }

  const handleReporting = async () => {
    childRef.current?.changeCurrent(3)
    setGenerating1(true)
    setLoading(true)
    setkey2(k => k + 1)
    setInfo2('')
    let res = ''
    try {
      researchReport(
        {
          appKey,
          type: '生成报告',
          files: uploadedFiles.map(x => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x.id
          })),
          query: newinfo1
        },
        {
          onMessage: (message: string | null) => {
            if (message) {
              res += message || ''
              setInfo2(p => p + message || '')
            }
          },
          onError: () => {
            setGenerating1(false)
            setLoading(false)
          },
          onFinish: () => {
            setGenerating1(false)
            setLoading(false)
          }
        }
      )
    } catch (err) {
      setGenerating1(false)
      setLoading(false)
    }
  }

  const downloadReport = async () => {
    setLoading(true)
    let res = ''
    try {
      researchReport(
        {
          appKey,
          type: '生成文件',
          title: title,
          query: getMdContent(info2) || ''
        },
        {
          onMessage: (message: string | null) => {
            if (message) {
              res += message || ''
            }
          },
          onError: () => {
            setLoading(false)
          },
          onFinish: () => {
            setLoading(false)

            // 提取()中的内容
            const parenthesesContent = res.match(/\((.*?)\)/)
            const parenthesesResult = parenthesesContent ? parenthesesContent[1] : null

            // 提取[]中的内容
            const squareBracketsContent = res.match(/\[(.*?)\]/)
            const squareBracketsResult = squareBracketsContent ? squareBracketsContent[1] : null

            if (parenthesesResult && squareBracketsResult) {
              const link = document.createElement('a')
              link.href = `${parenthesesResult}`
              link.download = `行业研究报告${squareBracketsResult}`
              document.body.appendChild(link)
              link.click()
              link.remove()
            }
          }
        }
      )
    } catch (err) {
      setLoading(false)
    }
  }

  interface SectionItem {
    name: string | React.ReactNode
    children: React.ReactNode
    status: StepProps['status']
  }

  const scrollRef = useRef<HTMLDivElement>(null)
  const scrollRef1 = useRef<HTMLDivElement>(null)
  const handleContentChange = (value: string) => {
    setNewinfo1(value)
  }

  const handleReSearch = async () => {
    setLoading(true)
    let res = ''
    try {
      researchReport(searchParams, {
        onMessage: (message: string | null) => {
          if (message) {
            res += message || ''
          }
        },
        onError: () => {
          setLoading(false)
        },
        onFinish: () => {
          setOptions(
            res
              ?.split(/[\n、]/)
              ?.filter(x => x)
              .map((x, i) => ({ value: String(i), label: x }))
          )
          setLoading(false)
        }
      })
    } catch (err) {
      setLoading(false)
    }
  }

  const back = (i: number) => {
    childRef.current?.changeCurrent(i)
  }

  const sections: SectionItem[] = [
    {
      name: <span>step 1 输入主题</span>,
      children: (
        <GenerateTitle
          onStart={(obj: any) => {
            setOptions([])
            setSearchParams(obj)
          }}
          onFinish={(message: string) => {
            childRef.current?.changeCurrent(1)
            setOptions(
              message
                ?.split(/[\n、]/)
                ?.filter(x => x)
                .map((x, i) => ({ value: String(i), label: x }))
            )
          }}
          onFileChange={(files: FileItem[]) => {
            setUploadedFiles(files)
          }}
        />
      ),
      status: options.length > 0 ? 'finish' : 'process'
    },
    {
      name: <span>step 2 选择标题</span>,
      children: (
        <SelectTitle
          onSelect={(title: string) => {
            setTitle(title)
            generateOutline(title)
            childRef.current?.changeCurrent(2)
          }}
          back={() => {
            childRef.current?.changeCurrent(0)
          }}
          reSearch={handleReSearch}
          options={options}
        />
      ),
      status: title ? 'finish' : 'process'
    },
    {
      name: <span>step 3 生成大纲</span>,
      children: (
        <Flex key={key1} align='center' justify='space-between' vertical className='outline-container'>
          <div className='outline-scroll' ref={scrollRef}>
            {generating ? (
              <StreamTypewriter
                text={info1}
                end={!generating}
                onchange={() => {
                  if (scrollRef.current) {
                    scrollRef.current.scrollTop = scrollRef.current.scrollHeight + 20
                  }
                }}
              />
            ) : (
              <OutlineEditor value={info1} onChange={handleContentChange} />
            )}
          </div>
          <Flex style={{ margin: '30px 0', padding: '4px 12px' }} gap={20}>
            <Button onClick={() => back(1)}>返回上一步</Button>
            <Button
              onClick={() => {
                generateOutline(title)
              }}
            >
              <UndoOutlined />
              换个大纲
            </Button>
            <Button type='primary' onClick={handleReporting}>
              撰写报告
            </Button>
          </Flex>
        </Flex>
      ),
      status: info1 && !generating ? 'finish' : 'process'
    },
    {
      name: <span>step 4 撰写报告</span>,
      children: (
        <Flex key={key2} align='center' justify='space-between' vertical className='outline-container'>
          <div className='outline-scroll' ref={scrollRef1}>
            <StreamTypewriter
              text={info2}
              end={!generating1}
              components={{
                code({ node, inline, className, children, ...props }: any) {
                  const match = /language-(\w+)/.exec(className || '')
                  return match && match[1] === 'markdown' ? (
                    <ReactMarkdown remarkPlugins={[remarkGfm]} rehypePlugins={[rehypeRaw]}>
                      {children}
                    </ReactMarkdown>
                  ) : (
                    <code {...props} className={className}>
                      {children}
                    </code>
                  )
                },
                pre({ node, children, ...props }: any) {
                  return <div {...props}>{children}</div>
                }
              }}
              onchange={() => {
                if (scrollRef1.current) {
                  scrollRef1.current.scrollTop = scrollRef1.current.scrollHeight + 20
                }
              }}
            />
          </div>

          <Flex style={{ margin: '30px 0', padding: '4px 12px' }} gap={20}>
            <Button onClick={() => back(2)}>返回上一步</Button>
            <Button
              onClick={() => {
                handleReporting()
              }}
            >
              重新生成
            </Button>
            <Button type='primary' onClick={downloadReport}>
              下载报告
            </Button>
          </Flex>
        </Flex>
      ),
      status: info2 && !generating1 ? 'finish' : 'process'
    }
  ]

  const childRef = useRef<FullPageScrollHandle>(null)

  return (
    <>
      <Spin tip='加载中' spinning={loading} fullscreen size='large' />
      <FullPageScroll sections={sections} ref={childRef} />
    </>
  )
}

export default WeeklyReportMenu
