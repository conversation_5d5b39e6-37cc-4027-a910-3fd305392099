import React, { useEffect, useState } from 'react'
import { Button, Card, Flex, Form, Input, message, Radio, Typography } from 'antd'
import type { RadioChangeEvent } from 'antd'

const SelectTitle: React.FC<{
  onSelect?: (title: string) => void
  options: { value: string; label: string }[]
  back: () => void
  reSearch: () => void
}> = ({ onSelect, options, back, reSearch }) => {
  const [value, setValue] = useState('')
  const [inputValue, setInputValue] = useState('')
  const [list, setList] = useState<{ value: string; label: string }[]>(options)

  const onChange = (e: RadioChangeEvent) => {
    setValue(e.target.value)
    setInputValue('')
  }
  useEffect(() => {
    setList(options)
  }, [options])

  const handleNext = () => {
    if (!value) {
      message.error('请选择标题')
      return
    }
    let title = ''
    if (value === '999') {
      title = inputValue
    } else {
      title = list[Number(value) - 1].label
    }
    onSelect?.(title)
  }
  return (
    <div style={{ height: '100%', paddingTop: '100px' }}>
      <Card className='research-report-card' title=''>
        <Typography.Title level={2} className='page-title'>
          行研报告生成
        </Typography.Title>

        <Form layout='vertical'>
          <Form.Item
            label={
              <Flex style={{ width: 600 }} align='center' justify='space-between'>
                <span>选择标题</span>
                <Button
                  type='link'
                  onClick={() => {
                    setValue('')
                    reSearch()
                  }}
                >
                  换一换
                </Button>
              </Flex>
            }
          >
            <Radio.Group
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: 8
              }}
              value={value}
              onChange={onChange}
              options={[
                ...list.map(x => ({ value: x.value, label: x.label })),
                {
                  value: '999',
                  label: <>其它...</>
                }
              ]}
            />

            {value === '999' && (
              <Input.TextArea
                value={inputValue}
                onChange={e => setInputValue(e.target.value)}
                variant='filled'
                placeholder='请输入'
                style={{
                  width: 280,
                  marginInlineStart: 12,
                  resize: 'none',
                  height: 80,
                  marginLeft: 20,
                  marginTop: 10
                }}
                showCount
                allowClear
                maxLength={20}
              />
            )}
          </Form.Item>
        </Form>

        <Flex style={{ marginTop: 45 }} gap={20}>
          <Button size='large' onClick={back} block>
            返回上一步
          </Button>
          <Button type='primary' size='large' onClick={handleNext} block>
            生成大纲
          </Button>
        </Flex>
      </Card>
    </div>
  )
}

export default SelectTitle
