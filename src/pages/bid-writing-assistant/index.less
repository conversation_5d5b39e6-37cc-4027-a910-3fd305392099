.toolbar {
  // padding: 20px;
  background-color: #fff;
  // border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.title-text {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  letter-spacing: -0.5px;
}

.main-container {
  display: flex;
  gap: 24px;
  padding: 24px;
  max-width: 1600px;
  margin: 0 auto;
  height: calc(100vh - 80px);
}

.left-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  min-width: 0; // 防止flex子项溢出

  .chat-content {
    flex: 1;
    overflow-y: auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    min-height: 0; // 确保flex: 1生效

    .message-content {
      margin-top: 16px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      height: calc(100% - 40px); // 减去标题高度
      overflow-y: auto;
    }
  }

  .input-area {
    background: #fff;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    height: 72px; // 固定输入区域高度
    .ant-card-body {
      padding: 0;
    }

    .ant-input {
      height: 40px;
      border-radius: 8px;
      font-size: 14px;
      
      &:hover, &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
    }

    // .ant-btn {
    //   height: 40px;
    //   border-radius: 8px;
    // }
  }
}

.right-panel {
  width: 400px;
  background: #fff;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  
  .upload-section {
    height: 100%;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    flex:1;
  }
  .ant-card-body {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
  display: block;
}

.ant-upload-drag {
  padding: 32px;
  border: 2px dashed #e8e8e8;
  border-radius: 12px;
  background: #fafafa;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
    background: #f0f7ff;
  }
}

.ant-upload-drag-icon {
  margin-bottom: 20px;
  font-size: 48px;
  color: #1890ff;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.file-list {
  margin-top: 24px;
  height: 250px;
  overflow-y: auto;
  padding-right: 4px;
  flex: 1; // 让文件列表占据剩余空间

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f5f5;
    border-radius: 3px;
  }

  .file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 8px;
    transition: all 0.3s ease;

    &:hover {
      background: #f0f2f5;
    }

    span {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 12px;
    }
  }
}

.ant-input-textarea {
  textarea {
    border-radius: 8px;
    padding: 12px;
    font-size: 14px;
    
    &:hover, &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }
  }
}

// .ant-btn-primary {
//   height: 44px;
//   border-radius: 8px;
//   font-size: 16px;
//   font-weight: 500;
//   transition: all 0.3s ease;

//   &:hover {
//     transform: translateY(-1px);
//     box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
//   }

//   &:disabled {
//     transform: none;
//     box-shadow: none;
//   }
// }

.text-length-selector {
  margin-top: auto; // 将选择器推到底部
  background: #fff; // 确保背景色与面板一致

  .ant-select {
    .ant-select-selector {
      height: 40px;
      border-radius: 8px;
      padding: 4px 12px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
      }

      .ant-select-selection-item {
        line-height: 32px;
        font-size: 14px;
      }
    }

    &.ant-select-focused {
      .ant-select-selector {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
    }
  }
}
