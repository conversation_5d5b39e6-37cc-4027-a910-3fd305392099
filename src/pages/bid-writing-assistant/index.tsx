import { useCallback, useState, useEffect, useRef } from 'react'
import {
  Upload,
  Button,
  Typography,
  Card,
  Spin,
  Flex,
  Input,
  message,
  Select,
} from 'antd'
import {
  CheckCircleFilled,
  InboxOutlined,
  DeleteOutlined,
} from '@ant-design/icons'

import { bidWritingAssistantContract } from '@/api/bidWritingAssistant'
import { SSEClient } from '@/utils/sse-client'
import { uploadFile, convertFileToPDF } from '@/api/template'
import StreamTypewriter from '@/component/StreamTypewriter'
import './index.less'

const BID_WRITING_ASSISTANT_TOKEN =
  import.meta.env['VITE_BID_WRITING_ASSISTANT_TOKEN'] || ''
export const BidWritingAssistant = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [bidContent, setBidContent] = useState<string>('')
  const [uploadedFiles, setUploadedFiles] = useState<
    { id: string; name: string }[]
  >([])
  const [startSending, setStartSending] = useState<boolean>(false)
  const [openDisable, setOpenDisable] = useState<boolean>(false)
  const [generating, setGenerating] = useState<boolean>(false)
  const [streamingContent, setStreamingContent] = useState<string>('')
  const [messagesEnd, setMessagesEnd] = useState<boolean>(false)
  const [textLength, setTextLength] = useState<'长文' | '短文'>('短文')
  const isStoppedRef = useRef<boolean>(false)
  const sseClientRef = useRef<SSEClient | null>(null)
  const [stop, setStop] = useState(false) // 新增 停止输出 状态

  useEffect(() => {
    if (uploadedFiles.length > 0) {
      messageApi.open({
        key: 'uploading',
        type: 'success',
        content: '文件上传成功',
        duration: 1,
      })
    }
  }, [uploadedFiles, messageApi])

  const beforeUpload = (file: File) => {
    const originalFilename = file.name.substring(0, file.name.lastIndexOf('.'))
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf('.') + 1)
      ?.toLowerCase()
    if (['pdf', 'docx'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中',
      })
      convertFileToPDF(file).then(async (response) => {
        if (response['status'] && response['status'] !== 200) {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件处理异常，请稍后重试',
            duration: 1,
          })
        } else if ('blob' in response) {
          const blob = await response.blob()
          const pdfFile = new File([blob], `${originalFilename}.pdf`, {
            type: 'application/pdf',
          })
          uploadFile(pdfFile, BID_WRITING_ASSISTANT_TOKEN).then(async (response) => {
            if (response.id) {
              setUploadedFiles((prevFiles) => [...prevFiles, response])
              messageApi.open({
                key: 'uploading',
                type: 'success',
                content: '文件上传成功',
                duration: 1,
              })
            } else {
              messageApi.open({
                key: 'uploading',
                type: 'error',
                content: '文件上传失败',
                duration: 1,
              })
            }
          })
        }
      })
    } else {
      messageApi.error(
        '目前仅支持.docx, .pdf类型的文件，请您将文件转成这些格式后再次进行上传'
      )
    }
    return false
  }

  const handleDelete = (fileId: string) => {
    setUploadedFiles((prevFiles) =>
      prevFiles.filter((file) => file.id !== fileId)
    )
  }

  const handleGenerationStart = () => {
    setStartSending(true)
    isStoppedRef.current = false // 重置停止状态
    setStop(false) // 重置 流式输出 状态
    const fileIds = uploadedFiles.map((file) => file.id)
    handleGeneration(fileIds)
  }
  const handleStop = () => {
    isStoppedRef.current = true // 设置停止状态
    setGenerating(false) // 停止加载动画
    setOpenDisable(false) // 允许再次发送
    setStop(true) // 设置 stop 状态为 true，停止前端流式输出
    if (sseClientRef.current) {
      sseClientRef.current.cancel() // 停止后端流式输出
    }
  }

  const handleGeneration = useCallback(
    async (fileIds: string[]) => {
      if (!bidContent.trim()) {
        messageApi.error('请输入内容')
        return
      }

      // if (fileIds.length === 0) {
      //   messageApi.error("请上传至少一个文件");
      //   return;
      // }

      setGenerating(true)
      setOpenDisable(true)
      setMessagesEnd(false)
      setStreamingContent('')
      let accumulatedMessages = ''

      try {
        // 调用 bidWritingAssistantContract 并获取 SSEClient 实例
        const { client, sendMessage } = await bidWritingAssistantContract(
          // await bidWritingAssistantContract(
          {
            query: bidContent,
            files: fileIds.map((x) => ({
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: x,
            })),
            is_long: textLength,
          },
          {
            onMessage: (text: string | null, finished: boolean) => {
              setGenerating(false)
              setBidContent('')

              if (isStoppedRef.current) {
                // setGenerating(false);
                setOpenDisable(false)
                setMessagesEnd(true)
                return // 如果已停止，直接退出回调
              }
              if (text) {
                accumulatedMessages += text
                setStreamingContent(accumulatedMessages)
              }
              if (finished) {
                setOpenDisable(false)
                setMessagesEnd(true)
              }
            },
            onError: () => {
              setGenerating(false)
              setOpenDisable(false)
              setMessagesEnd(true)
              // messageApi.error('生成过程中发生错误')
            },
            onFinish: () => {
              setOpenDisable(false)
            },
          }
        )
        // 存储 SSEClient 实例到 ref 中
        sseClientRef.current = client

        // 等待 sendMessage 完成
        await sendMessage
      } catch {
        setGenerating(false)
        setMessagesEnd(true)
        setOpenDisable(false)
        messageApi.error('生成过程中发生错误')
      }
    },
    [bidContent, messageApi, textLength, isStoppedRef]
  )

  return (
    <>
      {contextHolder}
      <Spin tip="方案生成中..." spinning={generating} fullscreen size="large" />

      <Flex className="toolbar" justify="center">
        <Typography.Text className="title-text">标书写作助手</Typography.Text>
      </Flex>

      <div className="main-container">
        {/* 左侧区域 */}
        <div className="left-panel">
          {/* 对话内容区域 */}
          <Card className="chat-content scroll-container">
            <Typography.Text className="section-title">
              对话预览
            </Typography.Text>
            {startSending && streamingContent && (
              <div className="message-content scroll-container">
                <Flex vertical>
                  <StreamTypewriter
                    text={streamingContent}
                    end={messagesEnd}
                    stop={stop}
                  />
                </Flex>
              </div>
            )}
            {/* {!streamingContent && <NoData description='未找到匹配的文档信息' />} */}
          </Card>

          {/* 输入框区域 */}
          <Card className="input-area">
            <Flex gap="12px">
              <Input
                placeholder="请输入您的信息，例如：请帮我分析这些文件并生成投标书..."
                value={bidContent}
                onChange={(e) => setBidContent(e.target.value)}
                style={{ flex: 1 }}
              />
              <Button
                size="large"
                type="primary"
                disabled={openDisable || !bidContent.trim()}
                onClick={handleGenerationStart}
                style={{ width: '100px' }}
              >
                生成
              </Button>
              <Button
                size="large"
                type="primary"
                onClick={handleStop}
                style={{ width: '100px' }}
              >
                停止
              </Button>
            </Flex>
          </Card>
        </div>

        {/* 右侧区域 */}
        <div className="right-panel">
          <Card className="upload-section">
            <Typography.Text className="section-title">
              上传文档
            </Typography.Text>
            <Upload.Dragger
              multiple
              showUploadList={false}
              beforeUpload={beforeUpload}
              style={{ marginTop: '16px' }}
            >
              <div className="ant-upload-drag-icon">
                {uploadedFiles.length > 0 ? (
                  <CheckCircleFilled />
                ) : (
                  <InboxOutlined />
                )}
              </div>
              <div className="ant-upload-hint">
                <span>拖拽文件到此处上传</span>
                <br />
                <span style={{ fontSize: '12px', color: '#999' }}>
                  或点击选择文件
                </span>
              </div>
            </Upload.Dragger>

            {/* 文件列表 */}
            {uploadedFiles.length > 0 && (
              <div className="file-list">
                <Typography.Text
                  className="section-title"
                  style={{ marginTop: '24px', display: 'block' }}
                >
                  文档列表
                </Typography.Text>
                {uploadedFiles.map((file) => (
                  <div key={file.id} className="file-item">
                    <span>{file.name}</span>
                    <DeleteOutlined
                      onClick={() => handleDelete(file.id)}
                      style={{ color: '#ff4d4f', cursor: 'pointer' }}
                    />
                  </div>
                ))}
              </div>
            )}

            {/* 文本长度选择器 */}
            <div className="text-length-selector">
              <Typography.Text
                className="section-title"
                style={{ marginTop: '0px', display: 'block' }}
              >
                是否长文
              </Typography.Text>
              <Select
                value={textLength}
                onChange={setTextLength}
                style={{ width: '100%', marginTop: '8px' }}
                options={[
                  { value: '长文', label: '长文' },
                  { value: '短文', label: '短文' },
                ]}
              />
            </div>
          </Card>
        </div>
      </div>
    </>
  )
}

export default BidWritingAssistant
