import { useCallback, useState, useEffect } from "react";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import {
  Upload,
  Button,
  Typography,
  Card,
  Spin,
  Flex,
  List,
  Checkbox,
  message,
} from "antd";
import {
  CheckCircleFilled,
  InboxOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import { systemContract } from "@/api/systemVerification";
import { uploadFile } from "@/api/template";
import "./index.less";

const SYSTEM_VERIFICATION_TOKEN = import.meta.env['VITE_SYSTEM_VERIFICATION_TOKEN'] || "";

export const SystemVerification = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const [uploadedFiles, setUploadedFiles] = useState<
    { id: string; name: string }[]
  >([]);
  const [startSending, setStartSending] = useState<boolean>(false);
  const [generating, setGenerating] = useState<boolean>(false);
  const [messages, setMessages] = useState<string>("");
  const [markdownTable, setMarkdownTable] = useState("");
  const [expanded, setExpanded] = useState(false);
  const [checkList, setCheckList] = useState<any[]>([]);

  useEffect(() => {
    if (uploadedFiles.length > 0) {
      messageApi.open({
        key: "uploading",
        type: "success",
        content: "文件上传成功",
        duration: 1,
      });
    }
  }, [uploadedFiles, messageApi]);

  const beforeUpload = (file: File) => {
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf(".") + 1)
      ?.toLowerCase();
    if (["pdf", "docx", "xlsx"].includes(originalFileExt)) {
      messageApi.open({
        key: "uploading",
        type: "loading",
        content: "文件上传中",
      });

      uploadFile(file, SYSTEM_VERIFICATION_TOKEN).then(async (response) => {
        if (response.id) {
          setUploadedFiles((prevFiles) => [...prevFiles, response]);
        } else {
          messageApi.open({
            key: "uploading",
            type: "error",
            content: "文件上传失败",
            duration: 1,
          });
        }
      });
    } else {
      messageApi.error(
        "目前支持.pdf,.docx,.xlsx类型的文件，请您将文件转成这些格式后再次进行上传"
      );
    }
    return false; // 阻止自动上传
  };

  const handleDelete = (fileId: string) => {
    setUploadedFiles((prevFiles) =>
      prevFiles.filter((file) => file.id !== fileId)
    );
  };

  const handleGenerationStart = () => {
    setStartSending(true);
    const fileIds = uploadedFiles.map((file) => file.id);
    handleGeneration(fileIds);
  };

  const handleGeneration = useCallback(
    async (fileIds: string[]) => {
      setGenerating(true);
      let accumulatedMessages = "";
      // 生成 Markdown 格式
      const markdownText = checkList
        .map((id) => {
          const rule = rules.find((r) => r.id === id);
          return rule ? `- **${rule.title}**${rule.status}` : "";
        })
        .join("\n");

      try {
        await systemContract(
          {
            query: markdownText,
            files: fileIds.map((x) => ({
              type: "document",
              transfer_method: "local_file",
              upload_file_id: x,
            })),
          },

          {
            onMessage: (text: string | null, finished: boolean) => {
              if (text) {
                accumulatedMessages += text;
              }
              if (finished) {
                setGenerating(false);
                try {
                  const cleanedData = accumulatedMessages.replace(
                    /^```markdown\s*|```$/g,
                    ""
                  );
                  setMarkdownTable(cleanedData);
                } catch (e) {
                  console.log(accumulatedMessages);
                  setMessages(accumulatedMessages);
                }
              }
            },
            onError: () => {
              setGenerating(false);
            },
            onFinish: () => {
              setGenerating(false);
            },
          }
        );
      } catch (err) {
        setGenerating(false);
      }
    },
    [checkList]
  );
  const { Text } = Typography;

  // 校验规则数据源
  const rules = [
    {
      id: 1,
      title: "应急响应时间",
      status: "未明确具体应急响应时间",
    },
    {
      id: 2,
      title: "预案演练频率",
      status: "每年至少一次",
    },
    {
      id: 3,
      title: "数据保护措施",
      status: "提及容灾备份，但未详细说明",
    },
    {
      id: 4,
      title: "安全事件分类",
      status: "提供详细的分类，包括有害程序事件、网络攻击事件等",
    },
    {
      id: 5,
      title: "责任追究制",
      status: "明确实行责任追究制",
    },
    {
      id: 6,
      title: "预案修订频率",
      status: "每年评估一次，适时修订",
    },
    {
      id: 7,
      title: "日常管理措施",
      status: "提及网络安全检查、隐患排查等",
    },
    {
      id: 8,
      title: "培训与宣传",
      status: "提及应急知识培训和宣传活动",
    },
    {
      id: 9,
      title: "重要活动期间预防措施",
      status: "提及加强监测和分析研判",
    },
  ];

  // 处理复选框变化
  const handleCheck = (e: CheckboxChangeEvent, item: any) => {
    setCheckList((prev) => {
      if (e.target.checked) {
        // 如果选中，添加到列表
        return [...prev, item.id];
      } else {
        // 取消选中，移除
        return prev.filter((id) => id !== item.id);
      }
    });
  };

  // 展开/收起切换
  const toggleExpand = () => {
    setExpanded(!expanded);
  };
  const extra = (
    <Button type="link" onClick={toggleExpand}>
      {expanded ? "展开" : "收起"}
    </Button>
  );
  return (
    <>
      {contextHolder}
      <Spin tip="加载中" spinning={generating} fullscreen size="large" />

      <Flex className="toolbar" justify="center">
        <Typography.Text className="title-text">制度校验系统</Typography.Text>
      </Flex>

      <div
        style={{
          width: "1200px",
          overflow: "hidden",
          padding: "0 20px",
          margin: "0px auto",
        }}
      >
        <Card>
          <Flex vertical gap="middle">
            <Card
              bordered={false}
              style={{
                borderRadius: 8,
                boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
              }}
            >
              <Upload.Dragger
                multiple
                showUploadList={false}
                beforeUpload={beforeUpload}
              >
                <div className="ant-upload-drag-icon">
                  {uploadedFiles.length > 0 ? (
                    <CheckCircleFilled />
                  ) : (
                    <InboxOutlined />
                  )}
                </div>
                <div className="ant-upload-hint">
                  {uploadedFiles.length > 0 ? (
                    "点击或者将文件拖拽到这里重新上传"
                  ) : (
                    <span>在这里上传您的文件，让AI帮您进行解析</span>
                  )}
                </div>
                <div className="ant-upload-text">
                  {uploadedFiles.length > 0
                    ? uploadedFiles.map((file, index) => (
                        <div
                          key={file.id}
                          style={{ display: "flex", alignItems: "center" }}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Button type="link">
                            {index + 1 + "." + file.name}
                            <DeleteOutlined
                              onClick={() => handleDelete(file.id)}
                            />
                          </Button>
                        </div>
                      ))
                    : "点击或者将文件拖拽到这里进行上传"}
                </div>
              </Upload.Dragger>
            </Card>
            <Card
              title="校验规则"
              extra={extra}
              bordered={false}
              style={{
                height: expanded ? "55px" : "auto",
                overflow: expanded ? "hidden" : "auto",
                borderRadius: 8,
                boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
              }}
            >
              <List
                dataSource={rules}
                renderItem={(item) => (
                  <List.Item style={{ padding: "12px 0" }}>
                    <div
                      style={{
                        display: "flex",
                        width: "100%",
                        alignItems: "flex-start",
                      }}
                    >
                      {/* 左侧复选框 */}
                      <Checkbox
                        onChange={(e) => handleCheck(e, item)}
                        style={{
                          marginRight: 16,
                          marginTop: 4,
                          accentColor: "#52c41a", // Antd绿色
                        }}
                      />

                      {/* 右侧内容 */}
                      <div style={{ flex: 1 }}>
                        {/* 规则标题 */}
                        <Text strong style={{ fontSize: 14 }}>
                          {item.title}
                        </Text>

                        {/* 制度现状 */}
                        <div>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            制度现状：{item.status}
                          </Text>
                        </div>
                      </div>
                    </div>
                  </List.Item>
                )}
                split={false}
              />
            </Card>
            <Button
              size="large"
              type="primary"
              disabled={uploadedFiles.length === 0 || generating}
              onClick={handleGenerationStart}
            >
              开 始 校 验
            </Button>
          </Flex>
        </Card>

        {startSending && (
          <Flex
            align="center"
            justify="center"
            style={{ marginTop: 15, overflow: "auto", width: "100%" }}
          >
            {markdownTable ? (
              <Card style={{ width: "100%" }}>
                <ReactMarkdown
                  className="markdown-body"
                  remarkPlugins={[remarkGfm]}
                >
                  {markdownTable}
                </ReactMarkdown>
              </Card>
            ) : (
              <Flex justify="justify" align="center" vertical>
                <Typography.Text>
                  {messages || "正在提取文件信息，请不要关闭或刷新页面"}
                </Typography.Text>
              </Flex>
            )}
          </Flex>
        )}
      </div>
    </>
  );
};

export default SystemVerification;
