/** 模板生成工具 */
import {Button, Card, Flex, message, Space, Spin, Typography, Upload} from 'antd'
import {useCallback, useEffect, useRef, useState} from 'react'
import {structGeneration, uploadFile} from '@/api/template.ts'
import {CheckCircleFilled, DownloadOutlined, InboxOutlined, LoadingOutlined} from '@ant-design/icons'
import * as XLSX from 'xlsx';
import ReactMarkdown from 'react-markdown'
import RemarkMath from 'remark-math'
import RemarkGfm from 'remark-gfm'
import RemarkBreaks from 'remark-breaks'
import {Prism as SyntaxHighlighter} from 'react-syntax-highlighter'
import {dark} from 'react-syntax-highlighter/dist/esm/styles/prism'
import {extractContent} from '@/utils/common'
import './index.less'

const AGENT_TOKEN = import.meta.env['VITE_RAGAS_TOKEN'] || ''


export const Ragas = () => {
  const [open, setOpen] = useState(false)
  const [messageApi, contextHolder] = message.useMessage()
  // FIXME 这个装填应该提交到全局状态库
  const [globalLoading, setGlobalLoading] = useState<boolean>(false)

  // 点击【开始生成】后，状态变更
  const [startSending, setStartSending] = useState<boolean>(false)

  // 生成状态
  const [generating, setGenerating] = useState<boolean>(false)
  // 生成消息
  const [messages, setMessages] = useState<string>('')
  // 生成异常
  const [error, setError] = useState<string | null>(null)
  const [generationMetadata, setGenerationMetadata] = useState<object>()

  const [uploadedFile, setUploadedFile] = useState<object>()

  // 用于存储完整的响应文本
  const fullContentRef = useRef<string>('')
  // 用于控制动画帧
  const animationFrameRef = useRef<number>()
  // 用于跟踪当前显示的字符位置
  const currentIndexRef = useRef<number>(0)


  useEffect(() => {
    return cleanup
  }, [])

  // 清理函数
  const cleanup = () => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current)
    }
    fullContentRef.current = ''
    currentIndexRef.current = 0
  }

  const smoothRender = useCallback(() => {
    const renderNextChunk = () => {
      if (currentIndexRef.current < fullContentRef.current.length) {
        // 每次渲染多个字符以提高性能，同时保持流畅性
        const chunkSize = 2
        const nextIndex = Math.min(currentIndexRef.current + chunkSize, fullContentRef.current.length)

        setMessages(fullContentRef.current.slice(0, nextIndex))
        currentIndexRef.current = nextIndex

        // 继续下一帧渲染
        animationFrameRef.current = requestAnimationFrame(renderNextChunk)
      }
    }

    renderNextChunk()
  }, [])

  // 处理新收到的文本
  const handleNewText = useCallback(
    (text: string) => {
      fullContentRef.current += text
      // 如果当前没有动画在进行，启动新的渲染
      if (!animationFrameRef.current) {
        smoothRender()
      }
    },
    [smoothRender]
  )

  const handleGeneration = useCallback(
    async (fileId: string) => {
      setGenerating(true)
      setError(null)
      setMessages('')
      cleanup()
      let str = ''
      try {
        await structGeneration(
          fileId,
          {},
          {
            onMessage: (text, finished) => {
              if (text) {
                handleNewText(text)
                setMessages(prev => prev + text)
                str += text
              }
              if (finished) {
                setGenerating(false)
                const errorStr = extractContent(str, 'error')
                if (errorStr) {
                  message.error(errorStr)
                  setOpen(true)
                  return
                }
              }
            },
            onError: error => {
              setError(error.message)
              setGenerating(false)
              cleanup()
            },
            onFinish: (data: any) => {
              setGenerationMetadata(data.metadata)
            }
          },
          AGENT_TOKEN
        )
      } catch (err) {
        setError(err instanceof Error ? err.message : '生成过程中发生错误')
        setGenerating(false)
        cleanup()
      }
    },
    [handleNewText]
  )

  const beforeUpload = (file: File) => {
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)
    if (['xlsx'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中'
      })
      uploadFile(file, AGENT_TOKEN).then(async response => {
        if (response.id) {
          setUploadedFile(response)
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
        } else {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件上传失败',
            duration: 1
          })
        }
      })
    } else {
      messageApi.error('目前仅支持.xlsx类型的文件，请您将文件转成这些格式后再次进行上传')
    }
  }

  const handleGenerationStart = () => {
    setStartSending(true)
    setGenerationMetadata(null)
    handleGeneration(uploadedFile!.id)
  }


  const markdownComponents = {
    code({inline, className, children, ...props}) {
      const match = /language-(\w+)/.exec(className || '')
      return !inline && match ? (
        <SyntaxHighlighter
          {...props}
          className='editor custom-scrollbar'
          language={match?.[1]}
          showLineNumbers={true}
          wrapLines={true}
          style={dark}
          customStyle={{
            border: 'none',
            margin: '0'
          }}
          children={String(children).replace(/\n$/, '')}
        ></SyntaxHighlighter>
      ) : (
        <code {...props} className={className}>
          {children}
        </code>
      )
    }
  }
  const downloadExcelTemplate = () => {
    // 创建工作簿
    const wb = XLSX.utils.book_new();

    // 定义列头
    const headers = ['提问', '真实上下文', 'AI回答', '相关上下文'];

    // 创建工作表数据（仅包含表头）
    const wsData = [headers];

    // 创建工作表
    const ws = XLSX.utils.aoa_to_sheet(wsData);

    // 调整列宽
    ws['!cols'] = [
      {wch: 30}, // 提问列
      {wch: 40}, // 真实上下文列
      {wch: 40}, // AI回答列
      {wch: 40}  // 相关上下文列
    ];

    // 将工作表添加到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '问答模板');

    // 导出Excel文件
    XLSX.writeFile(wb, '问答模板.xlsx');
  };
  return (
    <>
      {contextHolder}
      <Spin tip='加载中' spinning={globalLoading} fullscreen size='large'/>
      <Flex className='toolbar' justify='space-between'>
        <Typography.Text className='title-text'>RAG 数据评估</Typography.Text>
        <Space></Space>
      </Flex>
      <Flex className='content-wrapper' gap='large'>
        <Card className='template-form'>
          <Flex vertical gap='middle'>
            <Upload.Dragger showUploadList={false} multiple={false} beforeUpload={beforeUpload}>
              <p className='ant-upload-drag-icon'>{uploadedFile ? <CheckCircleFilled/> : <InboxOutlined/>}</p>
              <p className='ant-upload-text'>{uploadedFile ? uploadedFile.name : '点击或者将文件拖拽到这里进行上传'}</p>
              <p className='ant-upload-hint'>
                {uploadedFile ? (
                  '点击或者将文件拖拽到这里重新上传'
                ) : (
                  <>
                    <p>在这里上传您的文件，让AI帮您进行解析</p>
                    <p>目前仅支持上传一个文件，支持.xlsx格式类型</p>
                  </>
                )}
              </p>
            </Upload.Dragger>
            <Button
              size='large'
              type='primary'
              disabled={!uploadedFile || generating}
              onClick={handleGenerationStart}
            >
              开 始 AI 提 取
            </Button>
            <Button
              onClick={downloadExcelTemplate}
            >
              <DownloadOutlined size={16}/>
              下载Excel模板
            </Button>
          </Flex>
        </Card>

        {startSending && (
          <Flex className='preview-panel' vertical gap='middle'>

            <Card className='preview-content custom-scrollbar'>
              {messages ? (
                <ReactMarkdown
                  className='markdown-body custom-scrollbar'
                  remarkPlugins={[[RemarkMath], RemarkGfm, RemarkBreaks]}
                  components={markdownComponents}
                >
                  {messages}
                </ReactMarkdown>
              ) : (
                <>
                  <LoadingOutlined style={{fontSize: 48, marginBottom: 24}}/>
                  <Typography.Text>正在提取文件信息，请不要关闭或刷新页面</Typography.Text>
                </>
              )}
            </Card>
          </Flex>
        )}
      </Flex>
    </>
  )
}

export default Ragas
