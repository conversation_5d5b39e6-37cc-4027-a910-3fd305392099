.main-container {
  margin: 0 20px;
  padding: 0 30px 16px 30px;
  flex-direction: row;

  .aside {
    .aside-header {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      text-align: center;
      position: relative;
      img {
        width: 12px;
        height: 12px;
      }

      .aside-header-normal {
        width: 100%;
        display: flex;
        justify-content: space-between !important;
      }

      .change-company {
        display: none;
      }
      .show-change-company {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 160px;
        z-index: 10;
      }

      .aside-header-right {
        cursor: pointer;
        &:hover {
          color: #1677ff;
        }
      }

      .search-history {
        display: none;
      }
      .show-search-history {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 40px;
        left: 0;
        width: 100%;
        background-color: #fff;
        height: 300px;
        box-shadow: 0 0 10px #ccc;
        padding: 16px 20px;

        .card-header {
          color: #ccc;
        }
        .card-contain {
          margin-top: 20px;
          overflow: auto;
        }
      }
    }

    .aside-middle {
      width: 300px;
      height: 600px;
      display: flex;
      flex-direction: column;
      position: relative;

      .only-data {
        width: 100%;
        position: absolute;
        left: 0;
        top: 0;
        .aside-middle-num {
          margin-top: 100px;
          display: flex;
          justify-content: center;
          .span-num {
            font-size: 50px;
            color: #ff9036;
          }
          .span-text {
            font-size: 12px;
            color: #5d657d;
          }
        }
        .only-text {
          width: 100%;
          text-align: center;
          font-size: 14px;
          color: #5d657d;
          letter-spacing: 0;
          line-height: 14px;
          font-weight: 400;
          margin-top: 4px;
        }
  
        .more-precise {
          font-size: 12px;
          color: #637698;
          font-weight: 400;
          margin-bottom: 10px;
          cursor: pointer;
          width: 100%;
          text-align: center;
          margin-top: 30px;
        }
        .more-precise-button {
          width: 70%;
          margin: 0 auto;
        }
        z-index: 10;
      }



      .video-play {
        width: 100%;
        position: absolute;
        left: 0;
        top: 0;
        // height: 300px;
      }
    }
  }
  .right-container {
    flex: 1;
  }
}

.videoClass {
  width: 100%;
  // height: 200px;
}
.video-js.vjs-paused .vjs-big-play-button {
  display: none !important;
}
