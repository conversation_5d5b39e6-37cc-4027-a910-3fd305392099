import { useCallback, useState, useEffect, useMemo, useRef } from "react";
import { useLocation, useSearchParams } from "react-router-dom";
import {
  Button,
  Typography,
  Card,
  Spin,
  Flex,
  Input,
  message,
  Modal,
  Form,
  Col,
  Row
} from "antd";
import "./index.less";
import {
  SearchOutlined,
  DeleteOutlined,
  RightOutlined,
} from "@ant-design/icons";

export const PolicyMatchDetails = () => {
  const { search } = useLocation();
  // const searchParams = useMemo(() => new URLSearchParams(search), [search])
  // const key = searchParams.get('key')

  const videoRef = useRef(null);

  const [videoStyle, setVideoStyle] = useState<boolean>(true);

  const [companyInputValue, setcompanyInputValue] = useState<string>("");

  const [messageApi, contextHolder] = message.useMessage();

  const [generating, setGenerating] = useState<boolean>(false);

  const [listdata, setListdata] = useState<any>([
    1, 2, 3, 5, 6, 7, 4, 54, 45, 23, 124,
  ]);

  const [changeCompanySearch, setchangeCompanySearch] =
    useState<boolean>(false);

    const [open, setOpen] = useState(false);
    const [confirmLoading, setConfirmLoading] = useState(false);

  const [params] = useSearchParams();
  const id = params.get("id");

  // input输入框信息变化，调接口时时查看之前的信息
  const handleInputChange = (event: any) => {
    setcompanyInputValue(event.target.value.trim());
    // 查询匹配的企业信息名称,还没有做。。。。
  };

  const handleCompanySubmit = (companyName: string) => {
    if (!companyInputValue) {
      messageApi.error("请输入企业名称");
      return;
    }
    setchangeCompanySearch(false);
    console.log(companyName);
  };

  const delCompanyHistory = () => {
    setListdata([]);
  };

  // 企业信息表单

  const openCompanyInfoModal = () => {
    setOpen(true)
  }
  const handleCompanyInfoSubmit = () => {
    console.log('OK');
  }
  const handleCompanyInfoCancel = () => {
    setOpen(false)
  }

  return (
    <>
      {contextHolder}
      <Spin tip="加载中" spinning={generating} fullscreen size="large" />
      <Flex className="toolbar" justify="center">
        <Typography.Text className="title-text"></Typography.Text>
      </Flex>
      <Flex className="main-container" vertical>
        <Card style={{ width: "350px" }} className="aside">
          {/* <div> */}
          {/* 头部搜索 */}
          <div className="aside-header">
            <div
              className={`${
                !changeCompanySearch ? "aside-header-normal" : "change-company"
              }`}
            >
              <div>
                <span>
                  <img src="" alt="" />
                </span>
                <span>北京神州新桥科技有限公司</span>
              </div>

              <div
                className="aside-header-right"
                onClick={() => {
                  setchangeCompanySearch(true);
                }}
              >
                [切换公司]
              </div>
            </div>

            {/* 点击切换公司时 */}
            <div
              className={`${
                changeCompanySearch ? "show-change-company" : "change-company"
              }`}
            >
              <Input
                width="100%"
                addonBefore={
                  <SearchOutlined
                    onClick={() => {
                      handleCompanySubmit(companyInputValue);
                    }}
                  />
                }
                placeholder="请输入企业名称，匹配可申领补贴"
                value={companyInputValue}
                onChange={handleInputChange}
              />

              <div
                className={`search-history ${
                  companyInputValue &&
                  listdata.length > 0 &&
                  "show-search-history"
                } `}
              >
                <Flex justify="space-between" className="card-header">
                  <div>匹配历史</div>
                  <div>
                    <DeleteOutlined onClick={delCompanyHistory} />
                  </div>
                </Flex>
                <div className="card-contain">
                  {listdata.map((item) => {
                    return (
                      <Flex justify="space-between" key={item}>
                        <div>公司名称</div>
                        <div
                          style={{
                            color: "#1677ff",
                            marginBottom: "16px",
                            cursor: "pointer",
                          }}
                          onClick={() => {
                            handleCompanySubmit(item);
                          }}
                        >
                          立即匹配 <RightOutlined />
                        </div>
                      </Flex>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* 中部数据 */}
          <div className="aside-middle">
            <div className="only-data">
              <div className="aside-middle-num">
                <div className="num">
                  <span className="span-num">8</span>
                  <span className="span-text">项</span>
                </div>
              </div>
              <div className="only-text">补贴可领</div>

              <div className="more-precise">
                <span>
                  完善企业信息，更新更精准 <RightOutlined />
                </span>
              </div>
                <div className="more-precise-button">
                <Button type="primary" size="large" block onClick={() => {openCompanyInfoModal()}}> 完善企业信息</Button>
                </div>
              
            </div>

            {/* mp4 */}
            <div className="video-play">
              <video
                autoPlay={videoStyle}
                muted={videoStyle}
                loop={videoStyle}
                ref={videoRef}
                controls
                id="side-video-4"
                className="videoClass"
              >
                <source
                  src="https://static.qizhidao.com/file-resource/prod/qzd-zhengce-pc/video/match-result/part4-1215.mp4"
                  type="video/mp4"
                />
              </video>
              
            </div>
          </div>

          {/* </div> */}
        </Card>
        <Card className="right-container">1</Card>



        {/* 完善企业信息 */}
        <Modal
        title="完善企业信息"
        width={'80%'}
        open={open}
        onOk={handleCompanyInfoSubmit}
        confirmLoading={confirmLoading}
        onCancel={handleCompanyInfoCancel}
      >
        <Form>

        <Row gutter={16}>
      <Col className="gutter-row" span={12}>
      <div >col-6</div>
      </Col>
      <Col className="gutter-row" span={12}>
        <div >col-6</div>
      </Col>

    </Row>

        </Form>
      </Modal>
      </Flex>
    </>
  );
};

export default PolicyMatchDetails;
