import React, { useState } from 'react'
import { Upload, Button, Form, message, Card, Typography, Spin, Layout, Flex } from 'antd'
import { UploadOutlined, FileTextOutlined, DownloadOutlined } from '@ant-design/icons'
import { layoutRecognition } from '@/api/layoutRecognition'
import { convertFileToPDF, uploadFile } from '@/api/template'
import StreamTypewriter from '@/component/StreamTypewriter'
import { saveAs } from 'file-saver'
import MarkdownIt from 'markdown-it'
import { Document, Packer, Paragraph, TextRun, HeadingLevel, TableCell, TableRow, Table, WidthType } from 'docx'
import jsPDF from 'jspdf'
import './index.less'

const { Title, Text } = Typography
const appkey = import.meta.env['VITE_LAYOUT_RECOGNITION_TOKEN'] || ''
interface FileState {
  file: File | null
  name: string
  id: string | number
}

export const LayoutRecognition: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState(false)
  const [mk, setMk] = useState('')
  const [count, setCount] = useState(0)
  const [isShowMk, setIsShowMk] = useState(false)
  const [templateFile, setTemplateFile] = useState<FileState>({ file: null, name: '', id: '' })
  const scrollRef = React.useRef<HTMLDivElement>(null)
  // 转换为PDF
  const convertToPDF = async (file: File) => {
    try {
      const doc = new jsPDF()
      let positionY = 10 // 初始Y坐标
      const margin = 10 // 页边距
      const maxWidth = doc.internal.pageSize.getWidth() - 2 * margin

      // 创建图片对象
      const img = new Image()
      img.src = URL.createObjectURL(file)

      // 等待图片加载
      await new Promise(resolve => {
        img.onload = resolve
      })

      // 计算图片在PDF中的高度
      const imgHeight = (img.height * maxWidth) / img.width

      // 检查是否需要添加新页
      if (positionY + imgHeight > doc.internal.pageSize.getHeight() - margin) {
        doc.addPage()
        positionY = margin
      }

      // 添加图片到PDF
      doc.addImage(img, 'JPEG', margin, positionY, maxWidth, imgHeight)

      // 更新Y坐标
      positionY += imgHeight + 10

      // 释放对象URL
      URL.revokeObjectURL(img.src)

      // doc.save('converted.pdf')

      // 获取doc文件对象
      const pdfBlob = await doc.output('blob')

      return pdfBlob
    } catch (error) {
      console.error('转换失败:', error)
    }
  }

  const beforeUpload = async (file: File) => {
    messageApi.open({
      key: 'uploading',
      type: 'loading',
      content: '文件上传中'
    })
    const originalFilename = file.name.substring(0, file.name.lastIndexOf('.'))
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)?.toLowerCase()
    setGenerating(true)
    if (['png', 'jpg', 'jpeg'].includes(originalFileExt)) {
      let blob = await convertToPDF(file)
      handleUploadFile(blob, originalFilename, file)
    } else if (['pdf', 'docx', 'xlsx', 'xls'].includes(originalFileExt)) {
      convertFileToPDF(file).then(async response => {
        if (response['status'] && response['status'] !== 200) {
          setGenerating(false)
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件处理异常，请稍后重试',
            duration: 1
          })
        } else if ('blob' in response) {
          const blob = await response.blob()
          handleUploadFile(blob, originalFilename, file)
        }
      })
    }
    return false // 阻止自动上传
  }

  const handleUploadFile = async (blob: any, originalFilename: string, file: any) => {
    const pdfFile = new File([blob], `${originalFilename}.pdf`, {
      type: 'application/pdf'
    })
    uploadFile(pdfFile, appkey).then(async response => {
      setGenerating(false)
      if (response.id) {
        setTemplateFile({
          file: file,
          name: file.name,
          id: response.id
        })
        messageApi.open({
          key: 'uploading',
          type: 'success',
          content: '文件上传成功',
          duration: 1
        })
      } else {
        messageApi.open({
          key: 'uploading',
          type: 'error',
          content: '文件上传失败',
          duration: 1
        })
      }
    })
  }

  const handleDownload = async () => {
    if (!templateFile.id) {
      messageApi.error('请先上传文件')
      return
    }
    setIsShowMk(true)
    setGenerating(true)
    setCount(p => p + 1)
    setMk('')
    messageApi.success('正在生成内容，请稍候...')
    let res = ''
    try {
      await layoutRecognition(
        {
          files: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: templateFile.id
            }
          ]
        },
        {
          onMessage: (message: string | null) => {
            if (message) {
              res += message || ''
              setMk(p => p + message || '')
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {
            setGenerating(false)
          }
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }
  const handleExport = () => {
    try {
      const md = new MarkdownIt({ html: true })
      const html = md.render(mk)

      // 解析 HTML 并转换为 docx 格式的段落
      const parser = new DOMParser()
      const docEl = parser.parseFromString(html, 'text/html').body
      const paragraphs: Paragraph[] = []

      // 遍历 HTML 节点并转换为 docx 段落
      docEl.childNodes.forEach(node => {
        if (node.nodeType === Node.TEXT_NODE && node.textContent) {
          // 纯文本
          paragraphs.push(
            new Paragraph({
              children: [new TextRun(node.textContent)]
            })
          )
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement
          switch (element.tagName.toLowerCase()) {
            case 'html':
              // 如果需要处理整个 HTML 文档，可以在这里添加逻辑
              paragraphs.push(
                new Paragraph({
                  children: [new TextRun('HTML Document Start')]
                })
              )
              break
            case 'body':
              // 如果需要处理 body 标签内容，可以在这里添加逻辑
              paragraphs.push(
                new Paragraph({
                  children: [new TextRun('Body Content Start')]
                })
              )
              break
            case 'h1':
              paragraphs.push(
                new Paragraph({
                  heading: HeadingLevel.HEADING_1,
                  children: [new TextRun(element.textContent || '')]
                })
              )
              break
            case 'h2':
              paragraphs.push(
                new Paragraph({
                  heading: HeadingLevel.HEADING_2,
                  children: [new TextRun(element.textContent || '')]
                })
              )
              break
            case 'p':
              paragraphs.push(
                new Paragraph({
                  children: [new TextRun(element.textContent || '')]
                })
              )
              break
            case 'ul':
              element.querySelectorAll('li').forEach(li => {
                paragraphs.push(
                  new Paragraph({
                    children: [
                      new TextRun({
                        text: `• ${li.textContent || ''}`,
                        bold: li.innerHTML.includes('<strong>')
                      })
                    ],
                    indent: { left: 720 }
                  })
                )
              })
              break
            case 'table':
              const rows = element.querySelectorAll('tr')
              const tableRows: TableRow[] = []

              rows.forEach(row => {
                const cells = Array.from(row.cells)
                const tableCells: TableCell[] = []

                cells.forEach(cell => {
                  const colspan = parseInt(cell.getAttribute('colspan') || '1', 10)
                  const rowspan = parseInt(cell.getAttribute('rowspan') || '1', 10)
                  const cellText = cell.textContent?.trim() || ''

                  // 创建表格单元格
                  tableCells.push(
                    new TableCell({
                      children: [new Paragraph(cellText)],
                      columnSpan: colspan > 1 ? colspan : undefined,
                      rowSpan: rowspan > 1 ? rowspan : undefined
                    })
                  )
                })

                // 创建表格行
                tableRows.push(new TableRow({ children: tableCells }))
              })

              // 创建表格并添加到文档
              const table = new Table({
                rows: tableRows,
                width: { size: 100, type: WidthType.PERCENTAGE } // 设置表格宽度为 100%
              })

              paragraphs.push(table as any)
              break
            default:
              paragraphs.push(
                new Paragraph({
                  children: [new TextRun(element.textContent || '')]
                })
              )
          }
        }
      })

      // 创建 Word 文档
      const doc = new Document({
        styles: {
          paragraphStyles: [
            {
              id: 'Heading1',
              name: 'Heading 1',
              run: {
                size: 32,
                bold: true,
                color: '2e6c80'
              }
            }
          ]
        },
        sections: [{ children: paragraphs }]
      })

      // 导出 Word
      Packer.toBlob(doc).then(blob => {
        saveAs(blob, '导出内容1.docx')
      })
    } catch (error) {
      console.error('导出失败:', error)
    }
  }

  return (
    <div className='layout-recognition-container'>
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      {contextHolder}
      <Layout style={{ height: '100%' }}>
        <Layout.Sider width={isShowMk ? '600px' : '100%'} style={{ backgroundColor: '#f0f2f5', padding: '0 20px' }}>
          <Card className='layout-recognition-card'>
            <Title level={2} className='page-title'>
              <FileTextOutlined /> 版面识别
            </Title>

            <Form layout='vertical' className='upload-form'>
              <Form.Item label='版面文件' required>
                <Upload
                  showUploadList={false}
                  beforeUpload={file => beforeUpload(file)}
                  accept='.docx, .pdf, .xlsx, .xls, .png, .jpg, .jpeg'
                >
                  <Button icon={<UploadOutlined />}>上传文件</Button>
                </Upload>
                {templateFile.name && (
                  <Text type='success' className='file-name'>
                    已上传: {templateFile.name}
                  </Text>
                )}
              </Form.Item>

              <Form.Item>
                <Button type='primary' onClick={handleDownload} size='large' loading={generating} block>
                  开始识别
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Layout.Sider>
        {isShowMk && (
          <Layout.Content style={{ padding: 24, background: '#fff' }}>
            <Flex align='center' justify='center' style={{ height: '50px' }}>
              <Title level={3}>分析结果</Title>
            </Flex>

            <div
              className='scroll-container'
              ref={scrollRef}
              style={{ height: 'calc(100vh - 98px)', overflowY: 'auto' }}
            >
              <StreamTypewriter
                onchange={() => {
                  scrollRef.current?.scrollTo({ top: scrollRef.current.scrollHeight, behavior: 'smooth' })
                }}
                key={count}
                text={mk}
                end={!generating}
              />

              {!generating && mk && (
                <Button
                  style={{ marginTop: 12, padding: 0 }}
                  icon={<DownloadOutlined />}
                  type='link'
                  onClick={handleExport}
                >
                  导出内容
                </Button>
              )}
            </div>
          </Layout.Content>
        )}
      </Layout>
    </div>
  )
}

export default LayoutRecognition
