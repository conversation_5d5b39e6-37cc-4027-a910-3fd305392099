import { useCallback, useEffect, useMemo, useState } from 'react'
import { useLocation } from 'react-router-dom'
import { Spin } from 'antd'
import dagre from 'dagre'
import {
  Background,
  Controls,
  MiniMap,
  ReactFlow,
  addEdge,
  useNodesState,
  useEdgesState,
  type OnConnect,
  type Node,
  type Edge
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'
import { getCodeInfo } from '@/api/interpreter'

// 布局函数
const applyDagreLayout = (nodes: Node[], edges: Edge[]): Node[] => {
  const g = new dagre.graphlib.Graph()
  g.setGraph({ rankdir: 'TB', nodesep: 50, ranksep: 100 }) // TB 表示从上到下的布局
  g.setDefaultEdgeLabel(() => ({}))

  // 添加节点到图中
  nodes.forEach((node) => {
    g.setNode(node.id, { width: 100, height: 50 })
  })

  // 添加边到图中
  edges.forEach((edge) => {
    g.setEdge(edge.source, edge.target)
  })

  // 计算布局
  try {
    dagre.layout(g)
  } catch (error) {
    console.error('Dagre layout failed:', error)
    return nodes
  }

  // 更新节点位置
  return nodes.map((node) => {
    const pos = g.node(node.id)
    return { ...node, position: { x: pos.x, y: pos.y } }
  })
}

// 解析流程图字符串
const parseFlowchart = (flowchart: string) => {
  const nodeRegex = /(\w+)\[(.*?)\]/g
  const edgeRegex = /(\w+)\s*-->\s*(\w+)/g

  let nodes: Node[] = []
  let edges: Edge[] = []
  let match

  // 解析节点
  while ((match = nodeRegex.exec(flowchart)) !== null) {
    const [_, id, label] = match
    nodes.push({ id, position: { x: 0, y: 0 }, data: { label } })
  }

  // 解析边
  while ((match = edgeRegex.exec(flowchart)) !== null) {
    const [_, source, target] = match
    edges.push({ id: `${source}->${target}`, source, target })
  }

  // 应用布局
  nodes = applyDagreLayout(nodes, edges)

  return { nodes, edges }
}

export const Flowchart = () => {
  const { search } = useLocation()
  const searchParams = useMemo(() => new URLSearchParams(search), [search])
  const shortUrl = searchParams.get('shortUrl')
  const [loading, setLoading] = useState(true)
  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])

  useEffect(() => {
    if (shortUrl) {
      getCodeInfo(shortUrl).then((res) => {
        setLoading(false)
        if (res.code === 200) {
          const code = String(`${res.data}`).replace(/\n$/, '')
          const parsedData = parseFlowchart(code)
          setEdges(parsedData.edges as any)
          setNodes(parsedData.nodes as any)
        }
      })
    }
  }, [searchParams, shortUrl, setEdges, setNodes])

  const onConnect: OnConnect = useCallback(
    (connection) => setEdges((eds) => addEdge(connection, eds)),
    [setEdges]
  )

  return (
    <div style={{ width: '100vw', height: '100vh' }}>
      <Spin tip="加载中" spinning={loading} fullscreen size="large" />
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        fitView
        proOptions={{ hideAttribution: true }}
      >
        <Background />
        <MiniMap />
        <Controls />
      </ReactFlow>
    </div>
  )
}

export default Flowchart
