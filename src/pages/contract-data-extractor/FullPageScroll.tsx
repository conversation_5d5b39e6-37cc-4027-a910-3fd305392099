import React, { useRef, forwardRef, useImperativeHandle, useEffect, useState } from 'react'

// 类型定义
interface SectionItem {
  name: string
  children: React.ReactNode
}

interface FullPageScrollProps {
  sections: SectionItem[]
  setCurrentSection: (index: number) => void
}

export interface FullPageScrollHandle {
  handleNavClick: (i: number) => void
}

export const FullPageScroll = forwardRef<FullPageScrollHandle, FullPageScrollProps>(
  ({ sections, setCurrentSection }, ref) => {
    const sectionsRef = useRef<(HTMLDivElement | null)[]>([])
    const containerRef = useRef<HTMLDivElement>(null)
    const [windowWidth, setWindowWidth] = useState(window.innerWidth)
    // 处理窗口大小变化
    useEffect(() => {
      const handleResize = () => {
        setWindowWidth(window.innerWidth)
      }

      window.addEventListener('resize', handleResize)

      return () => {
        window.removeEventListener('resize', handleResize)
      }
    }, [])

    // 导航点击处理
    const handleNavClick = (index: number) => {
      setCurrentSection(index)
      sectionsRef.current[index]?.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'start'
      })
    }

    useImperativeHandle(ref, () => ({
      handleNavClick
    }))

    return (
      <div
        ref={containerRef}
        style={{
          overflow: 'hidden',
          height: 'calc(100vh - 100px)',
          display: 'flex',
          flexDirection: 'row',
          flexWrap: 'nowrap',
          scrollSnapType: 'x mandatory',
          width: '100vw',
          position: 'relative'
        }}
      >
        {/* 内容区块 */}
        {sections.map((section, index) => (
          <div
            key={index}
            ref={el => (sectionsRef.current[index] = el)}
            style={{
              width: `${windowWidth}px`, // 使用当前窗口宽度
              minWidth: `${windowWidth}px`, // 确保最小宽度匹配
              scrollSnapAlign: 'start',
              flexShrink: 0,
              height: '100%'
            }}
          >
            {section.children}
          </div>
        ))}
      </div>
    )
  }
)
