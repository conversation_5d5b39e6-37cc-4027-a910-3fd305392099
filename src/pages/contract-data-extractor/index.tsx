/** 模板生成工具 */
import { Flex, message, Modal, Spin, Typography } from 'antd'
import { useRef, useState } from 'react'
import { contractDataExtractor } from '@/api/contractDataExtractor'
import { FullPageScroll, FullPageScrollHandle } from './FullPageScroll'
import Step1, { FileItem } from './Step1'
import Step2 from './Step2'
import './index.less'

const appKey = import.meta.env['VITE_CONTRACT_DATA_EXTRACTOR'] || ''

// 添加全局变量存储markdown内容和keyPoints
export let globalMarkdownContent = ''
export let globalKeyPoints = ''
const CONTRACT_MARKDOWN_DATA = `## 合同基本信息
### 合同主体
- **买方**：北京神州新桥科技有限公司  
  - 地址：北京市海淀区西三环北路89号12层B-08号  
  - 联系人：李娜  
  - 电话：010-59639388  
- **卖方**：长沙巨茂电子科技有限公司  
  - 地址：湖南省长沙市雨花区人民路9号朝阳银座707  
  - 联系人：何传智  
  - 电话：15111076707  

### 合同签订信息
- **签订地点**：北京  
- **签订日期**：2024年11月08日  

### 合同编号
- 买方合同编号：未提供  
- 卖方合同编号：未提供  

---

## 合同标的信息
### 设备清单
| 产品名称         | 产品型号                   | 数量 | 单价（元） | 小计（元） | 质保年限 |
|------------------|----------------------------|------|------------|------------|----------|
| 信创分流器       | T6100-48XF4QP             | 1    | 53190      | 53190      | 5年      |
| 10G光模块        | SFP-M1-L192P8             | 24   | 288        | 6912       | 5年      |
| 40G光模块        | QSFP-M1-M768C8            | 4    | 639        | 2556       | 5年      |
| 信创分流器       | T5100-48GT2QP             | 1    | 18624      | 18624      | 4年      |
| 10G光模块        | SFP-M1-L192P8             | 2    | 288        | 576        | 4年      |
| 1G光模块         | SFP-M1-L24P8              | 4    | 150        | 600        | 4年      |

### 合同总金额
- **最终成交价**：¥82,458.00元（人民币捌万贰仟肆佰伍拾捌元整）  
- **说明**：总金额已包含所有人工费及税费，买方不再支付其他费用。

---

## 合同付款计划
### 付款时间
- **付款时间**：合同签订后，买方在到货后45个自然日内支付全部货款。

### 付款条件
- 付款前，卖方需提供以下材料：  
  - 增值税专用发票（13%税率）  
  - 货物签收单  
  - 设备序列号  
  - 原厂服务生效函  

- 若材料不全，买方有权延期付款，由此造成的损失由卖方承担。

### 卖方付款账号
- **账户名称**：长沙巨茂电子科技有限公司  
- **开户行**：长沙银行解放东路支行  
- **账号**：800123860108010  
- **行号**：未提供  

---

## 交付与验收
### 交货时间及方式
- **交货时间**：合同签订后15日内一次性送达。  
- **交货地点**：  
  1. 财信证券股份有限公司：湖南省长沙市雨花区沙湾路168号移动东片区机楼10层（联系人：李帅，电话：18163553789）  
  2. 北京银行股份有限公司长沙分行：湖南省长沙市天心区湘江中路二段36号华远华中心7楼（联系人：杨新根，电话：18570614987）  

### 验收标准及方式
1. **外包装验收**：买方或最终用户收到设备当日进行外包装验收，发现不符的，卖方应在2日内更换。  
2. **开箱验收**：买方或最终用户自收到设备之日起5日内进行开箱验收，发现不符的，卖方应在2日内更换。  
3. **使用验收**：买方或最终用户在使用设备的最初3个月内发现隐蔽性缺陷，卖方应在5日内完成更换或维修。

---

## 合同其他核心条款
### 售后服务
1. 设备安装调试及售后服务由迈普原厂提供。  
2. 售后服务期限：4年或5年（详见附件一）。  
3. 售后服务方式：按原厂商标准及双方约定内容，提供上门服务。  
4. 卖方保证买方或最终用户享有原厂售后服务、技术支持和升级服务，因卖方原因导致无法享受服务的，卖方承担全部责任。

### 违约责任
1. **卖方违约**：  
   - 迟延交货每超过1日，支付合同总价款0.5%的违约金，最高不超过合同总价款的5%。  
   - 迟延交货超过5日，买方有权解除合同。  
   - 货物质量不符合约定的，卖方应在1个月内更换或维修，仍不符合的，买方有权终止合同并索赔。  
   - 设备侵犯第三方权益的，卖方承担全部责任并赔偿损失。  

2. **买方违约**：  
   - 迟延付款每超过1日，支付合同总价款0.5%的违约金，最高不超过合同总价款的5%。  
   - 迟延付款超过5日，卖方有权解除合同。

### 保密条款
- 买卖双方对在合同签订、履行过程中知悉的对方资料和专有信息负有保密义务，未经书面同意不得向第三方泄露。本条款独立于合同，不受合同变更、解除或终止的影响。

### 争议解决
- 因合同履行发生争议，双方协商解决；协商未果，提交买方所在地人民法院解决。

### 其他条款
1. 卖方与设备原厂商的特殊约定，未经买方书面确认，对买方不发生法律效力。  
2. 合同自签订之日起生效，有效期至保修终止日期止。  
3. 合同一式肆份，双方各执贰份，具有同等效力。  
4. 未尽事宜，双方可签订补充协议。`

export const ContractDataExtractor = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [uploadedFiles, setUploadedFiles] = useState<FileItem[]>([])
  const [currentSection, setCurrentSection] = useState<number>(0)
  const [step2Key, setStep2Key] = useState(1)
  const childRef = useRef<FullPageScrollHandle>(null)
  const [globalLoading, setGlobalLoading] = useState<boolean>(false)
  // 生成状态
  const [generating, setGenerating] = useState<boolean>(false)
  // 生成消息
  const [messages, setMessages] = useState<string>('')
  const [info, setInfo] = useState<string>('')
  const [finished, setFinished] = useState<boolean>(false)

  const handleGeneration = async (
    keyPoints: string,
    files: FileItem[]
    // info: string
  ) => {
    setGenerating(true)
    setGlobalLoading(true)
    // setMessages(
    //   '<think>是到付哈封建士大夫撒旦啊是大家的沙发上的客服都是发货速度放缓士大夫</think>'
    // )
    setMessages('')
    let str = ''
    let accumulatedMessages = '<think>'
    let istrue = false
    try {
      await contractDataExtractor(
        {
          keypoints: keyPoints,
          appKey,
          // info,
          type: '内容提取',
          // compType: '销售商务',
          files: files.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x.id,
          })),
        },
        {
          onMessage: (text) => {
            if (text) {
              accumulatedMessages += text
              str += text
              // console.log(str, 'str')

              // 先清理markdown代码块标识
              const cleanedData = accumulatedMessages
                .replace(/\n\n```markdown\s*/g, '\n\n') // 替换开始的markdown标识
                .replace(/\n{1,2}```/g, '\n\n<think>') // 替换结束的markdown标识
              // .replace(/\n{1,2}```/g, (match, offset, string) => {
              //   // 检查是否是最后一个```
              //   const remainingText = string.slice(offset + match.length)
              //   return remainingText.includes('```') ? '\n\n<think>' : ''
              // })

              // 处理所有think标签内的换行符
              let processedText = cleanedData
              //               let processedText = `<think>我在思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考我在思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考</think>\n\n## 合同名称：北京银行 2024 年度基础网络设备采购合同

              // ### 合同主体
              // - **甲方**：北京银行股份有限公司[长沙分行]
              // - **乙方**：北京神州新桥科技有限公司\n\n<think>我在思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考思考</think>\n\n艾弗森感动和法规和国家和国际航空港的复合风管风格恢复鬼画符鬼画符鬼画符梵蒂冈梵蒂冈地方官地方官的`
              let hasThinkTag = true

              while (hasThinkTag) {
                const beforeLength = processedText.length
                processedText = processedText.replace(
                  /<think>([\s\S]*?)<\/think>/g,
                  (match, p1) => {
                    // 替换think标签内的换行符
                    const processedContent = p1.replace(/\n/g, '<br />')
                    return `<think>${processedContent}</think>`
                  }
                )
                // 如果长度没变，说明没有找到think标签了
                hasThinkTag = beforeLength !== processedText.length
              }
              // 判断processedText字符串中所有的think标签是否闭合
              const checkThinkTags = (text: string): boolean => {
                const openTags = (text.match(/<think>/g) || []).length
                const closeTags = (text.match(/<\/think>/g) || []).length
                return openTags === closeTags
              }

              istrue = checkThinkTags(processedText)

              if (istrue) {
                setMessages(processedText)
              }

              // setMessages(processedText)
            }
          },
          onError: () => {
            setGenerating(false)
            setGlobalLoading(false)
          },
          onFinish: () => {
            setGlobalLoading(false)
            setGenerating(false)
            setFinished(true)
            // str = '```markdown\n' + CONTRACT_MARKDOWN_DATA + '\n```' + '```markdown\n' + CONTRACT_MARKDOWN_DATA + '\n```'
            // const markdownContent =
            //   str
            //     .match(/```markdown([\s\S]*?)```/g)
            //     ?.map((match) => {
            //       return match.replace(/```markdown\n?/, '').replace(/```$/, '')
            //     })
            //     .join('\n') || ''

            // 存储到全局变量
            // globalMarkdownContent = markdownContent
            globalMarkdownContent = str
            // 存储到全局变量
            //             globalMarkdownContent = `## 合同名称：北京银行 2024 年度基础网络设备采购合同
            // ### 合同主体
            // - **甲方**：北京银行股份有限公司[长沙分行]
            // - **乙方**：北京神州新桥科技有限公司`
            // console.log(str, 'str')
            console.log(globalMarkdownContent, 'globalMarkdownContent')
          },
        }
      )
    } catch (err) {
      setGenerating(false)
      setGlobalLoading(false)
    }
  }

  const getFileInfo = async (files: FileItem[], keyPoints: string) => {
    setGlobalLoading(true)
    console.log(files, 'file')
    // return
    let str = ''
    try {
      await contractDataExtractor(
        {
          appKey,
          type: '内容提取',
          compType: '销售商务',
          // files: [
          //   {
          //     type: 'document',
          //     transfer_method: 'local_file',
          //     upload_file_id: files[0].id,
          //   },
          // ],
          files: files.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x.id,
          })),
        },
        {
          onMessage: (text) => {
            if (text) {
              str += text
            }
          },
          onError: () => {
            setGlobalLoading(false)
          },
          onFinish: () => {
            setInfo(str)
            handleGeneration(keyPoints, files, str)
          },
        }
      )
    } catch (err) {
      setGenerating(false)
      setGlobalLoading(false)
    }
  }

  const Step1OnSubmit = (files: FileItem[], keyPoints: string) => {
    console.log(files, 'files')
    setUploadedFiles(files)
    childRef.current?.handleNavClick(1)
    // 存储keyPoints到全局变量
    globalKeyPoints = keyPoints
    handleGeneration(keyPoints, files)
    setStep2Key((prev) => prev + 1)
  }

  const back = () => {
    Modal.confirm({
      title: '提示',
      content: '确定要返回吗？数据不会被保存',
      okText: '返回',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        setStep2Key((prev) => prev + 1)
        childRef.current?.handleNavClick(0)
      },
      onCancel() {
        console.log('取消')
      },
    })
  }

  interface SectionItem {
    name: string
    children: React.ReactNode
  }

  const sections: SectionItem[] = [
    {
      name: '1',
      children: (
        <Step1
          onSubmit={Step1OnSubmit}
          messageApi={messageApi}
          setGlobalLoading={setGlobalLoading}
        />
      ),
    },
    {
      name: '2',
      children: (
        <Step2
          key={step2Key}
          info={info}
          messages={messages}
          messagesEnd={!generating}
          back={back}
          fileList={uploadedFiles}
          currentSection={currentSection}
          setGlobalLoading={setGlobalLoading}
          markdownContent={globalMarkdownContent}
          keyPoints={globalKeyPoints}
          finished={finished}
        />
      ),
    },
  ]

  return (
    <>
      {contextHolder}
      <Spin tip="加载中" spinning={globalLoading} fullscreen size="large" />
      <Flex
        vertical
        className="contract-scene-set-v2-toolbar"
        justify="center"
        align="center"
      >
        <Typography.Title className="title-text">
          合同信息提取汇总助手
        </Typography.Title>
        <Typography.Text style={{ fontSize: 14, color: '#8F91A6' }}>
          合同解析，提取关键信息，提取汇总
        </Typography.Text>
      </Flex>
      <FullPageScroll
        sections={sections}
        ref={childRef}
        setCurrentSection={setCurrentSection}
      />
    </>
  )
}

export default ContractDataExtractor
