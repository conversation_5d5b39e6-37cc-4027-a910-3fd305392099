import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Card, Typo<PERSON>, Button, Spin } from 'antd'
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons'
import StreamTypewriter from '@/component/StreamTypewriter'
import { NoData } from '@/component/NoData'
import ReactECharts from 'echarts-for-react'
import { businessQuery } from '@/api/businessQuery'
import './index.less'

const { Title, Paragraph } = Typography
interface nameMapType {
  [key: string]: string
}

const nameMap: nameMapType = {}

interface ContractType {
  [key: string]: number | string
}

const contractData: ContractType[] = []

// 添加一个计算列数的函数
const getGridColumns = (fields: Array<keyof ContractType>): string => {
  const columnCount = fields.length || 1 // 如果没有字段，至少保持1列
  return `repeat(${columnCount}, 1fr)`
}

export const BusinessQuery: React.FC = () => {
  const [generating, setGenerating] = useState<boolean>(false)
  const [thankText, setThankText] = useState<string>('')
  const [option, setOption] = useState<any | null>(null)
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [viewMode, setViewMode] = useState<'card' | 'list'>('card')
  const [selectedFields, setSelectedFields] = useState<Array<keyof ContractType>>([])
  const [showFields, setShowFields] = useState<boolean>(false)
  const [filteredData, setFilteredData] = useState<ContractType[]>(contractData)

  const fieldRef = useRef<HTMLDivElement>(null)

  // 点击外部关闭字段选择
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent): void => {
      if (fieldRef.current && !fieldRef.current.contains(e.target as Node)) {
        setShowFields(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const components = {
    pre: ({ node, ...props }: any) => <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }} {...props} />,
    think: ({ node, ...props }: any) => <span>{props.children}</span>,
    details: () => {
      return <></>
    }
  }

  const generateOption = (data: any[], xAxisKey: string, yAxisKey: string) => {
    // 动态提取 xAxis 和 yAxis 数据
    const xAxisData = data.map(item => item[xAxisKey])
    const yAxisData = data.map(item => Number(item[yAxisKey]))

    return {
      title: {
        text: '借入人转融券余额统计'
      },
      grid: {
        left: '110px',
        bottom: '20px',
        right: '90px',
        top: '80px'
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        name: `${xAxisKey}`
      },
      yAxis: {
        type: 'value',
        name: `${yAxisKey} (元)`, // 在 y 轴名称中添加单位
        axisLabel: {
          formatter: (value: number) => `${value} (元)` // 格式化刻度值，添加单位
        }
      },
      series: [
        {
          data: yAxisData,
          type: 'bar',
          barMaxWidth: 30,
          itemStyle: {
            color: '#4F46E5'
          }
        }
      ],
      tooltip: {
        trigger: 'axis'
      }
    }
  }

  const generateOption2 = (
    data: any[],
    xAxisKey: string,
    yAxisKeys: string[], // 改为数组，支持多个y轴数据键
    seriesNames: string[] // 每个系列的名称
  ) => {
    // 动态提取 xAxis 数据
    const xAxisData = data.map(item => item[xAxisKey])

    // 为每个y轴键创建对应的系列数据
    const series = yAxisKeys.map((key, index) => ({
      name: seriesNames[index] || key, // 使用提供的名称或默认使用键名
      type: 'bar',
      barMaxWidth: 30,
      data: data.map(item => Number(item[key])),
      itemStyle: {
        color: index === 0 ? '#4F46E5' : '#10B981' // 为不同系列设置不同颜色
      }
    }))

    return {
      title: {
        text: '投资者二级分类金额统计'
      },
      grid: {
        left: '110px',
        bottom: '50px',
        right: '100px',
        top: '80px'
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        name: `${xAxisKey}`,
        axisLabel: {
          interval: 0,
          rotate: 30,
          fontSize: 10,
          formatter: (value: string) => {
            // 如果字符长度小于等于 6，直接返回
            if (value.length <= 6) return value

            // 计算字符串的中间位置
            const middle = Math.floor(value.length / 2)

            // 插入换行符并返回
            return value.slice(0, middle) + '\n' + value.slice(middle)
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '金额 (元)', // 统一y轴名称
        axisLabel: {
          formatter: (value: number) => `${value} (元)`
        }
      },
      series,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: seriesNames // 显示图例
      }
    }
  }

  const formatDateToChinese = (dateString: string): string => {
    const date = new Date(dateString)
    const year = date.getFullYear()
    const month = date.getMonth() + 1 // 月份从 0 开始，需要加 1
    const day = date.getDate()
    return `${year}年${month}月${day}日`
  }

  const isTimestamp = (value: any): boolean => {
    // 检查是否为数字或可以转换为数字
    if (typeof value === 'number' || (!isNaN(value) && !isNaN(parseFloat(value)))) {
      // 将值转换为数字并尝试创建日期对象
      const date = new Date(Number(value))
      return date.getTime() > 0 // 检查是否为有效日期
    }
    return false
  }

  const handleGeneration = useCallback(async (str: string) => {
    setThankText('')
    setFilteredData([])
    setGenerating(true)
    setOption(null)
    let message = ''
    let hasThank = true
    try {
      await businessQuery(str, {
        onMessage: (text, finished) => {
          if (text) {
            message += text
            if (!(text.includes('<details>') || text.includes('</details>')) && hasThank) {
              setThankText(t => t + text)
            } else {
              hasThank = false
            }
          }
          if (finished) {
            // 正则表达式获取<think>标签之后的内容
            const regexArr = /<details>([\s\S]*?)<\/details>/
            const matchArr = message.match(regexArr)

            if (matchArr && matchArr[1]) {
              // 去掉前面的空格或者换行，解析为数组的内容
              const jsonArrayString = matchArr[1].trim()
              // 尝试解析JSON
              try {
                const contracts = JSON.parse(jsonArrayString)
                if (contracts && contracts.length > 1) {
                  let xAxisKey = '借入人名称'
                  let yAxisKey = '转融券余额'
                  // 判断 contracts 对象中是否包含 '转融券余额' 属性
                  const hasBalanceKey = contracts.some((contract: any) => contract.hasOwnProperty(yAxisKey))
                  if (hasBalanceKey) {
                    const option = generateOption(contracts, xAxisKey, yAxisKey)
                    setOption(option)
                  }

                  const hasCJKey = contracts.some((contract: any) => contract.hasOwnProperty('出借余额'))
                  if (hasCJKey) {
                    let xAxisKey2 = '投资者二级分类'
                    let yAxisKey2 = '出借余额'
                    let yAxisKey3 = '成交额'
                    const option = generateOption2(contracts, xAxisKey2, [yAxisKey2, yAxisKey3], [yAxisKey2, yAxisKey3])
                    setOption(option)
                  }
                } else {
                  setOption(null)
                }
                if (contracts && contracts.length > 0) {
                  setFilteredData(
                    contracts.map((x: any) => {
                      if (x['转融券余额']) {
                        x['转融券余额'] += '元'
                      }
                      Object.keys(x).forEach(key => {
                        if (key.includes('时间') && isTimestamp(x[key])) {
                          x[key] = formatDateToChinese(x[key])
                        }
                      })
                      return x
                    })
                  )
                }
              } catch (error) {
                console.error('JSON解析失败:', error)
                setFilteredData([])
              }
            } else {
              setFilteredData([])
            }
            setGenerating(false)
          }
        },
        onError: () => {
          setGenerating(false)
        },
        onFinish: () => {}
      })
    } catch (err) {
      setGenerating(false)
    }
  }, [])

  // 字段选择处理
  const toggleField = (field: keyof ContractType): void => {
    setSelectedFields(prev => (prev.includes(field) ? prev.filter(f => f !== field) : [...prev, field]))
  }

  // 修改 renderContractItems 函数中的列表视图部分
  const renderContractItems = () => {
    return filteredData.map((contract, index) => {
      if (!contract) return null
      const availableFields = Object.keys(contract) as Array<keyof ContractType>
      const fieldsToShow =
        selectedFields.length > 0 ? availableFields.filter(field => selectedFields.includes(field)) : availableFields

      return (
        <div key={index} className={`contract-item ${viewMode === 'list' ? 'list-view' : ''}`}>
          {viewMode === 'card' ? (
            <>
              {fieldsToShow.map(field => {
                const fieldLabel = nameMap[field] || field
                if (field === 'contract_amount') {
                  return (
                    <p key={field}>
                      {fieldLabel}：<span className='contract-amount'>¥{contract[field].toLocaleString()}</span>
                    </p>
                  )
                }
                if (field === 'contract_effective_date') {
                  return (
                    <p key={field}>
                      {fieldLabel}：{new Date(contract[field]).toLocaleDateString()}
                    </p>
                  )
                }
                return (
                  <p key={field}>
                    {fieldLabel}：{contract[field]}
                  </p>
                )
              })}
            </>
          ) : (
            <div
              className='list-item-container'
              style={{
                gridTemplateColumns: getGridColumns(fieldsToShow)
              }}
            >
              {fieldsToShow.map(field => (
                <div key={field} className='list-item-field'>
                  <span className='field-label'>{nameMap[field] || field}</span>
                  <span className='field-value'>
                    {field === 'contract_amount'
                      ? `¥${contract[field].toLocaleString()}`
                      : field === 'contract_effective_date'
                      ? new Date(contract[field]).toLocaleDateString()
                      : contract[field]}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      )
    })
  }

  const [collapse, setCollapse] = useState(false)
  const handleCollapse = () => {
    // 处理收起逻辑
    setCollapse(!collapse)
  }

  return (
    <>
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      <div className='business-query-container'>
        <h1>业务查询</h1>

        <div className='search-container'>
          <input
            type='text'
            value={searchTerm}
            onKeyDown={e => {
              if (e.key === 'Enter') {
                handleGeneration(searchTerm)
              }
            }}
            onChange={e => setSearchTerm(e.target.value)}
            placeholder='请输入内容查询'
          />
          <button onClick={() => handleGeneration(searchTerm)}>搜索</button>
        </div>

        <div className='controls'>
          <div className='field-selection' ref={fieldRef}>
            <button onClick={() => setShowFields(!showFields)}>自定义展示</button>
            <div className={`dropdown-content ${showFields ? 'show' : ''}`}>
              {Object.keys(filteredData[0] || {}).map(field => (
                <label key={field}>
                  <input
                    type='checkbox'
                    checked={selectedFields.includes(field as keyof ContractType)}
                    onChange={() => toggleField(field as keyof ContractType)}
                  />
                  {nameMap[field] || field}
                </label>
              ))}
            </div>
          </div>

          <div className='view-toggle'>
            <button className={viewMode === 'card' ? 'active' : ''} onClick={() => setViewMode('card')}>
              卡片视图
            </button>
            <button className={viewMode === 'list' ? 'active' : ''} onClick={() => setViewMode('list')}>
              列表视图
            </button>
          </div>
        </div>

        <div className='total-count'>共 {filteredData.length} 条合同</div>
        {/* 思考过程区域 */}
        {thankText && (
          <Card className='thinking-card'>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={4} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  fill='none'
                  viewBox='0 0 24 24'
                  stroke='currentColor'
                  style={{ marginRight: 8, width: 20, height: 20, color: '#1890ff' }}
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth='2'
                    d='M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z'
                  />
                </svg>
                查询思考过程
              </Title>
              <Button
                type='link'
                onClick={handleCollapse}
                icon={collapse ? <CaretDownOutlined /> : <CaretUpOutlined />}
              >
                {collapse ? '展开' : '收起'}
              </Button>
            </div>
            {!collapse && (
              <Paragraph
                style={{
                  marginTop: 16,
                  backgroundColor: '#f5f5f5',
                  padding: '8px',
                  borderRadius: '4px',
                  color: 'rgb(55, 65, 81)'
                }}
              >
                <StreamTypewriter text={thankText} components={components} end={true} />
              </Paragraph>
            )}
          </Card>
        )}

        {option && (
          <div style={{ padding: '24px', background: '#fff', margin: '28px 0' }}>
            <ReactECharts option={option as any} style={{ height: '400px' }} />
          </div>
        )}
        <div className={`contract-list ${viewMode === 'list' ? 'list-view' : ''}`}>
          {filteredData.length === 0 ? (
            <NoData description='未查询出数据，请输入条件并查询' marginTop='150px' />
          ) : (
            renderContractItems()
          )}
        </div>

        <div style={{ height: '20px' }}></div>
      </div>
    </>
  )
}

export default BusinessQuery
