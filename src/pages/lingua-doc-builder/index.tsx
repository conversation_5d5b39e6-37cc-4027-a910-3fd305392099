import React, { useCallback, useRef, useState } from 'react'
import { Spin, message, Flex, Typography, Steps, Button } from 'antd'
import { Template } from './componment/template'
import Step2BasicInfo from './componment/Step2BasicInfo'
import Step3Analysis from './componment/Step3Analysis'
import { linguaDocBuilder } from '@/api/linguaDocBuilder'
import { extractJSONFromString } from '@/utils/json-extractor'
import { extractContent, extractFirstCompleteJSON, extractMarkdownFromString } from '@/utils/common'
import { Document, Packer, Paragraph, TextRun, HeadingLevel, TableCell, TableRow, Table, WidthType } from 'docx'
import { saveAs } from 'file-saver'
import './index.less'
import MarkdownIt from 'markdown-it'

const { Step } = Steps

export const LinguaDocBuilder: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [step3message, setStep3message] = useState<string>('')
  const [generating, setGenerating] = useState<boolean>(false)
  const [step3End, setStep3End] = useState<boolean>(false)
  const [language, setLanguage] = useState<string>('英文')
  const [templateData1, setTemplateData1] = useState<string>('')
  const [templateData2, setTemplateData2] = useState<string>('')
  const [uploadedFile, setUploadedFile] = useState<{ id: string; name: string }>({ id: '', name: '' })
  const [uploadedFileList, setUploadedFileList] = useState<{ id: string; name: string }[]>([])
  const [current, setCurrent] = useState(0)
  const scrollToBottom = () => {
    const element = steps[1].ref.current
    if (element) {
      const targetScrollTop = element.scrollHeight
      element.scrollTop = targetScrollTop
    }
  }

  const steps = [
    {
      title: '上传模板',
      content: (
        <Template
          language={language}
          setLanguage={setLanguage}
          start={() => {
            handleAnalyzeTemplate()
          }}
          onUpload={file => {
            setUploadedFile(file)
          }}
          onFileListChange={(list: any[]) => {
            setUploadedFileList(list)
          }}
          generating={generating}
          setGenerating={setGenerating}
        />
      ),
      ref: useRef<HTMLDivElement>(null)
    },
    {
      title: '模板解析',
      content: (
        <Step2BasicInfo
          data={templateData1}
          generating={generating}
          onchange={() => {
            scrollToBottom()
          }}
        />
      ),
      ref: useRef<HTMLDivElement>(null)
    },
    {
      title: '内容生成',
      content: <Step3Analysis data={step3message} language={language} end={step3End} />,
      ref: useRef<HTMLDivElement>(null)
    }
  ]
  const handleExport = (str: string) => {
    try {
      const md = new MarkdownIt({ html: true })
      const html = md.render(str)

      // 解析 HTML 并转换为 docx 格式的段落
      const parser = new DOMParser()
      const docEl = parser.parseFromString(html, 'text/html').body
      const paragraphs: Paragraph[] = []

      // 遍历 HTML 节点并转换为 docx 段落
      docEl.childNodes.forEach(node => {
        if (node.nodeType === Node.TEXT_NODE && node.textContent) {
          // 纯文本
          paragraphs.push(
            new Paragraph({
              children: [new TextRun(node.textContent)]
            })
          )
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement
          switch (element.tagName.toLowerCase()) {
            case 'html':
              // 如果需要处理整个 HTML 文档，可以在这里添加逻辑
              paragraphs.push(
                new Paragraph({
                  children: [new TextRun('HTML Document Start')]
                })
              )
              break
            case 'body':
              // 如果需要处理 body 标签内容，可以在这里添加逻辑
              paragraphs.push(
                new Paragraph({
                  children: [new TextRun('Body Content Start')]
                })
              )
              break
            case 'h1':
              paragraphs.push(
                new Paragraph({
                  heading: HeadingLevel.HEADING_1,
                  children: [new TextRun(element.textContent || '')]
                })
              )
              break
            case 'h2':
              paragraphs.push(
                new Paragraph({
                  heading: HeadingLevel.HEADING_2,
                  children: [new TextRun(element.textContent || '')]
                })
              )
              break
            case 'p':
              paragraphs.push(
                new Paragraph({
                  children: [new TextRun(element.textContent || '')]
                })
              )
              break
            case 'ul':
              element.querySelectorAll('li').forEach(li => {
                paragraphs.push(
                  new Paragraph({
                    children: [
                      new TextRun({
                        text: `• ${li.textContent || ''}`,
                        bold: li.innerHTML.includes('<strong>')
                      })
                    ],
                    indent: { left: 720 }
                  })
                )
              })
              break
            case 'table':
              const rows = element.querySelectorAll('tr')
              const tableRows: TableRow[] = []

              rows.forEach(row => {
                const cells = Array.from(row.cells)
                const tableCells: TableCell[] = []

                cells.forEach(cell => {
                  const colspan = parseInt(cell.getAttribute('colspan') || '1', 10)
                  const rowspan = parseInt(cell.getAttribute('rowspan') || '1', 10)
                  const cellText = cell.textContent?.trim() || ''

                  // 创建表格单元格
                  tableCells.push(
                    new TableCell({
                      children: [new Paragraph(cellText)],
                      columnSpan: colspan > 1 ? colspan : undefined,
                      rowSpan: rowspan > 1 ? rowspan : undefined
                    })
                  )
                })

                // 创建表格行
                tableRows.push(new TableRow({ children: tableCells }))
              })

              // 创建表格并添加到文档
              const table = new Table({
                rows: tableRows,
                width: { size: 100, type: WidthType.PERCENTAGE } // 设置表格宽度为 100%
              })

              paragraphs.push(table as any)
              break
            default:
              paragraphs.push(
                new Paragraph({
                  children: [new TextRun(element.textContent || '')]
                })
              )
          }
        }
      })

      // 创建 Word 文档
      const doc = new Document({
        styles: {
          paragraphStyles: [
            {
              id: 'Heading1',
              name: 'Heading 1',
              run: {
                size: 32,
                bold: true,
                color: '2e6c80'
              }
            }
          ]
        },
        sections: [{ children: paragraphs }]
      })

      Packer.toBlob(doc).then(blob => {
        saveAs(blob, '导出内容.docx')
      })
    } catch (error) {
      console.error('导出失败:', error)
    }
  }

  const handleGenerationStart = async () => {
    setStep3message('')
    setGenerating(true)
    setStep3End(false)
    let res = ''
    try {
      await linguaDocBuilder(
        {
          type: '相关文件',
          info: templateData2,
          Relevant_documents: uploadedFileList.map(item => ({
            upload_file_id: item.id,
            type: 'document',
            transfer_method: 'local_file'
          })),
          template: {
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: uploadedFile.id
          },
          language
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              setStep3message(p => p + message)
              steps[2].ref.current?.scrollTo({
                top: steps[2].ref.current.scrollHeight + 1000,
                behavior: 'smooth'
              })
            }
            if (finished) {
              setGenerating(false)
              setStep3End(true)
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
            }
          },
          onError: () => {
            setGenerating(false)
            setStep3End(true)
          },
          onFinish: () => {
            setStep3End(true)
          }
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  const nextStep = () => {
    if (current === 1) {
      setCurrent(current + 1)
      if (step3message) return
      handleGenerationStart()
    } else {
      setCurrent(current + 1)
    }
  }

  const downloadReport = async () => {
    setGenerating(true)
    const res2 = extractJSONFromString(templateData2)
    let res = ''
    const jsonMatch = extractFirstCompleteJSON(step3message)
    if (!jsonMatch) {
      handleExport(extractMarkdownFromString(step3message) || '{}')
      setGenerating(false)
      return
    }
    const [_fullMatch, jsonStr] = jsonMatch
    try {
      await linguaDocBuilder(
        {
          type: '报告下载',
          info: res2 || '{}',
          files: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: uploadedFile.id
            }
          ],
          query: extractJSONFromString(jsonStr) || '{}',
          language
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
              // 提取()中的内容
              const parenthesesContent = res.match(/\((.*?)\)/)
              const parenthesesResult = parenthesesContent ? parenthesesContent[1] : null

              // 提取[]中的内容
              const squareBracketsContent = res.match(/\[(.*?)\]/)
              const squareBracketsResult = squareBracketsContent ? squareBracketsContent[1] : null

              if (parenthesesResult && squareBracketsResult) {
                const link = document.createElement('a')
                link.href = parenthesesResult
                link.download = `智能文档生成${squareBracketsResult}`
                document.body.appendChild(link)
                link.click()
                link.remove()
              }
              setGenerating(false)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  const handleAnalyzeTemplate = useCallback(async () => {
    setCurrent(1)
    setTemplateData1('')
    setTemplateData2('')
    setStep3message('')
    handleGenerationStart1()
    setGenerating(true)
    let res = ''
    try {
      await linguaDocBuilder(
        {
          type: '模板文件',
          template_type: '页面结构',
          template: {
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: uploadedFile.id
          },
          language,
          query: '开始'
        },
        {
          onMessage: (message: string | null) => {
            if (message) {
              res += message || ''
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {
            setGenerating(false)
            setTemplateData1(res.replace(/^```markdown\s*|```|markdown$/g, ''))
          }
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }, [uploadedFile, uploadedFileList, language])

  const handleGenerationStart1 = useCallback(async () => {
    let res = ''
    try {
      await linguaDocBuilder(
        {
          type: '模板文件',
          template_type: '模板结构',
          template: {
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: uploadedFile.id
          },
          language,
          query: '开始'
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              setTemplateData2(res)
              setGenerating(false)
            }
          },
          onError: () => {},
          onFinish: () => {
            console.log('finish1', res)
          }
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }, [uploadedFile, uploadedFileList, language])

  return (
    <div className='lingua_doc_builder-container'>
      {contextHolder}
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      <Flex className='toolbar' justify='center'>
        <Typography.Text className='title-text'>智能文档生成工具</Typography.Text>
      </Flex>
      <div className='steps-container'>
        <Steps current={current} className='custom-steps'>
          {steps.map(item => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <div className='steps-content '>
          {steps.map((step, index) => (
            <div
              key={index}
              ref={step.ref}
              className={`step-content scroll-container ${index === current ? 'active' : 'hidden'}`}
            >
              {step.content}
            </div>
          ))}
        </div>

        <div className='steps-action'>
          {current > 0 && (
            <Button
              style={{ marginRight: 8 }}
              onClick={() => {
                if (current === 1) {
                  handleAnalyzeTemplate()
                } else if (current === 2) {
                  handleGenerationStart()
                }
              }}
            >
              重新生成
            </Button>
          )}
          {current > 0 && (
            <Button style={{ marginRight: 8 }} onClick={() => setCurrent(current - 1)}>
              上一步
            </Button>
          )}
          {current < steps.length - 1 && current !== 0 && (
            <Button type='primary' onClick={nextStep}>
              下一步
            </Button>
          )}
          {current === steps.length - 1 && (
            <Button type='primary' onClick={() => downloadReport()}>
              下载报告
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

export default LinguaDocBuilder
