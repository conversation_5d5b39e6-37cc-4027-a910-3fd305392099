import { Card, Tabs } from 'antd'
import type { TabsProps } from 'antd'
import { useEffect, useRef, useState } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import RemarkBreaks from 'remark-breaks'
import RemarkMath from 'remark-math'
import './Step3Analysis.less'
import { extractMarkdownFromString } from '@/utils/common'

export const Step3Analysis: React.FC<{
  data: string
  language: string
  end: boolean
}> = ({ data, language, end }) => {
  const [activeKey, setActiveKey] = useState<string>('')
  const [content, setContent] = useState<string>('')
  const [content1, setContent1] = useState<string>('')
  const [cursorVisible, setCursorVisible] = useState(true)

  const cursorTimer = useRef<NodeJS.Timeout | undefined>(undefined)

  useEffect(() => {
    if (end) {
      clearInterval(cursorTimer.current)
      setCursorVisible(false)
    } else {
      cursorTimer.current = setInterval(() => {
        setCursorVisible(v => !v)
      }, 500)
    }
    return () => clearInterval(cursorTimer.current)
  }, [end, cursorTimer.current])

  useEffect(() => {
    if (language === '中文') {
      setActiveKey('1')
      setContent1(data)
    } else {
      if (data.length > 0) {
        setActiveKey('2')
        if (data.indexOf('</翻译结束>') !== -1) {
          let arr1 = data.split('</翻译结束>')
          setContent(arr1[0])
          setContent1(arr1[1] || '')
        } else if (data.indexOf('#') !== -1) {
          const index = data.indexOf('#')
          setContent(data.substring(0, index))
          setContent1('#' + data.substring(index + 1))
        }
      }
    }
  }, [data])

  const items: TabsProps['items'] = [
    {
      key: '2',
      label: '文献翻译',
      children: (
        <>
          <ReactMarkdown
            className='markdown-body lingua_doc_builder-items-container'
            remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}
          >
            {content}
          </ReactMarkdown>
          <span
            style={{
              visibility: cursorVisible ? 'visible' : 'hidden',
              color: '#222',
              fontWeight: 600,
              marginLeft: 2
            }}
          >
            |
          </span>
        </>
      )
    },
    {
      key: '1',
      label: '报告内容',
      children: (
        <>
          <ReactMarkdown
            className='markdown-body lingua_doc_builder-items-container'
            remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}
          >
            {extractMarkdownFromString(content1) || content1}
          </ReactMarkdown>
          <span
            style={{
              visibility: cursorVisible ? 'visible' : 'hidden',
              color: '#222',
              fontWeight: 600,
              marginLeft: 2
            }}
          >
            |
          </span>
        </>
      )
    }
  ]

  return (
    <Card title='分析结果' className='lingua_doc_builder-card'>
      <Tabs
        defaultActiveKey='1'
        onChange={key => setActiveKey(key)}
        activeKey={activeKey}
        items={language === '中文' ? items.filter(x => x.key === '1') : items}
      />
    </Card>
  )
}

export default Step3Analysis
