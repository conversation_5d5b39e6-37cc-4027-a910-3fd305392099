import React, { useState, useRef, useEffect } from 'react'
import { message, Flex, Card, Upload, Button, Typography, Tag, Select } from 'antd'
import { uploadFile } from '@/api/template'
import { InboxOutlined } from '@ant-design/icons'
import './template.less'

const token = import.meta.env['VITE_REGULATORY_REPORTING_TOKEN'] || ''

interface TemplateProps {
  onUpload: (file: { id: string; name: string }) => void
  onFileListChange: (uploadedFileList: { id: string; name: string }[]) => void
  generating: boolean
  setGenerating: (generating: boolean) => void
  language: string
  setLanguage: (language: string) => void
  start: () => void
}
export const Template: React.FC<TemplateProps> = ({
  onUpload,
  onFileListChange,
  generating,
  setGenerating,
  language,
  setLanguage,
  start
}) => {
  const [messageApi, contextHolder] = message.useMessage()
  const resValue = useRef({
    res1: '',
    res2: ''
  })

  const [uploadedFile, setUploadedFile] = useState<{ id: string; name: string }>({ id: '', name: '' })
  const [uploadedFileList, setUploadedFileList] = useState<{ id: string; name: string }[]>([])
  const allowedFileExtensions = (type: string): string[] => {
    return ['txt', 'docx', 'mdx', 'pdf', 'html', 'xlsx', 'xls', 'csv', 'md', 'htm'] // 允许全部格式
    // if (type === '1') {
    //   return ['docx'] // 仅允许上传 docx 格式
    // }
    // return ['docx', 'xlsx', 'txt', 'mdx', 'pdf', 'html', 'xls', 'csv', 'md', 'htm'] // 允许全部格式
  }
  const beforeUpload = (file: File, type: string) => {
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)?.toLowerCase()
    if (!allowedFileExtensions(type).includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'error',
        content: `文件格式不正确，请上传${allowedFileExtensions(type).join(',')}格式的文件`,
        duration: 2
      })
      return false
    }
    messageApi.open({
      key: 'uploading',
      type: 'loading',
      content: '文件上传中'
    })
    setGenerating(true)
    uploadFile(file, token)
      .then(response => {
        setGenerating(false)
        if (response && response.id) {
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
          if (type === '1') {
            setUploadedFile(response)
            onUpload(response)
          } else if (type === '2') {
            setUploadedFileList(prevList => [...prevList, response])
          }
          return false // 阻止自动上传
        } else {
          throw new Error('上传失败：未收到有效的响应')
        }
      })
      .catch(error => {
        setGenerating(false)
        messageApi.open({
          key: 'uploading',
          type: 'error',
          content: `文件上传失败: ${error.message}`,
          duration: 2
        })
      })
    return false // 阻止自动上传
  }

  useEffect(() => {
    onFileListChange(uploadedFileList)
  }, [uploadedFileList])

  return (
    <>
      {contextHolder}
      <Flex gap='large'>
        <Card className='lingua_doc_builder-form'>
          <Typography.Title level={2} className='page-title'>
            智能文档生成
          </Typography.Title>
          <Typography.Text type='secondary' className='page-description'>
            你好，作为资深文档生成专家，我可以结合您上传的资料和模板，为您快速生成专业、精准的跨语言文档。
          </Typography.Text>
          <Typography.Title level={5} style={{ padding: '10px 0', margin: 0 }}>
            目标语言
          </Typography.Title>
          <Select
            defaultValue='英文'
            value={language}
            onChange={value => {
              setLanguage(value)
            }}
            style={{ width: '100%' }}
            options={[
              { value: '中文', label: '中文' },
              { value: '英文', label: '英文' }
            ]}
          />
          <Typography.Title level={5} style={{ padding: '10px 0', margin: 0 }}>
            上传一份模板文件
          </Typography.Title>
          <Upload.Dragger
            showUploadList={false}
            multiple={false}
            beforeUpload={(file: File) => beforeUpload(file, '1')}
          >
            <p className='reporting-upload-drag-icon'>
              <InboxOutlined />
            </p>
            <p>请拖拽文件到此处或点击上传文件按钮</p>
          </Upload.Dragger>

          <Typography.Title level={5} style={{ padding: '10px 0', margin: 0 }}>
            上传一份或多份资料文件
          </Typography.Title>
          <Upload.Dragger showUploadList={false} multiple beforeUpload={(file: File) => beforeUpload(file, '2')}>
            <p className='reporting-upload-drag-icon'>
              <InboxOutlined />
            </p>
            <p>请拖拽文件到此处或点击上传文件按钮</p>
          </Upload.Dragger>
          {uploadedFileList.length > 0 && (
            <>
              <p style={{ padding: '4px 0' }}>资料文件：</p>
              {uploadedFileList.map(x => (
                <p key={x.id}>
                  <Tag
                    closeIcon
                    style={{ marginTop: 4 }}
                    onClose={() => {
                      setUploadedFileList(prevList => prevList.filter(y => y.id !== x.id))
                      return false
                    }}
                  >
                    {x.name}
                  </Tag>
                </p>
              ))}
            </>
          )}
          <div>
            {uploadedFile.id && (
              <>
                <p style={{ padding: '4px 0' }}>模板文件：</p>
                <Tag
                  closeIcon
                  onClose={() => {
                    setUploadedFile({ id: '', name: '' })
                    return false
                  }}
                >
                  {uploadedFile.name}
                </Tag>
              </>
            )}
          </div>
          <Button
            size='large'
            type='primary'
            style={{ width: '100%', marginTop: 20 }}
            disabled={!uploadedFile || generating}
            onClick={start}
          >
            {resValue.current.res1 && resValue.current.res2 ? '下 一 步' : '开 始 解 析'}
          </Button>
        </Card>
      </Flex>
    </>
  )
}

export default Template
