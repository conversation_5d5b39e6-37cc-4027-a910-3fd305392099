import { Card, Flex } from 'antd'
import StreamTypewriter from '@/component/StreamTypewriter'
import './Step2BasicInfo.less'

export const Step2BasicInfo: React.FC<{
  data: any
  generating: boolean
  onchange: () => void
}> = ({ data, generating, onchange }) => {
  return (
    <Card title='模板解析' className='lingua_doc_builder2-card'>
      <Flex vertical style={{ padding: '0 20px 0 20px' }}>
        <StreamTypewriter text={data} key={data} end={!generating} charsPerUpdate={10} onchange={onchange} />
      </Flex>
    </Card>
  )
}

export default Step2BasicInfo
