import React, { useCallback, useState } from 'react'
import { Tabs, Upload, But<PERSON>, Card, Modal, Space, message, Spin } from 'antd'
import StreamTypewriter from '@/component/StreamTypewriter'
import { extractJSONFromString } from '@/utils/json-extractor'
import Get<PERSON><PERSON> from '@/component/getKey'
import { UploadOutlined } from '@ant-design/icons'
import { prdPurcontractVer } from '@/api/prdPurcontractVer'
import { uploadFile } from '@/api/template'
import { extractContent } from '@/utils/common'

const { TabPane } = Tabs

interface FileState {
  file: File | null
  name: string
  id: string | number
}

const appkey = import.meta.env['VITE_PRD_PURCONTRACT_VER_TOKEN'] || ''

const PrdPurcontractVer: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [isLoading, setIsLoading] = useState(false)
  const [key, setKey] = useState('')
  const [open, setOpen] = useState(false)
  const [templateFile1, setTemplateFile1] = useState<FileState>({ file: null, name: '', id: '' })
  const [templateFile2, setTemplateFile2] = useState<FileState>({ file: null, name: '', id: '' })
  const [activeTab, setActiveTab] = useState('sContractTab')
  const [salesContract1, setSalesContract1] = useState(null)
  const [salesContract2, setSalesContract2] = useState(null)
  const [salesContract3, setSalesContract3] = useState(null)
  const [sContractBox, setSContractBox] = useState('')
  const [pContractBox, setPContractBox] = useState('')
  const [isFinished, setIsFinished] = useState(true)

  const generateMarkdown = (data: any, depth = 0, parentKey = ''): string => {
    // Handle empty/undefined/null data
    if (data === undefined || data === null) {
      const emptyStyle = `
        margin: 10px 10px 10px 0;
        background-color: #f5f5f5;
        color: #999;
        font-style: italic;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        border-radius: 5px;
        padding: 10px;
        width: calc(50% - 100px);
        display: inline-block;
        margin-right: 20px;
        vertical-align: top;
      `
        .replace(/\s+/g, ' ')
        .trim()

      return `<div style="${emptyStyle}">(空数据)</div>`
    }

    // Base case for primitive values
    if (typeof data !== 'object') {
      const boxStyle = `
        margin: 10px 10px 10px 0;
        background-color: #f9f9f9;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border-radius: 5px;
        padding: 10px;
        width: calc(50% - 100px);
        display: inline-block;
        margin-right: 20px;
        vertical-align: top;
      `
        .replace(/\s+/g, ' ')
        .trim()

      return `<div style="${boxStyle}">${String(data)}</div>`
    }

    // Handle arrays
    if (Array.isArray(data)) {
      // Handle empty array case
      if (data.length === 0) {
        return `<div style="
          padding: 10px;
          color: #666;
          font-style: italic;
          border-left: 3px solid #eee;
          margin: 5px 0;
        ">(空列表)</div>`
      }

      if (typeof data[0] === 'object' && data[0] !== null) {
        const allKeys = new Set<string>()
        data.forEach(item => {
          Object.keys(item).forEach(key => allKeys.add(key))
        })

        const keys = Array.from(allKeys)

        // Add empty cell styling for missing values
        const tableHeader = `| ${keys.join(' | ')} |\n| ${keys.map(() => '------').join(' | ')} |`
        const tableRows = data
          .map(
            item =>
              `| ${keys
                .map(key => (item[key] ? item[key] : `<span style="color:#999;font-style:italic;">(空值)</span>`))
                .join(' | ')} |`
          )
          .join('\n')

        const title = depth === 0 ? `# ${parentKey || 'Items'}\n\n` : ''
        return `${title}${tableHeader}\n${tableRows}`
      }

      return data.map(item => `- ${generateMarkdown(item, depth + 1)}`).join('\n')
    }

    // Handle objects
    let result = ''
    for (const [key, value] of Object.entries(data)) {
      if (value === undefined || value === null) {
        // Style for null/undefined values
        const emptyValueStyle = `
          margin: 10px 10px 10px 0;
          background-color: #f5f5f5;
          color: #999;
          font-style: italic;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
          border-radius: 5px;
          padding: 10px;
          width: calc(50% - 100px);
          display: inline-block;
          margin-right: 20px;
          vertical-align: top;
        `
          .replace(/\s+/g, ' ')
          .trim()

        result += `
          ​**​${key}​**​\n\n
          <div style="${emptyValueStyle}">
            (空值)
          </div>\n\n
        `.replace(/\n\s+/g, '\n')
        continue
      }

      if (typeof value === 'object') {
        result += `${depth === 0 ? '\n## ' : '\n### '}${key}\n\n${generateMarkdown(value, depth + 1, key)}\n\n`
      } else {
        const boxStyle = `
          margin: 10px 10px 10px 0;
          background-color: #f9f9f9;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border-radius: 5px;
          padding: 10px;
          width: calc(50% - 100px);
          display: inline-block;
          margin-right: 20px;
          vertical-align: top;
        `
          .replace(/\s+/g, ' ')
          .trim()

        result += `
          ​**​${key}​**​\n\n
          <div style="${boxStyle}">
            ${String(value)}
          </div>\n\n
        `.replace(/\n\s+/g, '\n')
      }
    }

    // Handle empty object case
    if (result === '' && depth > 0) {
      return `<div style="
        padding: 10px;
        color: #666;
        font-style: italic;
        border-left: 3px solid #eee;
        margin: 5px 0;
      ">(空对象)</div>`
    }

    return depth === 0 ? `\n\n${result}` : result
  }

  const handleTabChange = (key: string) => {
    if (!isFinished) {
      return
    }
    if (key === 'riskCheckTab') {
      if (!salesContract1) {
        Modal.warning({
          title: '提示',
          content: '请先上传销售合同文件'
        })
        return
      }
      if (!salesContract2) {
        Modal.warning({
          title: '提示',
          content: '请先上传采购合同文件'
        })
        return
      }
    }
    setActiveTab(key)
  }

  const beforeUpload1 = (file: File) => {
    setIsLoading(true)
    uploadFile(file, appkey).then(async response => {
      setIsLoading(false)
      if (response.id) {
        setTemplateFile1({
          file: file,
          name: file.name,
          id: response.id
        })
        handleGeneration1(response.id)
      } else {
        messageApi.open({
          key: 'uploading',
          type: 'error',
          content: '文件上传失败',
          duration: 1
        })
      }
    })
    return false
  }

  const beforeUpload2 = (file: File) => {
    setIsLoading(true)
    uploadFile(file, appkey).then(async response => {
      setIsLoading(false)
      if (response.id) {
        setTemplateFile2({
          file: file,
          name: file.name,
          id: response.id
        })
        handleGeneration2(response.id)
      } else {
        messageApi.open({
          key: 'uploading',
          type: 'error',
          content: '文件上传失败',
          duration: 1
        })
      }
    })
    return false
  }

  const handleGeneration1 = useCallback(
    async (id: string) => {
      if (!key) {
        setOpen(true)
        return
      }
      setIsLoading(true)
      setSalesContract1(null)
      setIsFinished(false)
      let messages = ''
      try {
        await prdPurcontractVer(
          {
            key,
            query: '默认参数1',
            type: '1',
            files: [
              {
                type: 'document',
                transfer_method: 'local_file',
                upload_file_id: id
              }
            ]
          },
          {
            onMessage: (text: string | null) => {
              if (text) {
                messages += text
              }
            },
            onError: () => {
              setIsLoading(false)
            },
            onFinish: () => {
              try {
                let obj = JSON.parse(extractJSONFromString(messages) || `${{ 提示: messages }}`)
                setSalesContract1(obj)
                let sContractBox = ''
                for (const [key, value] of Object.entries(obj)) {
                  if (typeof value === 'object') {
                    let str = JSON.stringify(value)
                    sContractBox += `${key}\n${str}\n\n`
                  } else {
                    sContractBox += `${key}\n${value || ' '}\n\n`
                  }
                }
                setSContractBox(sContractBox)
              } catch (err) {
                console.error('Error parsing messages:', err)
              }
              setIsLoading(false)
              const errorStr = extractContent(messages, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                setKey('')
                setOpen(true)
                return
              }
            }
          }
        )
      } catch (err) {
        setIsLoading(false)
      }
    },
    [key]
  )

  const handleGeneration2 = useCallback(
    async (id: string) => {
      if (!key) {
        setOpen(true)
        return
      }
      setIsLoading(true)
      setSalesContract2(null)
      setIsFinished(false)
      let messages = ''
      try {
        await prdPurcontractVer(
          {
            key,
            query: '默认参数2',
            type: '2',
            files: [
              {
                type: 'document',
                transfer_method: 'local_file',
                upload_file_id: id
              }
            ]
          },
          {
            onMessage: (text: string | null) => {
              if (text) {
                messages += text
              }
            },
            onError: () => {
              setIsLoading(false)
            },
            onFinish: () => {
              try {
                let obj = JSON.parse(extractJSONFromString(messages) || `${JSON.stringify({ 提示: messages })}`)
                setSalesContract2(obj)

                let pContractBox = ''
                for (const [key, value] of Object.entries(obj)) {
                  if (typeof value === 'object') {
                    let str = JSON.stringify(value)
                    pContractBox += `${key}\n${str}\n\n`
                  } else {
                    pContractBox += `${key}\n${value || ' '}\n\n`
                  }
                }
                setPContractBox(pContractBox)
              } catch (err) {
                console.error('Error parsing messages:', err)
              }
              setIsLoading(false)
              const errorStr = extractContent(messages, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                setKey('')
                setOpen(true)
                return
              }
            }
          }
        )
      } catch (err) {
        setIsLoading(false)
      }
    },
    [key, templateFile2]
  )

  const handleGeneration3 = useCallback(async () => {
    if (!key) {
      setOpen(true)
      return
    }
    setIsLoading(true)
    let messages = ''
    setSalesContract3(null)
    setIsFinished(false)
    try {
      await prdPurcontractVer(
        {
          key,
          query: `1、销售合同：\n ${sContractBox} \n 2、采购合同： \n ${pContractBox}`,
          type: '3'
        },
        {
          onMessage: (text: string | null) => {
            if (text) {
              messages += text
            }
          },
          onError: () => {
            setIsLoading(false)
          },
          onFinish: () => {
            try {
              setSalesContract3(JSON.parse(extractJSONFromString(messages) || `${JSON.stringify({ 提示: messages })}`))
            } catch (err) {
              console.error('Error parsing messages:', err)
            }
            setIsLoading(false)
            const errorStr = extractContent(messages, 'error')
            if (errorStr) {
              messageApi.error(errorStr)
              setKey('')
              setOpen(true)
              return
            }
          }
        }
      )
    } catch (err) {
      setIsLoading(false)
    }
  }, [key, sContractBox, pContractBox])

  return (
    <div style={{ padding: 24, background: '#f0f2f5', height: '100%' }}>
      <Spin tip='加载中' spinning={isLoading} fullscreen size='large' />
      {contextHolder}
      <GetKey open={open} onClose={setOpen} onChange={setKey} />
      <Card>
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab='销售合同' key='sContractTab'>
            <Space direction='vertical' style={{ width: '100%' }}>
              <Upload accept='.pdf' showUploadList={false} beforeUpload={file => beforeUpload1(file)}>
                <Button type='primary' icon={<UploadOutlined />}>
                  上传销售合同
                </Button>
                {templateFile1.name && <span style={{ marginLeft: 8 }}>{templateFile1.name}</span>}
              </Upload>
              <Card title='销售合同信息'>
                {salesContract1 && (
                  <StreamTypewriter
                    speed={1}
                    text={generateMarkdown(salesContract1)}
                    end={!isLoading}
                    onFinished={() => {
                      setIsFinished(true)
                    }}
                    charsPerUpdate={4}
                  />
                )}
              </Card>
            </Space>
          </TabPane>

          <TabPane tab='采购合同' key='pContractTab'>
            <Space direction='vertical' style={{ width: '100%' }}>
              <Upload accept='.pdf' showUploadList={false} beforeUpload={file => beforeUpload2(file)}>
                <Button type='primary' icon={<UploadOutlined />}>
                  上传采购合同
                </Button>
                {templateFile2.name && <span style={{ marginLeft: 8 }}>{templateFile2.name}</span>}
              </Upload>

              <Card title='采购合同信息'>
                {salesContract2 && (
                  <StreamTypewriter
                    speed={1}
                    text={generateMarkdown(salesContract2)}
                    end={!isLoading}
                    onFinished={() => {
                      setIsFinished(true)
                    }}
                    charsPerUpdate={4}
                  />
                )}
              </Card>
            </Space>
          </TabPane>

          <TabPane tab='风险校验结果' key='riskCheckTab'>
            <Card
              title='风险校验结果'
              extra={
                <Button type='primary' onClick={handleGeneration3}>
                  执行风险校验
                </Button>
              }
            >
              {salesContract3 && (
                <StreamTypewriter
                  speed={1}
                  text={generateMarkdown(salesContract3)}
                  end={!isLoading}
                  onFinished={() => {
                    setIsFinished(true)
                  }}
                />
              )}
            </Card>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  )
}

export default PrdPurcontractVer
