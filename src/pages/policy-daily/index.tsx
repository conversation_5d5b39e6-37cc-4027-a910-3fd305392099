import React, { useCallback, useState, useMemo } from 'react'
import { Input, Typography, Layout, Card, Spin, Flex, Empty, ConfigProvider, message } from 'antd'
import { generateDailyReport } from '@/api/policy'
import { extractContent } from '@/utils/common'
import GetKey from '@/component/getKey'
import './index.less'

const { Title, Link, Text } = Typography
const { Content } = Layout

interface PolicyItem {
  title: string
  url: string
  content?: string
  publishTime?: string
}

interface SearchParams {
  Question: string
  start_date?: string
  end_date?: string
  key?: string
}

export const PolicyDaily: React.FC = () => {
  const [key, setKey] = useState('')
  const [open, setOpen] = useState(false)
  const [generating, setGenerating] = useState<boolean>(false)
  const [messages, setMessages] = useState<PolicyItem[]>([])
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [timeRange, setTimeRange] = useState<[any | null, any | null]>([null, null])

  const highlightText = useCallback((text: string, keywords: string[]) => {
    if (!text || keywords.length === 0) return text
    const regex = new RegExp(`(${keywords.map(k => k.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|')})`, 'gi')
    return text.replace(regex, match => `<mark style="background-color: yellow; color: black;">${match}</mark>`)
  }, [])

  const handleTimeRangeChange = (dates: [any | null, any | null]) => {
    setTimeRange(dates)
  }

  const handleGeneration = useCallback(
    async (query: string, start_date?: string, end_date?: string) => {
      if (!key) {
        setOpen(true)
        return
      }
      if (!query) return
      setGenerating(true)
      setMessages([])
      setSearchQuery(query)

      try {
        const searchParams: SearchParams = {
          Question: query,
          start_date: '2024-11-25',
          end_date: '2025-02-25',
          key: key || ''
        }
        let str = ''

        await generateDailyReport(searchParams, {
          onMessage: (text, finished) => {
            if (text) {
              str += text
              try {
                const list = JSON.parse(text)
                setMessages(list)
              } catch (e) {
                console.error('Error parsing message:', e)
              }
            }
            if (finished) {
              setGenerating(false)
              const errorStr = extractContent(str, 'error')
              if (errorStr) {
                message.error(errorStr)
                setKey('')
                setOpen(true)
                return
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {
            setGenerating(false)
          }
        })
      } catch (err) {
        setGenerating(false)
      }
    },
    [key]
  )

  const handleSearch = () => {
    const start_date = timeRange[0] ? timeRange[0].format('YYYY-MM-DD') : undefined
    const end_date = timeRange[1] ? timeRange[1].format('YYYY-MM-DD') : undefined

    handleGeneration(searchQuery, start_date, end_date)
  }

  const highlightedMessages = useMemo(() => {
    const keywords = searchQuery.split(/\s+/).filter(Boolean)

    return messages.map((item: any, index) => (
      <Card
        hoverable
        key={index}
        onClick={() => window.open(item.url)}
        style={{
          marginBottom: '10px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
        }}
      >
        <Title
          level={4}
          style={{
            paddingBottom: '10px',
            fontSize: '16px',
            wordBreak: 'break-all'
          }}
        >
          <span dangerouslySetInnerHTML={{ __html: highlightText(item.title, keywords) }} />
        </Title>

        {item.content && (
          <div
            style={{
              marginBottom: '10px',
              color: 'rgba(0,0,0,0.65)',
              fontSize: '14px',
              // overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical'
            }}
          >
            <span
              dangerouslySetInnerHTML={{
                __html: highlightText(item.content, keywords)
              }}
            />
          </div>
        )}

        <Text
          type='secondary'
          style={{
            fontSize: '12px',
            marginBottom: '8px',
            display: 'block',
            color: 'rgba(0,0,0,0.45)'
          }}
        >
          <span dangerouslySetInnerHTML={{ __html: highlightText(item.summary, keywords) }} />
        </Text>

        <Flex justify='space-between' align='center' style={{ marginTop: '10px' }}>
          <Text
            type='secondary'
            style={{
              fontSize: '12px',
              color: 'rgba(0,0,0,0.45)'
            }}
          >
            {item.pubtimeStr}
          </Text>

          <Link href={item.url} target='_blank' style={{ fontSize: '12px' }}>
            查看原文
          </Link>
        </Flex>
      </Card>
    ))
  }, [messages, searchQuery, highlightText])

  return (
    <ConfigProvider
      theme={{
        components: {
          Input: {
            fontSize: 14,
            paddingBlock: 8
          },
          DatePicker: {
            // 设置移动端专用样式
            fontSize: 14,
            controlHeight: 48,
            borderRadius: 4
          }
        }
      }}
    >
      <GetKey open={open} onClose={setOpen} onChange={setKey} />
      <div
        style={{
          width: '100%',
          padding: '0 15px'
        }}
      >
        <Spin tip='加载中' spinning={generating} fullscreen size='large' />

        <Flex className='toolbar' justify='center' style={{ marginBottom: '15px' }}>
          <Typography.Text className='title-text' style={{ fontSize: '18px', fontWeight: 600 }}>
            政策资讯
          </Typography.Text>
        </Flex>

        <Layout>
          <Content style={{ width: '100%', padding: 0 }}>
            <Flex gap='middle' style={{ marginBottom: '15px' }}>
              <Input.Search
                id='search-input'
                placeholder='输入方向，例如信托'
                enterButton
                style={{ width: '100%' }}
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                onSearch={handleSearch}
              />
            </Flex>

            {/* <Flex gap="middle" style={{ marginBottom: '15px' }}>  
              <RangePicker   
                placeholder={['开始日期', '结束日期']}  
                onChange={handleTimeRangeChange}  
                style={{ width: '100%' }}  
                allowClear  
                bordered  
                size="middle"
                
              />  
            </Flex>   */}

            {(searchQuery || (timeRange[0] && timeRange[1])) && (
              <Flex
                justify='space-between'
                align='center'
                style={{
                  marginBottom: '15px',
                  padding: '10px',
                  backgroundColor: '#f5f5f5',
                  borderRadius: '4px'
                }}
              >
                {searchQuery && (
                  <Text
                    strong
                    style={{
                      fontSize: '12px',
                      // overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    关键词：{searchQuery}
                  </Text>
                )}
                {timeRange[0] && timeRange[1] && (
                  <Text
                    type='secondary'
                    style={{
                      fontSize: '12px',
                      // overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {timeRange[0].format('YYYY-MM-DD')}至{timeRange[1].format('YYYY-MM-DD')}
                  </Text>
                )}
              </Flex>
            )}

            <div className='messages'>
              {highlightedMessages.length > 0 ? highlightedMessages : <Empty description='暂无数据' />}
            </div>
          </Content>
        </Layout>
      </div>
    </ConfigProvider>
  )
}

export default PolicyDaily
