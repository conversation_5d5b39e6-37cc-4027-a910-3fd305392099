import { useCallback, useState, useEffect } from "react";
import {
  Upload,
  Button,
  Typo<PERSON>,
  Card,
  Spin,
  Flex,
  message,
  Table,
} from "antd";
import {
  CheckCircleFilled,
  InboxOutlined,
  LoadingOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import { summaryContract } from "@/api/summary";
import { uploadFile } from "@/api/template";
import "./index.less";

const SUMMARY_AGENT_TOKEN = import.meta.env['VITE_SUMMARY_AGENT_TOKEN'] || "";

export const TableSummary = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const [uploadedFiles, setUploadedFiles] = useState<
    { id: string; name: string }[]
  >([]);
  const [startSending, setStartSending] = useState<boolean>(false);
  const [generating, setGenerating] = useState<boolean>(false);
  const [messages, setMessages] = useState<string>("");
  const [tableList, setTableList] = useState<any[]>([]);

  useEffect(() => {
    if (uploadedFiles.length > 0) {
      messageApi.open({
        key: "uploading",
        type: "success",
        content: "文件上传成功",
        duration: 1,
      });
    }
  }, [uploadedFiles, messageApi]);

  const beforeUpload = (file: File) => {
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf(".") + 1)
      ?.toLowerCase();
    if (
      ["pdf", "docx", "xlsx", "pptx", "xls", "csv", "txt"].includes(
        originalFileExt
      )
    ) {
      messageApi.open({
        key: "uploading",
        type: "loading",
        content: "文件上传中",
      });

      uploadFile(file, SUMMARY_AGENT_TOKEN).then(async (response) => {
        if (response.id) {
          setUploadedFiles((prevFiles) => [...prevFiles, response]);
        } else {
          messageApi.open({
            key: "uploading",
            type: "error",
            content: "文件上传失败",
            duration: 1,
          });
        }
      });
    } else {
      messageApi.error(
        "目前仅支持.docx，.pdf, .xlsx, .pptx, .xls, .csv, .txt类型的文件，请您将文件转成这些格式后再次进行上传"
      );
    }
    return false; // 阻止自动上传
  };

  const handleDelete = (fileId: string) => {
    setUploadedFiles((prevFiles) =>
      prevFiles.filter((file) => file.id !== fileId)
    );
  };

  const handleGenerationStart = () => {
    setStartSending(true);
    const fileIds = uploadedFiles.map((file) => file.id);
    handleGeneration(fileIds);
  };

  const handleGeneration = useCallback(async (fileIds: string[]) => {
    setGenerating(true);
    let accumulatedMessages = "";

    try {
      await summaryContract(
        {
          files: fileIds.map((x) => ({
            type: "document",
            transfer_method: "local_file",
            upload_file_id: x,
          })),
        },
        {
          onMessage: (text: string | null, finished: boolean) => {
            if (text) {
              accumulatedMessages += text;
            }
            if (finished) {
              console.log(accumulatedMessages);
              setGenerating(false);

              // // 使用正则表达式匹配 JSON 数组部分
              // const jsonRegex = /json\s+(\[.*?\])/s
              // const match = accumulatedMessages.match(jsonRegex)

              // if (!match) return null

              try {
                // const jsonString = match[1].replace(/“|”/g, '"').replace(/\n/g, '')
                const list = JSON.parse(accumulatedMessages);
                console.log(list);
                list.forEach((item: { key: any }, index: any) => {
                  item.key = index;
                });
                setTableList(list);
              } catch (e) {
                console.log(accumulatedMessages);
                setMessages(accumulatedMessages);
              }
            }
          },
          onError: () => {
            setGenerating(false);
          },
          onFinish: () => {
            setGenerating(false);
          },
        }
      );
    } catch (err) {
      setGenerating(false);
    }
  }, []);

  const columns = [
    {
      title: "系统类别（大类）",
      dataIndex: "systemCb",
      key: "systemCb",
      minWidth: 150,
    },
    {
      title: "系统类别（小类）",
      dataIndex: "systemCs",
      key: "systemCs",
      minWidth: 150,
    },
    {
      title: "系统编号",
      dataIndex: "systemNo",
      key: "systemNo",
      minWidth: 150,
    },
    {
      title: "应用系统名称",
      dataIndex: "systemName",
      key: "systemName",
      minWidth: 150,
    },
    {
      title: "ESB服务名称",
      dataIndex: "esbName",
      key: "esbName",
      minWidth: 150,
    },
    {
      title: "应用系统功能描述",
      dataIndex: "systemDes",
      key: "systemDes",
      minWidth: 150,
    },
    {
      title: "应用业务部门",
      dataIndex: "systemDep",
      key: "systemDep",
      minWidth: 150,
    },
    {
      title: "部署机房",
      dataIndex: "systemRoom",
      key: "systemRoom",
      minWidth: 150,
    },
    {
      title: "部署模式",
      dataIndex: "systemMode",
      key: "systemMode",
      minWidth: 150,
    },
    {
      title: "灾备建设情况",
      dataIndex: "systemZbDrcs",
      key: "systemZbDrcs",
      minWidth: 150,
    },
    // { title: '系统架构', dataIndex: 'systemArch', key: 'systemArch', minWidth: 150 },
    // { title: '负载均衡', dataIndex: 'systemLoad', key: 'systemLoad', minWidth: 150 },
    // { title: 'DNS访问支持', dataIndex: 'systemDns', key: 'systemDns', minWidth: 150 },
    // {
    //   title: '是否向市县行社提供服务',
    //   dataIndex: 'systemWtps',
    //   key: 'systemWtps',
    //   minWidth: 150
    // },
    // {
    //   title: '目前行社访问方式',
    //   dataIndex: 'systemMof',
    //   key: 'systemMof',
    //   minWidth: 150
    // },
    // { title: '网络访问', dataIndex: 'systemAccess', key: 'systemAccess', minWidth: 150 },
    // {
    //   title: '依赖系统和关联情况简述',
    //   dataIndex: 'systemDa',
    //   key: 'systemDa',
    //   minWidth: 150
    // },
    // {
    //   title: '停机迁移窗口时间（HH:MM-HH:MM）',
    //   dataIndex: 'downtime',
    //   key: 'downtime',
    //   minWidth: 150
    // },
    // {
    //   title: '是否允许停机（HH:MM-HH:MM）',
    //   dataIndex: 'ifDowntime',
    //   key: 'ifDowntime',
    //   minWidth: 150
    // },
    // { title: '监管报送窗口（如有）', dataIndex: 'rrw', key: 'rrw', minWidth: 150 },
    // {
    //   title: '是否监管报备、报备机构',
    //   dataIndex: 'systemJgDrcs',
    //   key: 'systemJgDrcs',
    //   minWidth: 150
    // },
    // {
    //   title: '系统等级（关键、重要、一般）',
    //   dataIndex: 'systemDrcs',
    //   key: 'systemDrcs',
    //   minWidth: 150
    // },
    // { title: 'RTO', dataIndex: 'RTO', key: 'RTO' },
    // { title: 'RPO', dataIndex: 'RPO', key: 'RPO', minWidth: 150 },
    // { title: '应用负责人', dataIndex: 'appOwner', key: 'appOwner', minWidth: 150 },
    // { title: '建设人', dataIndex: 'builder', key: 'builder', minWidth: 150 },
    // {
    //   title: '应用开发商',
    //   dataIndex: 'appDeveloper',
    //   key: 'appDeveloper',
    //   minWidth: 150
    // },
    // { title: '应用维护商', dataIndex: 'apppVendor', key: 'apppVendor', minWidth: 150 },
    // { title: '上线日期', dataIndex: 'launchDate', key: 'launchDate', minWidth: 150 },
    // {
    //   title: '运行状态',
    //   dataIndex: 'operationStatus',
    //   key: 'operationStatus',
    //   minWidth: 150
    // },
    // {
    //   title: '应用开发商维护人员信息（姓名、电话、邮箱）',
    //   dataIndex: 'appNPM',
    //   key: 'appNPM',
    //   minWidth: 150
    // },
    // { title: '应用维保截止时间', dataIndex: 'appExpiryDate', key: 'appExpiryDate', minWidth: 150 }
  ];

  return (
    <>
      {contextHolder}
      <Spin tip="加载中" spinning={generating} fullscreen size="large" />

      <Flex className="toolbar" justify="center">
        <Typography.Text className="title-text">材料汇总工具</Typography.Text>
      </Flex>

      <div style={{ width: "100vw", overflow: "hidden", padding: "0 20px" }}>
        <Card className="template-form">
          <Flex vertical gap="middle">
            <Upload.Dragger
              multiple
              showUploadList={false}
              beforeUpload={beforeUpload}
            >
              <div className="ant-upload-drag-icon">
                {uploadedFiles.length > 0 ? (
                  <CheckCircleFilled />
                ) : (
                  <InboxOutlined />
                )}
              </div>
              <div className="ant-upload-hint">
                {uploadedFiles.length > 0 ? (
                  "点击或者将文件拖拽到这里重新上传"
                ) : (
                  <span>在这里上传您的文件，让AI帮您进行解析</span>
                )}
              </div>
              <div className="ant-upload-text">
                {uploadedFiles.length > 0
                  ? uploadedFiles.map((file, index) => (
                      <div
                        key={file.id}
                        style={{ display: "flex", alignItems: "center" }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Button type="link">
                          {index + 1 + "." + file.name}
                          <DeleteOutlined
                            onClick={() => handleDelete(file.id)}
                          />
                        </Button>
                      </div>
                    ))
                  : "点击或者将文件拖拽到这里进行上传"}
              </div>
            </Upload.Dragger>
            <Button
              size="large"
              type="primary"
              disabled={uploadedFiles.length === 0 || generating}
              onClick={handleGenerationStart}
            >
              开 始 校 验
            </Button>
          </Flex>
        </Card>

        {startSending && (
          <Flex
            align="center"
            justify="center"
            style={{ marginTop: 15, width: "100%", overflow: "auto" }}
          >
            {tableList.length > 0 ? (
              <Table
                columns={columns}
                dataSource={tableList}
                rowKey={(record) => record.key}
                pagination={false}
                bordered
              />
            ) : (
              <Flex justify="justify" align="center" vertical>
                <Typography.Text>
                  {messages || "正在提取文件信息，请不要关闭或刷新页面"}
                </Typography.Text>
              </Flex>
            )}
          </Flex>
        )}
      </div>
    </>
  );
};

export default TableSummary;
