import { useState, useEffect, useCallback } from 'react'
import { Upload, Button, Typography, Card, Spin, message, Form, Flex } from 'antd'
import { CheckCircleFilled, InboxOutlined, DeleteOutlined } from '@ant-design/icons'
import { uploadFile, convertFileToPDF } from '@/api/template'
import { contractTools } from '@/api/contractTools'
import StreamTypewriter from '@/component/StreamTypewriter'
import { NoData } from '@/component/NoData'
import GetKey from '@/component/getKey'
import { extractContent } from '@/utils/common'
import './index.less'

const CONTRACT_TOOLS_TOKEN = import.meta.env['VITE_CONTRACT_TOOLS_TOKEN'] || ''

export const ContractTools = () => {
  const [key, setKey] = useState('')
  const [open, setOpen] = useState(false)
  const [messageApi, contextHolder] = message.useMessage()
  const [contractContent, setContractContent] = useState<string>('')
  const [uploadedFiles, setUploadedFiles] = useState<{ id: string; name: string }[]>([])
  const [startSending, setStartSending] = useState<boolean>(false)
  const [generating, setGenerating] = useState<boolean>(false)
  const [markdownTable, setMarkdownTable] = useState('')
  const [messagesEnd, setMessagesEnd] = useState<boolean>(false)

  const [messages, setMessages] = useState<string>('')

  const beforeUpload = (file: File) => {
    if (!key) {
      setOpen(true)
      return false
    }
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)?.toLowerCase()
    if (['pdf', 'docx'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中'
      })
      uploadFile(file, CONTRACT_TOOLS_TOKEN).then(async response => {
        if (response.id) {
          setUploadedFiles(prevFiles => [...prevFiles, response])
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
        } else {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件上传失败',
            duration: 1
          })
        }
      })
    } else {
      messageApi.error('目前仅支持.docx, .pdf类型的文件，请您将文件转成这些格式后再次进行上传')
    }
    return false
  }

  const handleDelete = (fileId: string) => {
    setUploadedFiles(prevFiles => prevFiles.filter(file => file.id !== fileId))

    messageApi.open({
      key: 'uploading',
      type: 'success',
      content: '文件删除成功',
      duration: 1
    })
  }

  const handleGenerationStart = () => {
    // if (!projectName.trim()) {
    //   messageApi.error('请输入项目名称')
    //   return
    // }
    if (uploadedFiles.length === 0) {
      messageApi.error('请上传至少一个文件')
      return
    }
    setStartSending(true)
    const fileIds = uploadedFiles.map(file => file.id)
    handleGeneration(fileIds)
    setMarkdownTable('')
  }
  const handleGeneration = useCallback(
    async (fileIds: string[]) => {
      if (!key) {
        setOpen(true)
        return
      }
      setGenerating(true)
      let accumulatedMessages = ''

      try {
        await contractTools(
          {
            key,
            query: contractContent ? contractContent : '1',
            type: fileIds.length > 1 ? '2' : '1',
            files: fileIds.map(x => ({
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: x
            }))
          },
          {
            onMessage: (text: string | null, finished: boolean) => {
              if (text) {
                setGenerating(false)
                accumulatedMessages += text
                const cleanedData = accumulatedMessages.replace(/^```markdown\s*|```$/g, '')
                setMarkdownTable(cleanedData)
              }
              if (finished) {
                setMessagesEnd(true)
                const errorStr = extractContent(accumulatedMessages, 'error')
                if (errorStr) {
                  messageApi.error(errorStr)
                  setKey('')
                  setOpen(true)
                  return
                }
                try {
                  // const list = JSON.parse(accumulatedMessages)
                  // list.forEach((item: { key: any }, index: any) => {
                  //   item.key = index
                  // })
                  // setTableList(list)
                  // const cleanedData = accumulatedMessages.replace(
                  //   /^```markdown\s*|```$/g,
                  //   ''
                  // )
                  // setMarkdownTable(cleanedData)
                } catch (e) {
                  console.log(accumulatedMessages)
                  setMessages(accumulatedMessages)
                }
              }
            },
            onError: () => {
              setGenerating(false)
              setMessagesEnd(true)
            },
            onFinish: () => {
              setGenerating(false)
              setMessagesEnd(true)
            }
          }
        )
      } catch (err) {
        setGenerating(false)
        setMessagesEnd(true)
      }
    },
    [contractContent, key]
  )

  return (
    <>
      {contextHolder}
      <Spin tip='处理中...' spinning={generating} fullscreen size='large' />
      <GetKey open={open} onClose={setOpen} onChange={setKey} />

      <div className='contract-tools-container'>
        {/* 左侧面板 */}
        <div className='contract-left-panel'>
          <Typography.Text className='title-text'>合同工具</Typography.Text>
          {/* <Card className="input-section"> */}
          <Form layout='vertical'>
            {/* <Form.Item label="分析内容">
              <Input
                placeholder="请输入分析内容"
                value={contractContent}
                onChange={(e) => setContractContent(e.target.value)}
              />
            </Form.Item> */}
            <Form.Item label='上传文件'>
              <Upload.Dragger multiple showUploadList={false} beforeUpload={beforeUpload}>
                <div className='ant-upload-drag-icon'>
                  {uploadedFiles.length > 0 ? <CheckCircleFilled /> : <InboxOutlined />}
                </div>
                <div className='ant-upload-hint'>
                  <span>拖拽文件到此处上传</span>
                  <br />
                  <span style={{ fontSize: '12px', color: '#999' }}>或点击选择文件</span>
                </div>
              </Upload.Dragger>

              {uploadedFiles.length > 0 && (
                <div className='file-list-contract'>
                  {uploadedFiles.map(file => (
                    <div key={file.id} className='file-item'>
                      <span style={{ flex: 1 }}>{file.name}</span>
                      <DeleteOutlined
                        onClick={() => handleDelete(file.id)}
                        style={{ cursor: 'pointer', flex: '0 0 20px' }}
                      />
                    </div>
                  ))}
                </div>
              )}
            </Form.Item>
            <Form.Item>
              <Button
                type='primary'
                disabled={uploadedFiles.length === 0 || generating}
                onClick={handleGenerationStart}
                style={{ width: '100%' }}
              >
                点击生成
              </Button>
            </Form.Item>
          </Form>
          {/* </Card> */}
        </div>

        {/* 右侧面板 */}
        <div className='contract-right-panel'>
          <div className='contract-action-buttons'>
            <Typography.Text className='contract-section-title'>合同分析</Typography.Text>
          </div>

          {startSending && (
            <Flex
              justify='center'
              style={{
                marginTop: 15,
                overflow: 'auto',
                width: '100%'
              }}
            >
              {markdownTable ? (
                <Card style={{ width: '100%', overflow: 'auto' }}>
                  <StreamTypewriter text={markdownTable} end={messagesEnd} />
                </Card>
              ) : (
                <Flex justify='justify' align='center' vertical>
                  <Typography.Text>{messages || '正在提取文件信息，请不要关闭或刷新页面'}</Typography.Text>
                </Flex>
              )}
            </Flex>
          )}
          {!startSending && <NoData description=' ' text='暂无内容' />}
        </div>
      </div>
    </>
  )
}

export default ContractTools
