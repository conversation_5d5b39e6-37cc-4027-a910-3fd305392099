import {useCallback, useState} from 'react'
import {<PERSON>ton, Card, Flex, Input, Layout, message, Popover, Spin, Typography} from 'antd'
import {getKnowledgeAnswer} from '@/api/regulatorySearch.ts'
import StreamTypewriter from '@/component/StreamTypewriter'

const {Header, Content} = Layout

export const RegulatorySearch = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [inputValue, setInputValue] = useState('')
  const [generating, setGenerating] = useState<boolean>(false)
  const [messages, setMessages] = useState<string>('')
  const [title, setTitle] = useState<string>('')
  const [key, setKey] = useState<string>('')
  const [retrievalMetadata, setRetrievalMetadata] = useState<{
    position: number,
    document_name: string,
    content: string
  }[] | null>(null)

  const handleInputChange = (event: any) => {
    setInputValue(event.target.value)
  }

  const handleSubmit = useCallback(async () => {
    if (!inputValue) {
      messageApi.open({
        key: 'uploading',
        type: 'error',
        content: '请输入查询内容',
        duration: 1
      })
      return
    }
    setTitle(inputValue)
    setInputValue('')
    setMessages('回答：')
    setGenerating(true)
    setKey(`${Date.now()}`)
    try {
      await getKnowledgeAnswer(inputValue, {
        onMessage: (text, finished) => {
          if (text) {
            setMessages(prev => prev + text)
          }
          if (finished) {
            setGenerating(false)
          }
        },
        onError: () => {
          setGenerating(false)
        },
        onFinish: (data) => {

          setRetrievalMetadata(data?.metadata?.retriever_resources)
        }
      })
    } catch (err) {
      setGenerating(false)
    }
  }, [inputValue])

  return (
    <>
      {contextHolder}
      <Spin tip='加载中' spinning={generating} fullscreen size='large'/>
      <Layout style={{height: '100vh', overflow: 'hidden'}}>
        <Header style={{backgroundColor: '#3d93e5', textAlign: 'center'}}>
          <h1 style={{color: 'white', margin: 0, fontSize: '20px', fontWeight: 'bold'}}>制度检索助手</h1>
        </Header>

        <Content style={{padding: '24px', flex: 1}}>
          <Flex vertical justify='space-between' style={{height: 'calc(100vh - 100px)'}}>
            <Card style={{marginBottom: '16px', height: 'calc(100vh - 180px)', overflow: 'auto'}}>
              <div style={{color: 'rgba(0, 0, 0, 0.45)'}}>
                {title && <h1 style={{margin: 0, fontSize: '18px', fontWeight: 'bold'}}>问题：{title}</h1>}
                <StreamTypewriter text={messages} key={key} end={!generating}/>
                {retrievalMetadata != null &&
                  <div>
                    <Flex vertical gap={"small"}>
                      <Typography>引用：</Typography>
                      {retrievalMetadata.map((item) => {
                        return <Popover autoAdjustOverflow placement="topLeft"
                                        content={item.content} key={item.position}>
                          <Typography.Text strong>
                            {item.document_name.substring(0, item.document_name.indexOf(".pdf"))}
                          </Typography.Text>
                        </Popover>
                      })}
                    </Flex>
                  </div>

                }
              </div>
            </Card>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                backgroundColor: '#f0f0f0',
                padding: '16px',
                borderRadius: '8px'
              }}
            >
              <Input
                placeholder='请输入您想要查询的内容...'
                value={inputValue}
                onChange={handleInputChange}
                style={{flex: 1, marginRight: '16px'}}
              />
              <Button type='primary' onClick={handleSubmit} style={{width: '70px'}}>
                发送
              </Button>
            </div>
          </Flex>
        </Content>
      </Layout>
    </>
  )
}

export default RegulatorySearch
