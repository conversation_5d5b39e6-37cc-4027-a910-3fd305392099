// 只针对开发流程页面的全屏 Spin
.develop-process-page {
  .ant-spin-fullscreen {
    overflow-y: auto !important;
    .ant-spin-container {
      overflow-y: auto !important;
    }
  }
}
.develop-process-container {
  display: flex;
  gap: 24px;
  // max-width: 1600px;
  margin: 0 auto;
  // height: 100vh;
  height: 100%;
  overflow: auto;
  // 左侧面板样式
  .develop-left-panel {
    // height:100%;
    height: fit-content;
    width: 25%;
    min-width: 25%;
    background: #ffffff;
    padding: 24px;    
    .title-text {
      font-size: 24px;
      font-weight: 600;
      text-align: center;
      color: #1a1a1a;
      padding-bottom: 8px;
      display: block;
    }
    .cursor-pointer{
      .text-green-600{
        color: #2ecc71;
      }
      .text-blue-600{
        color: #3498db;
      }
      .text-muted-foreground{
        color: #6c757d;
      }
      .menu-sts{
        color:#1890ff;
        font-size: 14px;
        margin-left: 10px;
      }
      
    }
  }

  // 右侧面板样式
  .develop-right-panel {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin: 32px 32px 32px 8px;
    position: relative;
    .edit-cm-code{
      height: calc(100vh - 110px);
      overflow-y: auto;
      .cm-editor{
        height: auto;
      }
    }
    .develop-action-buttons{
      display: flex;
      gap: 12px;
      justify-content: space-between;
      .ant-btn{
        margin-left:12px;
      }
      
    }
    
    .develop-output-section {
      flex: 1;
      background: #fff;
      border-radius: 12px;
      // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

      .ant-card-body {
        // height: auto;
        padding: 24px;
        display: flex;
        flex-direction: column;
      }
    }
    .develop-markdown-body table {
      border-collapse: collapse;
      width: 100%;
    }
    
    .develop-markdown-body th,
    .develop-markdown-body td {
      border: 1px solid #ccc; /* 设置边框颜色 */
      padding: 8px; /* 设置单元格内边距 */
      text-align: left; /* 设置文本对齐方式 */
    }
    
    .develop-markdown-body th {
      background-color: #f2f2f2; /* 设置表头背景色 */
    }
    .develop-markdown-body tr td:nth-child(1){
      min-width: 90px;
    }
  }
}

.develop-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
  display: block;
}

// 文件列表样式
.file-list {
  margin-top: 16px;
  max-height: 200px;
  overflow-y: auto;
  padding-right: 4px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f5f5;
    border-radius: 3px;
  }

  .file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 8px;
    transition: all 0.3s ease;

    &:hover {
      background: #f0f2f5;
    }

    span {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 12px;
    }
  }
}

// 输入框样式
.ant-input {
  border-radius: 8px;
  
  &:hover, &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }
}

:where(.css-dev-only-do-not-override-7ny38l).ant-menu-light.ant-menu-root.ant-menu-inline{
  border-inline-end: 0px solid rgba(5, 5, 5, 0.06);
}

