import React, { useEffect, useRef, useState } from "react";
import mermaid from "mermaid";

mermaid.initialize({ startOnLoad: false });

const Mermaid: React.FC<{ chart: string }> = ({ chart }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window === "undefined" || !containerRef.current) return;

    try {
      const id = `mermaid-${Math.random().toString(36).slice(2)}`;
      mermaid.render(id, chart, (svgCode) => {
        if (containerRef.current) {
          containerRef.current.innerHTML = svgCode;
        }
      });
    } catch (err: any) {
      console.error("Mermaid 渲染失败:", err);
      setError(err.message || "未知错误");
    }
  }, [chart]);

  return (
    <div className="mermaid-wrapper">
      {error ? (
        <pre style={{ color: "red" }}>Mermaid 渲染失败：{error}</pre>
      ) : (
        <div ref={containerRef} />
      )}
    </div>
  );
};

export default Mermaid;
