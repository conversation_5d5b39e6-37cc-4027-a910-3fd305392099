.regulatory-reporting-container {
  height: 100vh;
  background-color: #f0f2f5;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  overflow: auto;
  .regulatory-reporting-card {
    width: 100%;
    max-width: 600px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    transition: all 0.3s ease;
    margin: auto;
    flex: 1;

    .page-title {
      margin-bottom: 16px;
      color: #1f1f1f;
      display: flex;
      align-items: center;
      gap: 8px;

      .anticon {
        font-size: 24px;
        color: #1890ff;
      }
    }

    .page-description {
      display: block;
      font-size: 14px;
    }
    .reporting-upload-drag-icon {
      font-size: 36px;
      color: #1890ff;
      transition: transform 0.3s ease;
    }
    .markdown-body ol,
    .markdown-body ul {
      padding-left: 20px;
    }
  }
}
