import React, { useState } from 'react'
import { Upload, Button, message, Card, Typography, Spin, Layout, Flex, Tag, Table } from 'antd'
import { InboxOutlined } from '@ant-design/icons'
import { regulatoryReporting } from '@/api/regulatoryReporting'
import { uploadFile } from '@/api/template'
import { extractContent } from '@/utils/common'
import StreamTypewriter from '@/component/StreamTypewriter'
import * as XLSX from 'xlsx'
import './index.less'

const { Title, Text } = Typography
const token = import.meta.env['VITE_REGULATORY_REPORTING_TOKEN'] || ''

const DynamicTable = ({ data }: { data: Record<string, any>[] }) => {
  // 如果数据为空，返回空
  if (!data || data.length === 0) {
    return <div>暂无数据</div>
  }

  // 从第一条数据中提取所有键作为列配置
  const columns = Object.keys(data[0]).map(key => ({
    title: key,
    dataIndex: key,
    key: key,
    ellipsis: true,
    minWidth: 200
  }))

  return (
    <Table
      columns={columns}
      dataSource={data}
      pagination={false}
      scroll={{ x: 'max-content', y: 500 }}
      bordered
      size='middle'
      // 可以添加更多的表格配置
    />
  )
}

export const RegulatoryReporting: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState(false)
  const [mk, setMk] = useState('')
  const [isShowMk, setIsShowMk] = useState(false)
  const [jsonData, setJsonData] = useState<Record<string, any>[]>([])
  const [streamTypewriterKey, setStreamTypewriterKey] = useState(1)
  const [uploadedFile, setUploadedFile] = useState<{ id: string; name: string }>({ id: '', name: '' })
  const [uploadedFileList, setUploadedFileList] = useState<{ id: string; name: string }[]>([])
  const scrollRef = React.useRef<HTMLDivElement>(null)

  const allowedFileExtensions = (type: string): string[] => {
    if (type === '1') {
      return ['xlsx'] // 仅允许上传 xlsx 格式
    }
    return ['docx', 'xlsx', 'txt', 'mdx', 'pdf', 'html', 'xls', 'csv', 'md', 'htm'] // 允许全部格式
  }

  const beforeUpload = (file: File, type: string) => {
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)?.toLowerCase()
    if (!allowedFileExtensions(type).includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'error',
        content: `文件格式不正确，请上传${allowedFileExtensions(type).join(',')}格式的文件`,
        duration: 2
      })
      return false
    }
    messageApi.open({
      key: 'uploading',
      type: 'loading',
      content: '文件上传中'
    })
    setGenerating(true)
    uploadFile(file, token)
      .then(response => {
        setGenerating(false)
        if (response && response.id) {
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
          if (type === '1') {
            setUploadedFile(response)
          } else if (type === '2') {
            setUploadedFileList(prevList => [...prevList, response])
          }
          return false // 阻止自动上传
        } else {
          throw new Error('上传失败：未收到有效的响应')
        }
      })
      .catch(error => {
        setGenerating(false)
        messageApi.open({
          key: 'uploading',
          type: 'error',
          content: `文件上传失败: ${error.message}`,
          duration: 2
        })
      })
    return false // 阻止自动上传
  }

  const handleDownload = async () => {
    if (!uploadedFile.id || uploadedFileList.length === 0) {
      messageApi.error('文件未上传')
      return
    }
    setStreamTypewriterKey(streamTypewriterKey + 1)
    setMk('')
    setIsShowMk(true)
    setGenerating(true)
    messageApi.success('开始校验，请稍候...')
    let res = ''
    try {
      await regulatoryReporting(
        {
          template_file: {
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: uploadedFile.id
          },
          original_document: uploadedFileList.map(item => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: item.id
          }))
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              setMk(p => p + message || '')
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
              const regex = /\[(.*?)\]\((.*?)\)/ // 正确匹配 [text](url) 并捕获 url
              const match = res.match(regex)

              if (match) {
                const url = match[2] // 提取第二个捕获组（括号内的内容）
                console.log(url) // 输出: /path1
                fetchAndParseExcel(url)
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {
            setGenerating(false)
          }
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  const fetchAndParseExcel = async (url: string) => {
    try {
      // 1. 获取文件二进制数据
      const response = await fetch(`${window.location.protocol}//${window.location.hostname}${url}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })
      if (!response.ok) throw new Error('文件下载失败')
      const arrayBuffer = await response.arrayBuffer()
      const workbook = XLSX.read(arrayBuffer)
      const firstSheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[firstSheetName]
      const jsonData = XLSX.utils.sheet_to_json<Record<string, any>>(worksheet)
      setJsonData(jsonData)
    } catch (err) {}
  }

  return (
    <div className='regulatory-reporting-container'>
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      {contextHolder}
      <Layout style={{ height: '100%' }}>
        <Layout.Sider
          width={isShowMk ? '600px' : '100%'}
          style={{
            backgroundColor: '#f0f2f5',
            padding: '0 20px'
          }}
        >
          <Flex className='h-full' justify='center' align='center'>
            <Card className='regulatory-reporting-card'>
              <Title level={2} className='page-title'>
                监管报送文件生成
              </Title>
              <Text type='secondary' className='page-description'>
                你好，作为资深金融监管文件生成专家，我可以结合您上传的资料和模板，为您生成监管文件。
              </Text>
              <Title level={5} style={{ padding: '10px 0', margin: 0 }}>
                上传一份模板文件
              </Title>
              <Upload.Dragger
                showUploadList={false}
                multiple={false}
                beforeUpload={(file: File) => beforeUpload(file, '1')}
              >
                <p className='reporting-upload-drag-icon'>
                  <InboxOutlined />
                </p>
                <p>请拖拽文件到此处或点击上传文件按钮</p>
                <p>支持 xlsx', 'xls格式</p>
              </Upload.Dragger>
              <Title level={5} style={{ padding: '10px 0', margin: 0 }}>
                上传一份或多份资料文件
              </Title>
              <Upload.Dragger
                showUploadList={false}
                multiple={false}
                beforeUpload={(file: File) => beforeUpload(file, '2')}
              >
                <p className='reporting-upload-drag-icon'>
                  <InboxOutlined />
                </p>
                <p>请拖拽文件到此处或点击上传文件按钮</p>
                <p>支持 txt', 'docx', 'mdx', 'pdf', 'html', 'xlsx', 'xls', 'csv', 'md', 'htm格式</p>
              </Upload.Dragger>
              <div>
                {uploadedFile.id && (
                  <>
                    <p style={{ padding: '4px 0' }}>模板文件：</p>
                    <Tag
                      closeIcon
                      onClose={() => {
                        setUploadedFile({ id: '', name: '' })
                        return false
                      }}
                    >
                      {uploadedFile.name}
                    </Tag>
                  </>
                )}
                {uploadedFileList.length > 0 && (
                  <>
                    <p style={{ padding: '4px 0' }}>资料文件：</p>
                    {uploadedFileList.map(x => (
                      <>
                        <Tag
                          closeIcon
                          key={x.id}
                          style={{ marginTop: 4 }}
                          onClose={() => {
                            setUploadedFileList(prevList => prevList.filter(y => y.id !== x.id))
                            return false
                          }}
                        >
                          {x.name}
                        </Tag>
                        <br key={x.id + 'br'} />
                      </>
                    ))}
                  </>
                )}
              </div>
              <Button
                type='primary'
                onClick={handleDownload}
                size='large'
                loading={generating}
                block
                style={{ marginTop: '20px' }}
              >
                生成监管报送文件
              </Button>
            </Card>
          </Flex>
        </Layout.Sider>
        {isShowMk && (
          <Layout.Content style={{ padding: 24, background: '#fff' }}>
            <Flex align='center' justify='center' style={{ height: '50px' }}>
              <Title level={3}>生成结果</Title>
            </Flex>

            <div
              className='scroll-container'
              ref={scrollRef}
              style={{ height: 'calc(100vh - 98px)', overflowY: 'auto' }}
            >
              {!generating && <DynamicTable data={jsonData} />}
              <StreamTypewriter
                key={streamTypewriterKey}
                text={mk}
                end={!generating}
                onchange={() => {
                  scrollRef.current?.scrollTo({ top: scrollRef.current.scrollHeight, behavior: 'smooth' })
                }}
                components={{
                  p: ({ node, ...props }: any) => <p style={{ whiteSpace: 'pre-line', marginTop: 10 }} {...props} />
                }}
              />
            </div>
          </Layout.Content>
        )}
      </Layout>
    </div>
  )
}

export default RegulatoryReporting
