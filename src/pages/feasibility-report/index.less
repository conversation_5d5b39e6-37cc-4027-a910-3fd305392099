.feasibility-report-container {
  height: 100vh;
  background-color: #f0f2f5;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
}

.feasibility-report-card {
  width: 100%;
  max-width: 600px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  transition: all 0.3s ease;
  margin: 20vh auto;
  flex: 1;

  .page-title {
    margin-bottom: 16px;
    color: #1f1f1f;
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      font-size: 24px;
      color: #1890ff;
    }
  }

  .page-description {
    display: block;
    margin: 16px 0;
    font-size: 14px;
  }

  .upload-form {
    .ant-form-item {
      margin-bottom: 24px;
    }

    .ant-upload {
      width: 100%;
    }

    .ant-btn {
      width: 100%;
      height: 40px;
    }

    .file-name {
      display: block;
      margin-top: 8px;
      font-size: 14px;
    }
  }

  .ant-upload-list {
    margin-top: 8px;
  }
}
