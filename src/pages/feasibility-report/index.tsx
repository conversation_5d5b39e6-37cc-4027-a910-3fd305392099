import React, { useState } from 'react'
import { Upload, Button, Form, message, Card, Typography, Spin, Layout, Flex } from 'antd'
import { UploadOutlined, DownloadOutlined, FileTextOutlined } from '@ant-design/icons'
import ReactMarkdown from 'react-markdown'
import { feasibilityReport } from '@/api/feasibilityReport'
import { uploadFile } from '@/api/template.ts'
import { extractContent } from '@/utils/common'
import StreamTypewriter from '@/component/StreamTypewriter'
import GetKey from '@/component/getKey'
import remarkGfm from 'remark-gfm'
import RemarkBreaks from 'remark-breaks'
import RemarkMath from 'remark-math'
import './index.less'

const { Title, Text } = Typography
const FEASIBILITY_REPORT_TOKEN = import.meta.env['VITE_FEASIBILITY_REPORT_TOKEN'] || ''

interface FileState {
  file: File | null
  name: string
  id: string | number
}

export const FeasibilityReport: React.FC = () => {
  const [key, setKey] = useState('')
  const [open, setOpen] = useState(false)
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState(false)
  const [url, setUrl] = useState('')
  const [mk, setMk] = useState('')
  const [content, setContent] = useState<any>({})
  const [isShowMk, setIsShowMk] = useState(false)
  const [dueDiligenceFile, setDueDiligenceFile] = useState<FileState>({ file: null, name: '', id: '' })
  const [feasibilityTemplateFile, setFeasibilityTemplateFile] = useState<FileState>({ file: null, name: '', id: '' })

  const beforeUpload = (file: File, type: 'dueDiligence' | 'template') => {
    if (!key) {
      setOpen(true)
      return false
    }
    messageApi.open({
      key: 'uploading',
      type: 'loading',
      content: '文件上传中'
    })
    setGenerating(true)
    uploadFile(file, FEASIBILITY_REPORT_TOKEN)
      .then(response => {
        setGenerating(false)
        if (response && response.id) {
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
          if (type === 'dueDiligence') {
            setDueDiligenceFile({
              file: file,
              name: file.name,
              id: response.id
            })
          } else {
            setFeasibilityTemplateFile({
              file: file,
              name: file.name,
              id: response.id
            })
          }
          return false // 阻止自动上传
        } else {
          throw new Error('上传失败：未收到有效的响应')
        }
      })
      .catch(error => {
        setGenerating(false)
        messageApi.open({
          key: 'uploading',
          type: 'error',
          content: `文件上传失败: ${error.message}`,
          duration: 2
        })
      })
    return false // 阻止自动上传
  }

  const handleDownload = async () => {
    if (!key) {
      setOpen(true)
      return
    }
    if (!dueDiligenceFile.file || !feasibilityTemplateFile.file) {
      messageApi.error('请上传所有必需文件')
      return
    }
    setIsShowMk(true)
    setUrl('')
    setGenerating(true)
    messageApi.success('正在生成报告，请稍候...')
    let res = ''
    try {
      await feasibilityReport(
        {
          key,
          files: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: dueDiligenceFile.id
            },
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: feasibilityTemplateFile.id
            }
          ]
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              setMk(p => p + message || '')
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                setKey('')
                setOpen(true)
                return
              }

              const mk = extractContent(res, 'json')
              try {
                setContent(JSON.parse(mk))
              } catch (error) {
                return {}
              }

              const file = extractContent(res, 'file')
              setUrl(file)
              // 提取()中的内容
              const parenthesesContent = file.match(/\((.*?)\)/)
              const parenthesesResult = parenthesesContent ? parenthesesContent[1] : null

              // 提取[]中的内容
              const squareBracketsContent = file.match(/\[(.*?)\]/)
              const squareBracketsResult = squareBracketsContent ? squareBracketsContent[1] : null

              if (parenthesesResult && squareBracketsResult) {
                const link = document.createElement('a')
                link.href = parenthesesResult
                link.download = `可行性报告${squareBracketsResult}`
                document.body.appendChild(link)
                link.click()
                link.remove()
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {
            setGenerating(false)
          }
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  return (
    <div className='feasibility-report-container'>
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      {contextHolder}
      <GetKey open={open} onClose={setOpen} onChange={setKey} />
      <Layout style={{ height: '100%' }}>
        <Layout.Sider width={isShowMk ? '600px' : '100%'} style={{ backgroundColor: '#f0f2f5' }}>
          <Card className='feasibility-report-card'>
            <Title level={2} className='page-title'>
              <FileTextOutlined /> 可行性报告生成
            </Title>
            <Text type='secondary' className='page-description'>
              请上传尽职调查报告和可行性分析模板，系统将自动生成可行性报告
            </Text>

            <Form layout='vertical' className='upload-form'>
              <Form.Item label='尽职调查报告' required tooltip='请上传Word格式的尽职调查报告'>
                <Upload
                  showUploadList={false}
                  beforeUpload={file => beforeUpload(file, 'dueDiligence')}
                  accept='.docx, .pdf, .xlsx, .pptx, .xls, .csv, .txt'
                >
                  <Button icon={<UploadOutlined />}>上传尽职调查报告</Button>
                </Upload>
                {dueDiligenceFile.name && (
                  <Text type='success' className='file-name'>
                    已上传: {dueDiligenceFile.name}
                  </Text>
                )}
              </Form.Item>

              <Form.Item label='可行性分析模板' required tooltip='请上传Word格式的可行性分析模板'>
                <Upload
                  showUploadList={false}
                  beforeUpload={file => beforeUpload(file, 'template')}
                  accept='.docx, .pdf, .xlsx, .pptx, .xls, .csv, .txt'
                >
                  <Button icon={<UploadOutlined />}>上传可行性分析模板</Button>
                </Upload>
                {feasibilityTemplateFile.name && (
                  <Text type='success' className='file-name'>
                    已上传: {feasibilityTemplateFile.name}
                  </Text>
                )}
              </Form.Item>

              <Form.Item>
                <Button
                  type='primary'
                  onClick={handleDownload}
                  icon={<DownloadOutlined />}
                  size='large'
                  loading={generating}
                  block
                >
                  生成并下载报告
                </Button>
              </Form.Item>

              {url && <ReactMarkdown className='markdown-body'>{'可行性报告: ' + url}</ReactMarkdown>}
            </Form>
          </Card>
        </Layout.Sider>
        {isShowMk && (
          <Layout.Content style={{ padding: 24, background: '#fff' }}>
            <Flex align='center' justify='center' style={{ height: '50px' }}>
              <Title level={3}>分析结果</Title>
            </Flex>

            <div className='scroll-container' style={{ height: 'calc(100vh - 98px)', overflowY: 'auto' }}>
              {generating ? (
                <StreamTypewriter text={mk} end={!generating} />
              ) : (
                <>
                  {Object.keys(content).map((key: string, i: number) => {
                    return (
                      <>
                        <div
                          key={i + 'div'}
                          style={{ fontSize: '16px', fontWeight: 'bold' }}
                          dangerouslySetInnerHTML={{ __html: key }}
                        />
                        <ReactMarkdown
                          key={i}
                          className='markdown-body step3Analysis-container'
                          remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}
                        >
                          {content[key]}
                        </ReactMarkdown>
                      </>
                    )
                  })}
                </>
              )}
            </div>
          </Layout.Content>
        )}
      </Layout>
    </div>
  )
}

export default FeasibilityReport
