.document-format-verification-container {
  height: 100vh;
  background-color: #f0f2f5;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  .docx-preview-container {
    // 针对 docx-preview 生成的内部元素，移除段落的边框
    // 使用更具体的选择器来确保只影响预览区域内的段落
    p {
      border: none !important;
      // 或者分开设置各个方向的边框
      // border-top: none !important;
      // border-left: none !important;
      // border-bottom: none !important;
      // border-right: none !important;
  
      // 也可以只针对带有特定 class 的段落，例如：
      &.docx_level3 {
         border: none !important;
      }
      // 如果还有其他 level 的段落有边框，可以类似添加规则
      &.docx_level0, &.docx_level1, &.docx_level2 {
          border: none !important;
      }
    }
  
    // 确保 docx-wrapper 和 section.docx 这些父容器也没有不必要的边框
    .docx-wrapper, section.docx {
        border: none !important;
    }
  }
  .docx-wrapper {
    padding: 0;
    background-color: #fff;
  }
  .docx {
    padding: 20px 40px !important;
    width: 100% !important;
    margin: 0 !important;
    margin-bottom: 20px !important;
    border-radius: 4px;
  }
   
}

.document-verification-card {
  width: 100%;
  max-width: 50vw;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  transition: all 0.3s ease;
  margin: 20vh auto;
  flex: 1;

  .page-title {
    margin-bottom: 16px;
    color: #1f1f1f;
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      font-size: 24px;
      color: #1890ff;
    }
    
  }

  .page-description {
    display: block;
    margin: 16px 0 5px;
    font-size: 14px;
  }
  .ant-upload-drag-icon {
    font-size: 48px;
    color: #1890ff;
    transition: transform 0.3s ease;
  }
  .file-list {
    margin-top: 5px;
  }

}
