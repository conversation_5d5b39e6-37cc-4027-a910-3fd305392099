import React, { useState, useRef, useEffect } from 'react'
import {
  Upload,
  Button,
  message,
  Card,
  Typography,
  Spin,
  Layout,
  Flex,
  Tag,
} from 'antd'
import { CheckCircleFilled, InboxOutlined } from '@ant-design/icons'
import { documentFormatVerification } from '@/api/documentFormatVerification'
import { uploadFile } from '@/api/template'
import { extractContent } from '@/utils/common'
import { renderAsync } from 'docx-preview'
import './index.less'

const { Title, Text } = Typography
const token = import.meta.env['VITE_DOCUMENT_FORMAT_VERIFICATION'] || ''
function DocxPreview({ file }: any) {
  const previewRef = useRef<any>(null)

  useEffect(() => {
    if (!file) return

    // 渲染 .docx 文件
    renderAsync(file, previewRef.current)
      .then(() => console.log('DOCX 渲染成功'))
      .catch((err) => console.error('DOCX 渲染失败:', err))
  }, [file])

  return (
    <div
      ref={previewRef}
      style={{ width: '100%', minHeight: '500px' }}
      className="docx-preview-container"
    />
  )
}

export const DocumentFormatVerification: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState(false)
  const [mk, setMk] = useState('')
  const [isShowMk, setIsShowMk] = useState(false)
  const [isUploadBtn, setIsUploadBtn] = useState(false)
  const [uploadedFile, setUploadedFile] = useState<{
    id: string
    name: string
  }>({ id: '', name: '' })
  const [uploadedFiles, setUploadedFiles] = useState<
    { id: string; name: string }[]
  >([])
  const scrollRef = React.useRef<HTMLDivElement>(null)
  const [docxFile, setDocxFile] = useState<File | null>(null)

  useEffect(() => {
    // 获取完整的 URL 哈希部分
    const hash = window.location.hash

    // 在哈希中查找查询字符串的起始位置（第一个 '?'）
    const queryStringIndex = hash.indexOf('?')

    let fileParamsValue = null

    if (queryStringIndex !== -1) {
      // 提取查询字符串部分
      const queryString = hash.substring(queryStringIndex + 1)

      // 使用 URLSearchParams 对象来方便解析参数
      const params = new URLSearchParams(queryString)

      // 使用 get() 方法获取 'fileParams' 参数的原始编码值
      const rawFileParamsValue = params.get('fileParams')

      // 如果获取到了值，则进行解码
      if (rawFileParamsValue !== null) {
        try {
          fileParamsValue = decodeURIComponent(rawFileParamsValue)
        } catch (error) {
          console.error('解码 fileParams 值失败:', error)
          // 如果解码失败，可以使用原始值或者设置为 null
          fileParamsValue = rawFileParamsValue // 或者 null
        }
      }
    }

    // 检查是否获取到了值
    if (fileParamsValue) {
      console.log('从地址栏哈希中获取到并解码 fileParams:', fileParamsValue)
      processMarkdownLinkAndUpload(fileParamsValue)
    } else {
      console.log('地址栏哈希中没有找到 fileParams 参数')
    }
  }, []) // [] 作为依赖项表示只在组件挂载时运行一次

  const processMarkdownLinkAndUpload = async (markdownLink: string) => {
    console.log('正在处理文件链接:', markdownLink)
    // 解析字符串，提取文件名和 URL
    const match = markdownLink.match(/\[(.*?)\]\((.*?)\)/)

    if (!match || match.length < 3) {
      console.error('无效的 markdown 链接格式')
      messageApi.error('无效的文件链接格式')
      return
    }

    const fileName = match[1]
    const fileUrl = match[2] // 提取到的 URL
    // const fileUrl = `https://copilot.sino-bridge.com${match[2]}` // 本地调试 提取到的 URL

    // 下载文件内容
    try {
      const response = await fetch(fileUrl)
      console.log(response, 'response')

      if (!response.ok) {
        console.error(
          `HTTP error! status: ${response.status} for URL: ${fileUrl}`
        )
        const errorText = await response.text()
        console.error('响应内容:', errorText)
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const blob = await response.blob()
      console.log('下载到的 Blob 类型:', blob.type, '大小:', blob.size)

      // 检查下载的文件类型是否是预期的 .docx 或 .pdf
      const expectedTypes = [
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
        'application/pdf', // .pdf
      ]

      if (!expectedTypes.includes(blob.type)) {
        console.warn('下载的文件类型与预期不符:', blob.type)
        messageApi.warning(
          `下载的文件类型 ${blob.type} 与预期 (.docx/.pdf) 不符.`
        )
        // 决定是否继续处理非预期类型的文件
        return; // 如果不想处理非预期类型的文件，可以 uncomment 这行
      }

      // 创建 File 对象
      // 使用提取的文件名和下载到的 blob type
      const file = new File([blob], fileName, { type: blob.type })

      console.log('创建的 File 对象:', file)

      // 调用 beforeUpload 方法
      // 注意: beforeUpload 需要处理异步上传逻辑，并且返回 false 阻止默认行为
      beforeUpload(file)
    } catch (error: any) {
      console.error('下载或处理文件失败:', error)
      messageApi.open({
        key: 'downloading',
        type: 'error',
        content: `下载或处理文件 "${fileName}" 失败: ${error.message}`,
        duration: 3,
      })
    }
  }

  const beforeUpload = (file: File) => {
    console.log(file, '上传的文件')
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf('.') + 1)
      ?.toLowerCase()
    if (['pdf', 'docx'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中',
      })
      uploadFile(file, token).then(async (response) => {
        if (response.id) {
          setUploadedFiles([response])
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1,
          })
        } else {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件上传失败',
            duration: 1,
          })
        }
      })
      //   }
      // })
    } else {
      messageApi.error('目前仅支持.docx, .pdf类型的文件')
    }
    return false
  }

  const handleDelete = (fileId: string) => {
    setUploadedFiles((prevFiles) =>
      prevFiles.filter((file) => file.id !== fileId)
    )
    setIsUploadBtn(false)
  }

  const handleDownload = async () => {
    // if (!uploadedFile.id) {
    //   messageApi.error('文件未上传')
    //   return
    // }
    if (uploadedFiles.length <= 0) {
      messageApi.error('文件未上传')
      return
    }
    const fileIds = uploadedFiles.map((file) => file.id)
    setMk('')
    setDocxFile(null)
    setIsShowMk(true)
    setGenerating(true)
    messageApi.success('开始校验，请稍候...')
    let res = ''
    try {
      await documentFormatVerification(
        {
          files: fileIds.map((x) => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x,
          })),
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              setMk((p) => p + message || '')
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
              setIsUploadBtn(true)
              // 提取()中的内容
              // 提取文件URL
              const fileUrlMatch = res.match(/\[(.*?)\]\((.*?)\)/)
              console.log(fileUrlMatch, 'fileUrlMatch')
              if (fileUrlMatch) {
                // const fileUrl = `https://copilot.sino-bridge.com${fileUrlMatch[2]}` // 本地调试
                const fileUrl = fileUrlMatch[2]
                console.log('获取文件URL:', fileUrl)

                // 获取文件内容
                fetch(fileUrl)
                  .then((response) => {
                    if (!response.ok) {
                      throw new Error(`HTTP error! status: ${response.status}`)
                    }
                    return response.blob()
                  })
                  .then((blob) => {
                    console.log('获取到的blob:', blob)
                    // 检查blob类型
                    if (
                      blob.type !==
                      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                    ) {
                      console.warn('文件类型不匹配:', blob.type)
                    }

                    // 将 blob 转换为 File 对象
                    const file = new File([blob], 'document.docx', {
                      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    })
                    console.log('创建的File对象:', file)
                    setDocxFile(file)
                  })
                  .catch((error) => {
                    console.error('获取文件失败:', error)
                    messageApi.error('文件预览失败，请稍后重试')
                  })
              } else {
                console.warn('未找到文件URL')
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {
            setGenerating(false)
          },
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }
  const handleDownloadFile = () => {
    if (mk) {
      // 提取()中的内容
      const parenthesesContent = mk.match(/\((.*?)\)/)
      const parenthesesResult = parenthesesContent
        ? parenthesesContent[1]
        : null

      // 提取[]中的内容
      const squareBracketsContent = mk.match(/\[(.*?)\]/)
      const squareBracketsResult = squareBracketsContent
        ? squareBracketsContent[1]
        : null
      console.log(parenthesesResult, 'parenthesesResult')
      console.log(squareBracketsResult, 'squareBracketsResult')
      const link = document.createElement('a')
      // link.href = `https://copilot.sino-bridge.com${parenthesesResult}` // 本地调试
      link.href = parenthesesResult
      link.download = `${squareBracketsResult}`
      document.body.appendChild(link)
      link.click()
      link.remove()
    }
  }

  return (
    <div className="document-format-verification-container">
      <Spin tip="加载中" spinning={generating} fullscreen size="large" />
      {contextHolder}
      <Layout style={{ height: '100%' }}>
        <Layout.Sider
          width={isShowMk ? '40%' : '100%'}
          style={{ backgroundColor: '#f0f2f5', padding: '0 20px' }}
        >
          <Card className="document-verification-card">
            <Title level={2} className="page-title">
              公文格式校验
            </Title>
            <Text type="secondary" className="page-description">
              您好，我是您的 公文规范 助手，
            </Text>
            <Text
              type="secondary"
              className="page-description"
              style={{ marginTop: 0 }}
            >
              您可以上传文件，我会按照标准公文格式矫正您的公文。
            </Text>

            <Upload.Dragger
              multiple={false}
              accept=".doc,.docx,.pdf"
              showUploadList={false}
              beforeUpload={(file) => beforeUpload(file)}
              style={{ marginTop: '16px' }}
            >
              <div className="ant-upload-drag-icon">
                {uploadedFiles.length > 0 ? (
                  <CheckCircleFilled />
                ) : (
                  <InboxOutlined />
                )}
              </div>
              <div className="ant-upload-hint">
                <p style={{ margin: '0 10%' }}>
                  建议上传pdf、docx纯文档格式的文件、字数过多以及包含表格、图片等情况可能会影响效果
                </p>
                <p></p>
                <br />
              </div>
            </Upload.Dragger>

            {/* 文件列表 */}
            {uploadedFiles.length > 0 && (
              <div className="file-list">
                {uploadedFiles.map((x) => (
                  <p key={x.id}>
                    <Tag
                      closeIcon
                      style={{
                        marginTop: 4,
                        maxWidth: '100%',
                        whiteSpace: 'normal',
                        height: 'auto',
                        wordBreak: 'break-all',
                      }}
                      onClose={() => {
                        handleDelete(x.id)
                      }}
                    >
                      {x.name}
                    </Tag>
                  </p>
                ))}
              </div>
            )}

            {isUploadBtn ? (
              <>
                <Button
                  type="primary"
                  onClick={handleDownloadFile}
                  size="large"
                  block
                  style={{ marginTop: '20px' }}
                >
                  下载文件
                </Button>
                <Button
                  onClick={handleDownload}
                  size="large"
                  block
                  style={{ marginTop: '20px' }}
                >
                  重新生成
                </Button>
              </>
            ) : (
              <Button
                type="primary"
                onClick={handleDownload}
                size="large"
                block
                style={{ marginTop: '20px' }}
              >
                格式校验
              </Button>
            )}
          </Card>
        </Layout.Sider>
        {isShowMk && (
          <Layout.Content style={{ padding: 24, background: '#fff' }}>
            <Flex align="center" justify="center" style={{ height: '50px' }}>
              <Title level={3}>效果预览</Title>
            </Flex>

            <div
              className="scroll-container"
              ref={scrollRef}
              style={{ height: 'calc(100vh - 98px)', overflowY: 'auto' }}
            >
              {docxFile && <DocxPreview file={docxFile} />}
            </div>
          </Layout.Content>
        )}
      </Layout>
    </div>
  )
}

export default DocumentFormatVerification
