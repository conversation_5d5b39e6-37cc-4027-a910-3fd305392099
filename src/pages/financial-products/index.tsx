import { useCallback, useState } from 'react'
import { Layout, Input, Button, Card, Flex, message, Spin } from 'antd'
import { getFinancialProducts } from '@/api/financialProducts'
import StreamTypewriter from '@/component/StreamTypewriter'

const { Header, Content } = Layout

export const FinancialProducts = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [inputValue, setInputValue] = useState('')
  const [generating, setGenerating] = useState<boolean>(false)
  const [messages, setMessages] = useState<string>('')

  const handleInputChange = (event: any) => {
    setInputValue(event.target.value)
  }

  const handleSubmit = useCallback(async () => {
    if (!inputValue) {
      messageApi.open({
        key: 'uploading',
        type: 'error',
        content: '请输入查询内容',
        duration: 1
      })
      return
    }
    setGenerating(true)
    setMessages('')

    try {
      await getFinancialProducts(inputValue, {
        onMessage: (text, finished) => {
          if (text) {
            console.log(text)
            setMessages(prev => prev + text)
          }
          if (finished) {
            setGenerating(false)
          }
        },
        onError: () => {
          setGenerating(false)
        },
        onFinish: () => {}
      })
    } catch (err) {
      setGenerating(false)
    }
  }, [inputValue])

  return (
    <>
      {contextHolder}
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      <Layout style={{ height: '100vh', backgroundColor: '#f0f2f5', overflow: 'hidden' }}>
        <Header style={{ backgroundColor: '#16A34A', textAlign: 'center' }}>
          <h1 style={{ color: 'white', margin: 0, fontSize: '20px', fontWeight: 'bold' }}>理财产品推荐</h1>
        </Header>

        <Content style={{ padding: '24px', flex: 1 }}>
          <Flex vertical justify='space-between' style={{ height: 'calc(100vh - 100px)' }}>
            <Card style={{ marginBottom: '16px', height: 'calc(100vh - 180px)', overflow: 'auto' }}>
              <div style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
                <StreamTypewriter text={messages} end={!generating} />
              </div>
            </Card>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                backgroundColor: '#f0f0f0',
                padding: '16px',
                borderRadius: '8px'
              }}
            >
              <Input
                placeholder='请输入您的需求，例如‘推荐适合稳健型投资者的理财产品’...'
                value={inputValue}
                onChange={handleInputChange}
                style={{ flex: 1, marginRight: '16px' }}
              />
              <Button type='primary' onClick={handleSubmit} style={{ width: '70px' }}>
                发送
              </Button>
            </div>
          </Flex>
        </Content>
      </Layout>
    </>
  )
}

export default FinancialProducts
