import { useState, useEffect, useCallback } from 'react'
import {
  Upload,
  Button,
  Typography,
  Card,
  Spin,
  Input,
  message,
  Form,
  Flex,
  Menu,
  Select,
} from 'antd'
import {
  CheckCircleFilled,
  InboxOutlined,
  DeleteOutlined,
  CopyOutlined,
  DownloadOutlined,
  LoadingOutlined,
} from '@ant-design/icons'
import {
  uploadFile,
  convertFileToPDF,
  templatePadding,
  downloadPaddingResult,
} from '@/api/template'
import './index.less'
import { developProcess } from '@/api/developProcess'
import { extractJSONFromString } from '@/utils/json-extractor.ts'
import ReactMarkdown from 'react-markdown'
import RemarkMath from 'remark-math'
import RemarkGfm from 'remark-gfm'
import RemarkBreaks from 'remark-breaks'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { dark } from 'react-syntax-highlighter/dist/esm/styles/prism'
import collapse from 'antd/es/collapse'
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons'
import StreamTypewriter from '@/component/StreamTypewriter'
import { useNavigate } from 'react-router-dom'

const { Title, Paragraph } = Typography
const { TextArea } = Input

type Section =
  | '需求拆解'
  | '功能清单'
  | '原型设计'
  | '库表设计'
  | '接口文档'
  | 'API开发'
  | '测试用例'
type Status = '未开始' | '进行中' | '已生成'

interface SectionState {
  title: Section
  status: Status
  result?: string
}

interface TableItem {
  key: number
  section: string
  content: string
  analysis: string
}

export const DevelopProcessAIDS = () => {
  const navigate = useNavigate()

  const [messageApi, contextHolder] = message.useMessage()
  const [contractContent, setContractContent] = useState<string>('')
  const [uploadedFiles, setUploadedFiles] = useState<
    { id: string; name: string }[]
  >([])

  const [projectName, setProjectName] = useState<string>('')
  const [projectContext, setProjectContext] = useState<string>('')
  const [requirementDescription, setRequirementDescription] =
    useState<string>('')
  // 在组件顶部添加一个计数器状态
  const [regenerateCount, setRegenerateCount] = useState(0)
  // 添加一个新的状态来存储当前的流式内容
  const [streamContent, setStreamContent] = useState('')

  useEffect(() => {
    if (uploadedFiles.length > 0) {
      messageApi.open({
        key: 'uploading',
        type: 'success',
        content: '文件上传成功',
        duration: 1,
      })
    }
  }, [uploadedFiles, messageApi])
  type Section =
    | '需求拆解'
    | '功能清单'
    | '原型设计'
    | '库表设计'
    | '接口文档'
    | 'API开发'
    | '测试用例'
  type Status = '未开始' | '进行中' | '已完成'

  interface SectionState {
    title: Section
    status: Status
    result?: string
    disabled: boolean
    message: string
  }

  const [projectDesc, setProjectDesc] = useState('')
  const [requirements, setRequirements] = useState('')
  const [selectedSection, setSelectedSection] = useState<Section>('需求拆解')

  const [sections, setSections] = useState<SectionState[]>([
    { title: '需求拆解', status: '未开始', disabled: false, message: '' },
    { title: '功能清单', status: '未开始', disabled: true, message: '' },
    { title: '原型设计', status: '未开始', disabled: true, message: '' },
    { title: '库表设计', status: '未开始', disabled: true, message: '' },
    { title: '接口文档', status: '未开始', disabled: true, message: '' },
    { title: 'API开发', status: '未开始', disabled: true, message: '' },
    { title: '测试用例', status: '未开始', disabled: true, message: '' },
  ])

  const [requirementDisassemblyQuery, setRequirementDisassemblyQuery] =
    useState<string>('')
  const [functionListQuery, setFunctionListQuery] = useState<string>('')
  const [interfaceDocumentationQuery, setInterfaceDocumentationQuery] =
    useState<string>('')

  // 获取数据
  const handleGeneration = useCallback(
    // async (fileIds: string[]) => {
    async () => {
      setGenerating(true)
      let accumulatedMessages = ''
      // 清空流式内容
      setStreamContent('')
      let query = ''
      switch (selectedSection) {
        case '需求拆解':
          query = requirementDescription
          break
        case '功能清单':
          query = requirementDisassemblyQuery
          break
        case '原型设计':
        case '库表设计':
        case '测试用例':
          query = functionListQuery
          break
        case 'API开发':
          query = interfaceDocumentationQuery
          break
        case '接口文档':
          query = functionListQuery
          break
        default:
          break
      }
      const newSections = sections.map((s) => {
        if (s.title === selectedSection) {
          return { ...s, status: '进行中' as Status }
        }
        return s
      })
      setSections(newSections)

      try {
        await developProcess(
          {
            type: selectedSection,
            background: projectContext,
            query: query,
            dataModel: sections.filter((v) => {
              return v.title == '库表设计'
            })[0].message,
          },
          {
            onMessage: (text: string | null, finished: boolean) => {
              if (text) {
                accumulatedMessages += text
                const cleanedData = accumulatedMessages.replace(
                  /^```markdown\s*|```$/g,
                  ''
                )
                // setMarkdownTable(cleanedData)
                console.log(accumulatedMessages)
                // setMessages((prev) => prev + text);
                // 更新流式内容
                setStreamContent(cleanedData)
                // 立即更新当前部分的内容
                setSections((prevSections) =>
                  prevSections.map((s) =>
                    s.title === selectedSection
                      ? { ...s, message: cleanedData }
                      : s
                  )
                )
                switch (selectedSection) {
                  case '需求拆解':
                    setRequirementDisassemblyQuery(accumulatedMessages)
                    break
                  case '功能清单':
                    setFunctionListQuery(accumulatedMessages)
                    break
                  case '接口文档':
                    setInterfaceDocumentationQuery(accumulatedMessages)
                    break
                  default:
                    break
                }
              }
              if (finished) {
                setGenerating(false)
                // setMessagesEnd(true)
                // 改进后的版本
                setSections((prevSections) => {
                  // 第一步：标记当前选中section为已完成
                  const afterFirstUpdate = prevSections.map((s) =>
                    s.title === selectedSection
                      ? {
                          ...s,
                          status: '已完成' as Status,
                          message: accumulatedMessages,
                        }
                      : s
                  )

                  // 第二步：根据选中section进行额外处理
                  switch (selectedSection) {
                    case '需求拆解':
                      return afterFirstUpdate.map((s) =>
                        s.title === '功能清单' ? { ...s, disabled: false } : s
                      )

                    case '功能清单':
                      return afterFirstUpdate.map((s) =>
                        [
                          '原型设计',
                          '库表设计',
                          '接口文档',
                          '测试用例',
                        ].includes(s.title)
                          ? { ...s, disabled: false }
                          : s
                      )

                    case '接口文档':
                      return afterFirstUpdate.map((s) =>
                        s.title === 'API开发' ? { ...s, disabled: false } : s
                      )

                    default:
                      return afterFirstUpdate
                  }
                })
                try {
                  // const list = JSON.parse(accumulatedMessages)
                  // list.forEach((item: { key: any }, index: any) => {
                  //   item.key = index
                  // })
                  // setTableList(list)
                  // const cleanedData = accumulatedMessages.replace(
                  //   /^```markdown\s*|```$/g,
                  //   ''
                  // )
                  // setMarkdownTable(cleanedData)
                } catch (e) {
                  console.log(accumulatedMessages)
                  // setMessages(accumulatedMessages);
                }
              }
            },
            onError: () => {
              const newSections = sections.map((s) => {
                if (s.title === selectedSection) {
                  return { ...s, status: '已完成' as Status }
                }
                return s
              })
              setSections(newSections)
              setGenerating(false)
            },
            onFinish: () => {
              setGenerating(false)
            },
          }
        )
      } catch (err) {
        const newSections = sections.map((s) => {
          if (s.title === selectedSection) {
            return { ...s, status: '已完成' as Status }
          }
          return s
        })
        setSections(newSections)
        setGenerating(false)
      }
    },
    [
      contractContent,
      selectedSection,
      projectContext,
      requirementDescription,
      requirementDisassemblyQuery,
      functionListQuery,
      interfaceDocumentationQuery,
      sections,
    ]
  )

  // 依赖检查
  const checkDependencies = (section: Section): boolean => {
    const getStatus = (title: Section) =>
      sections.find((s) => s.title === title)?.status === '已完成'

    switch (section) {
      case '需求拆解':
        // 验证必备条件是否满足
        if (!projectName || !projectContext || !requirementDescription) {
          messageApi.warning('请填写项目名称、项目背景和需求描述')
          return false
        }
        return true
      case '功能清单':
        return getStatus('需求拆解')
      case '原型设计':
        return getStatus('需求拆解') && getStatus('功能清单')
      case '库表设计':
      case '接口文档':
      case '测试用例':
        return getStatus('功能清单')
      case 'API开发':
        return getStatus('库表设计') && getStatus('接口文档')
      default:
        return false
    }
  }

  // 选择步骤
  const handleSelectSection = (section: Section) => {
    console.log('选择工具条', section)
    const checkDepen = checkDependencies(section)
    console.log('依赖检查', checkDepen)
    // 设置当前步骤
    setSelectedSection(section)

    // 如果该部分已经有内容，同步到 streamContent
    const selectedSectionData = sections.find((s) => s.title === section)
    if (selectedSectionData?.message) {
      setStreamContent(selectedSectionData.message)
    } else {
      // 如果没有内容，清空 streamContent
      setStreamContent('')
    }
    // 检查依赖
    // if (checkDepen) {
    //   // 设置当前步骤
    //   setSelectedSection(section);
    //   // 设置状态
    //   const newSections = sections.map((s) => {
    //     if (s.title === section) {
    //       return { ...s, status: '进行中' as Status };
    //     }
    //     return s;
    //   });
    //   setSections(newSections);
    //   // 请求接口获取数据
    //   handleGeneration()
    // }else{
    //   // 需求拆解不需要提示
    //   if( section != '需求拆解' ){
    //     messageApi.warning('请先完成前置步骤');
    //   }
    // }
  }

  // 获取状态颜色
  const getStatusColor = (section: SectionState) => {
    switch (section.status) {
      case '已完成':
        return 'text-green-600'
      case '进行中':
        return 'text-blue-600'
      default:
        return 'text-muted-foreground'
    }
  }

  //点击生成
  const handleGenerationStart = () => {
    setStartSending(true)
    console.log('点击生成')
    setRegenerateCount((prev) => prev + 1) // 增加计数
    // setGenerationMetadata(null)
    // setProjectContext(projectContext)
    // setRequirementDescription(requirementDescription)
    // console.log(selectedSection,projectContext,requirementDescription);
    // 清空流式内容
    setStreamContent('')
    handleGeneration()
  }
  // 点击导出
  const handleExport = () => {
    if (
      sections.filter((v) => {
        return v.title == selectedSection
      })[0].message
    ) {
      setStartSending(true)
      console.log('点击导出')
      // setMarkdownTable('')
      // 导出内容到Word文档
      const resultContainer = document.getElementById('resultContainer')
      let text = ''
      if (resultContainer) {
        text = resultContainer.innerHTML
      } else {
        console.error('resultContainer is null')
        return
      }
      const blob = new Blob(['<html><body>' + text + '</body></html>'], {
        type: 'application/msword',
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = '需求拆解.doc'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } else {
      messageApi.warning('请先生成' + selectedSection)
    }
  }
  // 点击复制
  // const handleCopy = ()=>{
  //   setStartSending(true)
  //   console.log('点击复制');
  //   setMarkdownTable('')
  // }
  const [codeString, setCodeString] = useState('')
  const [copyText, setCopyText] = useState('复制')
  const [copyIcon, setCopyIcon] = useState(
    <CopyOutlined className="color-white" />
  )
  const [downloadText, setDownloadText] = useState('下载')

  // 点击【开始生成】后，状态变更
  const [startSending, setStartSending] = useState<boolean>(false)

  // 生成状态
  const [generating, setGenerating] = useState<boolean>(false)
  // 生成消息
  // const [messages, setMessages] = useState<string>("");
  // 生成异常
  const [error, setError] = useState<string | null>(null)
  const [generationMetadata, setGenerationMetadata] = useState<object | null>()
  const [selectedType, setSelectedType] = useState<number>()

  const [templates, setTemplates] = useState<object[]>([])
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>()
  const [uploadedFile, setUploadedFile] = useState<object>()
  const [thankText, setThankText] = useState<string>('')

  // const handleCopyClick = useCallback(() => {
  //   console.log('点击复制11');

  //   navigator.clipboard
  //     .writeText(codeString)
  //     .then(() => {
  //       setCopyText('已复制')
  //       setCopyIcon(<CheckCircleFilled className='color-white' />)
  //     })
  //     .finally(() => {
  //       setTimeout(() => {
  //         setCopyText('复制')
  //         setCopyIcon(<CopyOutlined className='color-white' />)
  //       }, 2000)
  //     })
  // }, [codeString])
  // 点击预览页面
  const handlePreview = () => {
    console.log('点击预览')
    const prototypeSection = sections.find((s) => s.title === '原型设计')
    if (prototypeSection?.message) {
      // 提取代码内容
      const codeContent = prototypeSection.message
      console.log(codeContent, 'codeContent')
      // 提取纯 HTML 部分
      const htmlContent =
        prototypeSection.message.match(/```html\n([\s\S]*?)```/)?.[1] || ''
      console.log(htmlContent, 'htmlContent')
      // 将代码内容转换为 Base64 编码，以便安全传递
      const encodedCode = btoa(encodeURIComponent(htmlContent))

      // // 导航到 interpreter 页面，并传递必要的参数
      // navigate(`/interpreter?type=html&code=${encodedCode}`)
      // 在新窗口打开预览页面
      // 使用完整的 URL
      // const baseUrl = window.location.origin; // 获取当前域名
      window.open(`https://copilot.sino-bridge.com/toolbox/#/interpreter?type=html&code=${encodedCode}`, '_blank');
    } else {
      messageApi.warning('请先生成原型设计内容')
    }
  }
  const handleCopyClick = () => {
    if (
      sections.filter((v) => {
        return v.title == selectedSection
      })[0].message
    ) {
      const resultContainer = document.getElementById('resultContainer')
      const text = resultContainer ? resultContainer.innerText : ''
      navigator.clipboard.writeText(text).then(
        () => {
          alert('内容已复制到剪贴板')
        },
        (err) => {
          console.error('复制失败', err)
        }
      )
    } else {
      messageApi.warning('请先生成' + selectedSection)
    }
  }

  // 获取步骤对应状态
  const getStatusBySection = (section: Section) => {
    return sections.find((s) => s.title === section)?.status
  }

  const handleTemplatePadding = () => {
    if (selectedType === 4) {
      const link = document.createElement('a')
      link.href = `https://copilot.sino-bridge.com/toolbox/%E7%94%B5%E5%AD%90%E4%BF%9D%E5%8D%95.pdf`
      link.download = ''
      document.body.appendChild(link)
      link.click()
      link.remove()
      return
    }
    const templatePaddingData: string | null = extractJSONFromString(messages)
    if (selectedTemplateId && templatePaddingData) {
      templatePadding(selectedTemplateId, templatePaddingData).then(
        (response) => {
          console.log(response)
          if (response.code === 200) {
            const url: string = response.data.shortUrl
            if (url) {
              downloadPaddingResult(url)
            }
          }
        }
      )
    }
  }

  const markdownComponents = {
    code({ inline, className, children, ...props }) {
      const match = /language-(\w+)/.exec(className || '')
      return !inline && match ? (
        <SyntaxHighlighter
          {...props}
          className="editor custom-scrollbar"
          language={match?.[1]}
          showLineNumbers={true}
          wrapLines={true}
          style={dark}
          customStyle={{
            border: 'none',
            margin: '0',
          }}
          children={String(children).replace(/\n$/, '')}
        ></SyntaxHighlighter>
      ) : (
        <code {...props} className={className}>
          {children}
        </code>
      )
    },
  }

  const [collapse, setCollapse] = useState(false)
  const handleCollapse = () => {
    // 处理收起逻辑
    setCollapse(!collapse)
  }

  return (
    <>
      {contextHolder}
      {/* 添加一个包装 div，带有特定的类名 */}
      <div className="develop-process-page">
        {/* {contextHolder}
        当前步骤---{selectedSection}***{getStatusBySection(selectedSection)}%% */}
        {/* {projectContext} */}
        <Spin tip="处理中..." spinning={generating} fullscreen size="large" />
        <div className="develop-process-container">
          {/* 左侧面板 */}
          <div className="develop-left-panel">
            <Typography.Text className="title-text">
              开发流程辅助工具
            </Typography.Text>
            {/* <Card className="input-section"> */}
            <Form layout="vertical">
              <Form.Item label="项目名称">
                <Input
                  placeholder="请输入项目名称"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                />
              </Form.Item>
              <Form.Item label="项目背景">
                <TextArea
                  rows={4}
                  placeholder="请输入项目背景"
                  value={projectContext}
                  onChange={(e) => setProjectContext(e.target.value)}
                />
              </Form.Item>
              <Form.Item label="需求描述">
                <TextArea
                  rows={4}
                  placeholder="请输入需求描述"
                  value={requirementDescription}
                  onChange={(e) => setRequirementDescription(e.target.value)}
                />
              </Form.Item>
            </Form>

            <Menu
              mode="inline"
              selectedKeys={[selectedSection]}
              onClick={({ key }) => handleSelectSection(key as Section)}
            >
              {sections.map((section) => (
                <Menu.Item
                  key={section.title}
                  className="cursor-pointer"
                  disabled={section.disabled}
                  // onClick={() => handleSelectSection(section.title)}
                >
                  <span className={getStatusColor(section)}>
                    {section.title}
                    {/* {section.status!='未开始' && <span className='menu-sts'>{section.status}</span>} */}
                    <span className="menu-sts">{section.status}</span>
                  </span>
                </Menu.Item>
              ))}
            </Menu>

            {/* </Card> */}
          </div>

          {/* 右侧面板 */}
          <div className="develop-right-panel">
            <div className="develop-action-buttons">
              <Typography.Text className="develop-section-title">
                {selectedSection}
              </Typography.Text>
              <div>
                {selectedSection === '原型设计' && (
                  <Button type="primary" onClick={handlePreview}>
                    点击预览
                  </Button>
                )}
                <Button type="primary" onClick={handleGenerationStart}>
                  {getStatusBySection(selectedSection) == '未开始'
                    ? '点击生成'
                    : '重新生成'}
                </Button>
                {/* 只有需求拆解步骤存在该按钮
                {selectedSection == "需求拆解" && (
                  <Button color="cyan" variant="solid" onClick={handleExport}>
                    点击导出
                  </Button>
                )} */}
                <Button type="primary" onClick={handleCopyClick}>
                  点击复制
                </Button>
              </div>
            </div>

            {/* 思考过程 */}
            {/* <div className="develop-section">
              <Typography.Text className="develop-section-title">
                思考过程
              </Typography.Text>
            </div> */}
            {/* 思考过程区域 */}
            {thankText && (
              <Card className="thinking-card">
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  <Title
                    level={4}
                    style={{ margin: 0, display: 'flex', alignItems: 'center' }}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      style={{
                        marginRight: 8,
                        width: 20,
                        height: 20,
                        color: '#1890ff',
                      }}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                      />
                    </svg>
                    查询思考过程
                  </Title>
                  <Button
                    type="link"
                    onClick={handleCollapse}
                    icon={
                      collapse ? <CaretDownOutlined /> : <CaretUpOutlined />
                    }
                  >
                    {collapse ? '展开' : '收起'}
                  </Button>
                </div>
                {!collapse && (
                  <Paragraph
                    style={{
                      marginTop: 16,
                      backgroundColor: '#f5f5f5',
                      padding: '8px',
                      borderRadius: '4px',
                      color: 'rgb(55, 65, 81)',
                    }}
                  >
                    <ReactMarkdown className="markdown-body">
                      {thankText}
                    </ReactMarkdown>
                  </Paragraph>
                )}
              </Card>
            )}

            {startSending && (
              <Flex className="preview-panel" vertical gap="middle">
                {/* <Flex className='preview-header' justify='space-between' gap='middle'>
                <Select
                  className='template-select'
                  size='large'
                  placeholder={
                    generationMetadata
                      ? selectedType === 2
                        ? '信息提取完毕，请划词复制表格后粘贴使用'
                        : '文件提取完毕，请选择模板'
                      : '正在提取文件信息，请不要关闭或刷新页面'
                  }
                  options={templates}
                  disabled={!generationMetadata || selectedType === 2}
                  onChange={setSelectedTemplateId}
                />
                {selectedType !== 2 ? (
                  <Button
                    icon={<DownloadOutlined />}
                    type='primary'
                    size='large'
                    disabled={!selectedTemplateId}
                    onClick={handleTemplatePadding}
                  >
                    下 载
                  </Button>
                ) : (
                  <Button type='primary' size='large' icon={<CopyOutlined />} onClick={handleTableFormat}>
                    复制到剪贴板
                  </Button>
                )}
              </Flex> */}
                {sections.filter((v) => {
                  return v.title == selectedSection
                })[0].message ? (
                  <Card
                    className="preview-content custom-scrollbar"
                    id="resultContainer"
                  >
                    {/* <ReactMarkdown
                      className="markdown-body custom-scrollbar"
                      remarkPlugins={[[RemarkMath], RemarkGfm, RemarkBreaks]}
                      components={markdownComponents}
                    >
                      {
                        sections.filter((v) => {
                          return v.title == selectedSection;
                        })[0].message
                      }
                    </ReactMarkdown> */}
                    <StreamTypewriter
                      key={`${selectedSection}-${regenerateCount}`}
                      // text={sections.filter((v) => {
                      //   return v.title == selectedSection;
                      // })[0].message}
                      text={streamContent}
                      components={markdownComponents}
                      end={!generating} // 添加这一行，当 generating 为 false 时表示输出结束
                    />
                  </Card>
                ) : sections.filter((v) => {
                    return v.title == selectedSection
                  })[0].status == '进行中' ? (
                  <Card
                    className="preview-content custom-scrollbar"
                    id="resultContainer"
                  >
                    <>
                      <LoadingOutlined
                        style={{ fontSize: 48, marginBottom: 24 }}
                      />
                      <Typography.Text>
                        正在提取文件信息，请不要关闭或刷新页面
                      </Typography.Text>
                    </>
                  </Card>
                ) : (
                  ''
                )}
              </Flex>
            )}
          </div>
        </div>
      </div>
    </>
  )
}

export default DevelopProcessAIDS
