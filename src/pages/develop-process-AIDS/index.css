.develop-tools-container {
  display: flex;
  gap: 24px;
  margin: 0 auto;
  height: 100vh;
}
.develop-tools-container .develop-left-panel {
  width: 25%;
  min-width: 25%;
  background: #ffffff;
  padding: 24px;
}
.develop-tools-container .develop-left-panel .title-text {
  font-size: 24px;
  font-weight: 600;
  text-align: center;
  color: #1a1a1a;
  padding-bottom: 8px;
  display: block;
}
.develop-tools-container .develop-right-panel {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin: 32px 32px 32px 8px;
  position: relative;
}
.develop-tools-container .develop-right-panel .develop-action-buttons {
  display: flex;
  gap: 12px;
  justify-content: space-between;
}
.develop-tools-container .develop-right-panel .develop-output-section {
  flex: 1;
  background: #fff;
  border-radius: 12px;
}
.develop-tools-container .develop-right-panel .develop-output-section .ant-card-body {
  padding: 24px;
  display: flex;
  flex-direction: column;
}
.develop-tools-container .develop-right-panel .develop-markdown-body table {
  border-collapse: collapse;
  width: 100%;
}
.develop-tools-container .develop-right-panel .develop-markdown-body th,
.develop-tools-container .develop-right-panel .develop-markdown-body td {
  border: 1px solid #ccc;
  /* 设置边框颜色 */
  padding: 8px;
  /* 设置单元格内边距 */
  text-align: left;
  /* 设置文本对齐方式 */
}
.develop-tools-container .develop-right-panel .develop-markdown-body th {
  background-color: #f2f2f2;
  /* 设置表头背景色 */
}
.develop-tools-container .develop-right-panel .develop-markdown-body tr td:nth-child(1) {
  min-width: 90px;
}
.develop-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
  display: block;
}
.ant-upload-drag {
  padding: 24px;
  border: 2px dashed #e8e8e8;
  border-radius: 12px;
  background: #fafafa;
  text-align: center;
  transition: all 0.3s ease;
}
.ant-upload-drag:hover {
  border-color: #1890ff;
  background: #f0f7ff;
}
.ant-upload-drag .ant-upload-drag-icon {
  margin-bottom: 16px;
  font-size: 48px;
  color: #1890ff;
  transition: transform 0.3s ease;
}
.ant-upload-drag .ant-upload-drag-icon:hover {
  transform: scale(1.05);
}
.file-list {
  margin-top: 16px;
  max-height: 200px;
  overflow-y: auto;
  padding-right: 4px;
}
.file-list::-webkit-scrollbar {
  width: 6px;
}
.file-list::-webkit-scrollbar-thumb {
  background-color: #d9d9d9;
  border-radius: 3px;
}
.file-list::-webkit-scrollbar-track {
  background-color: #f5f5f5;
  border-radius: 3px;
}
.file-list .file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}
.file-list .file-item:hover {
  background: #f0f2f5;
}
.file-list .file-item span {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 12px;
}
.ant-input {
  border-radius: 8px;
}
.ant-input:hover,
.ant-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}
