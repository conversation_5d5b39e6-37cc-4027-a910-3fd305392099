import { useState, useRef, useEffect } from 'react'
import {
  Card,
  Select,
  Button,
  Progress,
  message,
  Space,
  Typography,
  Upload,
  UploadProps,
  Flex,
  Input,
  Form,
} from 'antd'
import {
  UploadOutlined,
  PlayCircleOutlined,
  StopOutlined,
  DeleteOutlined,
  CopyOutlined,
  CloudUploadOutlined,
  <PERSON>Outlined,
  SettingOutlined,
  CommentOutlined,
  ConsoleSqlOutlined,
} from '@ant-design/icons'
import { copyText } from '@/utils/clipboard'
import { getKnowledgeData } from '@/api/contractSceneSet'

const { Text, Link } = Typography
const { TextArea } = Input
type LogType = 'secondary' | 'success' | 'warning' | 'danger' | ''

type props = {
  Tenantid: string
  Token: string
  handleStart: (type: string, query: string, keyPoints?: string) => void
  generating: boolean
  purgeData?: string
  onClearPurgeData?: () => void
  onKeyPointsChange?: (keyPoints: string) => void
}

const FunASRStreamTester: React.FC<props> = ({
  Tenantid,
  Token,
  handleStart,
  generating,
  purgeData,
  onClearPurgeData,
  onKeyPointsChange,
}) => {
  const [apiUrl, setApiUrl] = useState<string>('/funasr/recognize/stream')
  const [mode, setMode] = useState<'offline' | 'online' | '2pass'>('offline')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isProcessing, setIsProcessing] = useState<boolean>(false)
  const [progress, setProgress] = useState<number>(0)
  const [status, setStatus] = useState<
    'idle' | 'connecting' | 'processing' | 'completed' | 'error'
  >('idle')
  const [resultText, setResultText] = useState<string>('等待开始识别...')
  const [logs, setLogs] = useState<{ value: string; type: LogType }[]>([])
  const abortControllerRef = useRef<AbortController | null>(null)
  const [cardData, setCardData] = useState<any[]>([])
  const [personalLibs, setPersonalLibs] = useState<string[]>([])
  const [keyPoints, setKeyPoints] = useState<string>(`1. 收益承诺类（高危违规）
定义：任何明示或暗示保证收益的表述
典型话术："保证年化5%收益"、"稳赚不赔"、"最低收益3%"、"保底8%回报"
变体识别：
含具体数字的收益承诺："月收益1分利"
比较型承诺："收益是存款的3倍"
模糊承诺："收益可达两位数"
2. 本金保障类（高危违规）
定义：承诺本金不受损失
典型话术："100%保本"、"零风险投资"、"亏损包赔"、"绝对安全"
变体识别："资金绝对安全"、"不会亏一分钱"、"有本金保障机制"
3. 绝对化表述类（中危违规）
定义：使用极端词语诱导客户
典型话术："市场上最好的产品"、"唯一选择"、"绝对靠谱"、"百分百赚钱"
变体识别："顶尖理财产品"、"不二之选"、"万无一失"
4. 历史业绩误导类（中危违规）
定义：用历史数据暗示未来收益
典型话术："过去三年平均收益12%"、"去年最高到15%"、"从未亏损过"
变体识别：展示具体年份收益："2022年收益9.5%"、"历史业绩稳定在8%以上"
5. 资质虚假宣传类（高危违规）
定义：虚构或夸大产品资质
典型话术："银保监会推荐产品"、"国家认证"、"内部特批渠道"、"行长专属产品"
变体识别："官方背书的理财产品"、"政府支持项目"
6. 返利诱惑类（高危违规）
定义：用额外利益诱导购买
典型话术："开户返现1%"、"介绍朋友送礼品"、"私下给您返点"
变体识别："推荐奖励千分之五"、"认购即赠黄金"
7. 时效压迫类（中危违规）
定义：制造虚假紧迫感
典型话术："最后一天优惠"、"仅剩3个名额"、"明天就涨价"
变体识别："错过等半年"、"限时特供"
8. 同业贬低类（中危违规）
定义：贬低其他金融机构
典型话术："别家的产品会亏本"、"XX银行收益更低"、"只有我们安全"
变体识别："其他产品风险高"、"他们做不到我们这种收益"`)

  const getAllListData = () => {
    getKnowledgeData(
      {
        pageNum: 1,
        pageSize: 999999,
        entity: {
          libName: '',
        },
      },
      Tenantid,
      Token
    ).then((res) => {
      if (res.code === 200) {
        setCardData(res.data.records)
      }
    })
  }

  useEffect(() => {
    getAllListData()
  }, [])

  type Status = 'idle' | 'connecting' | 'processing' | 'completed' | 'error'

  const statusMap: Record<
    Status,
    { text: string; color: 'default' | 'processing' | 'success' | 'error' }
  > = {
    idle: { text: '待机', color: 'default' },
    connecting: { text: '连接中', color: 'processing' },
    processing: { text: '识别中', color: 'processing' },
    completed: { text: '完成', color: 'success' },
    error: { text: '错误', color: 'error' },
  }

  const handleFileChange = (info: {
    file: File & { status?: string; name: string; size: number; type: string }
  }) => {
    const file = info.file
    if (file.status === 'removed') {
      setSelectedFile(null)
      addLog('已移除文件', '')
      return
    }

    const supportedTypes = [
      'audio/mpeg',
      'audio/mp3',
      'audio/mp4',
      'audio/m4a',
      'audio/wav',
      'audio/amr',
      'audio/aac',
      'audio/flac',
      'audio/ogg',
      'audio/mpga',
    ]

    if (
      !supportedTypes.some((type) => file.type.includes(type.split('/')[1]))
    ) {
      addLog(`不支持的文件格式: ${file.type}`, 'danger')
      message.error('不支持的文件格式')
      return
    }

    setSelectedFile(file)
    addLog(`文件已选择: ${file.name} (${formatFileSize(file.size)})`, 'success')
  }
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const startRecognition = async () => {
    if (!selectedFile) {
      addLog('请先选择音频文件', 'danger')
      message.error('请先选择音频文件')
      return
    }

    setIsProcessing(true)
    setStatus('connecting')
    setProgress(0)
    addLog('开始连接到服务器...', '')

    try {
      abortControllerRef.current = new AbortController()

      const formData = new FormData()
      formData.append('audio', selectedFile)
      formData.append('mode', mode)

      setStatus('processing')
      addLog('连接成功，开始处理音频...', 'success')

      const response = await fetch(apiUrl, {
        method: 'POST',
        body: formData,
        signal: abortControllerRef.current.signal,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      setStatus('processing')
      addLog('连接成功，开始处理音频...', 'success')

      await processStream(response)
    } catch (error: any) {
      if (error.name === 'AbortError') {
        addLog('识别已被用户停止', 'warning')
        setStatus('idle')
      } else {
        addLog(`识别失败: ${error.message}`, 'danger')
        setStatus('error')
        message.error('识别失败: ' + error.message)
      }
    } finally {
      setIsProcessing(false)
    }
  }

  const processStream = async (response: any) => {
    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let buffer = ''

    try {
      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          addLog('数据流读取完成', 'success')
          break
        }

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || '' // 保留不完整的行

        for (const line of lines) {
          if (line.trim() && line.startsWith('data: ')) {
            try {
              const eventData: StreamEvent = JSON.parse(line.slice(6))
              handleStreamEvent(eventData)
            } catch (e) {
              addLog(`解析事件数据失败: ${(e as Error).message}`, 'danger')
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }
  }

  type StreamEvent = {
    type: string
    progress?: number
    text?: string
    data?: { text: string }
    message?: string
    is_partial?: boolean
  }

  const handleStreamEvent = (eventData: StreamEvent) => {
    console.log('Received event:', eventData)
    const { type, progress, text, data, message } = eventData

    addLog(`收到事件: ${type}`, '')

    switch (type) {
      case 'connected':
        setStatus('processing')
        addLog(message || '已连接到服务器', 'success')
        break

      case 'progress':
        if (typeof progress === 'number') {
          setProgress(progress)
          addLog(`处理进度: ${progress}%`, '')
        }
        break

      case 'text_update':
        if (text) {
          appendText(text)
        }
        break

      case 'final':
        setProgress(100)
        setStatus('completed')
        if (data && data.text) {
          addLog(`最终识别结果: ${data.text}`, 'success')
        }
        break

      case 'error':
        setStatus('error')
        addLog(`识别错误: ${message || '未知错误'}`, 'danger')
        break

      case 'end':
        setStatus('completed')
        addLog('识别完成', 'success')
        break
    }
  }

  const appendText = (text: string) => {
    setResultText((prev) => prev + text)
    //     setResultText(`[背景音:键盘声]嗯...张总您好，呃这个...我是XX银行的高级理财经理小王[笑声]，今天给您call呢主要是推荐我们行新推的拳头产品——金穗稳盈增强版[咳嗽]。客户：收益怎么样？销售：啊这个绝对牛！历史年化保底百分之7.8，最高到过15点几，比存定期强十倍不止！

    // [停顿3秒]您知道吗？我们这款产品采用AI量化策略，嗯...底层资产分散配置，国债占比超30%，AAA级企业债50%以上[翻页声]，剩下是精选蓝筹股。客户问：会不会亏本啊？销售：安啦！我们跟保险公司合作，有本金保障机制，百分百安全！就算市场大跌，最差情况也有5%+的收益托底，零风险！[语气加重]

    // 不过[清嗓子]要提醒您，起购金额是捌拾万元整，追加的话五万块就行。对对对，现在申购还有限时优惠——管理费打四折，相当于多赚0.6-0.8%年化return呢！[语速加快]而且支持T+0快速赎回，比余额宝还灵活！

    // 客户迟疑：这个...收益能写进合同吗？销售马上说：哎呀合同里虽然不能写死收益，但您看过去五年数据[打开iPad]——2019年9.2%，20年11.5%，21年13.8%，22年股市那么差都有7.9%，今年到九月已经8.6%了[敲桌子]！我敢拍胸脯说，年化10%稳拿！

    // [突然小声]其实我们内部预测明年能到12%-15%，但这个您知道就行...对了，推荐朋友买还有额外奖励，介绍一个返现千分之五，上不封顶！[笑声结束]`)
  }

  const stopRecognition = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    addLog('正在停止识别...', 'warning')
  }

  const clearResults = () => {
    setResultText('等待开始识别...')
    setProgress(0)
    setStatus('idle')
    addLog('结果已清空', '')
    onClearPurgeData?.()
  }

  const copyResult = async () => {
    if (!resultText || resultText === '等待开始识别...') {
      addLog('识别结果为空，无法复制', 'warning')
      message.warning('识别结果为空')
      return
    }
    copyText(resultText)
  }

  const addLog = (logMessage: string, type: LogType = '') => {
    const timestamp = new Date().toLocaleTimeString()
    const logEntry = {
      value: `[${timestamp}] ${logMessage}`,
      type,
    }

    setLogs((prev) => [...prev, logEntry])

    // 限制日志条数
    if (logs.length > 50) {
      setLogs((prev) => prev.slice(1))
    }
  }

  const uploadProps: UploadProps = {
    name: 'audio',
    multiple: false,
    showUploadList: false,
    beforeUpload: (file: File) => {
      handleFileChange({ file })
      return false
    },
    accept: '.mp3,.m4a,.wav,.amr,.aac,.flac,.ogg,.mpga',
  }
  const onSubmit = () => {}
  const onChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const newValue = e.target.value
    setKeyPoints(newValue)
    // 调用父组件传入的回调函数，传递新的规则值
    onKeyPointsChange?.(newValue)
  }

  return (
    <div
      style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        minHeight: '100vh',
        padding: '24px',
      }}
    >
      <Card
        title={
          <div style={{ textAlign: 'center' }}>
            <PlayCircleOutlined style={{ marginRight: 8 }} />
            <span>语音智能质检助手</span>
            <div style={{ fontSize: 14, fontWeight: 'normal' }}>
              智能语音质检，稽查敏感词。支持多种音频格式的语音识别
            </div>
          </div>
        }
        style={{
          maxWidth: 800,
          margin: '0 auto',
          borderRadius: 15,
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
          border: 'none',
        }}
        styles={{
          header: {
            background: 'linear-gradient(45deg, #667eea, #764ba2)',
            color: 'white',
            borderRadius: '15px 15px 0 0',
            padding: '24px',
          },
        }}
      >
        {/* 配置区域 */}
        {/* <div style={{ marginBottom: 24 }}>
          <div style={{ marginBottom: 12 }}>
            <Text type="danger">
              注：知识库和本地上传仅选一种方式 <br />
              1、如选知识库，则请先维护知识库，上传录音或文本到知识库中后再选取
              <br />
              2、如选上传会议录音，则上传本地的录音文件，最大50M
            </Text>
          </div>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>
                <LinkOutlined style={{ marginRight: 8 }} />
                知识库
              </Text>
              <Select
                showSearch
                disabled={selectedFile !== null}
                style={{ width: '100%', marginTop: 8 }}
                filterOption={(input, option) =>
                  option?.label.toLowerCase().includes(input.toLowerCase())
                }
                mode="multiple"
                placeholder="请选择知识库"
                options={cardData.map((x) => ({
                  value: x.id,
                  label: x.libName,
                }))}
                onChange={(value) => {
                  setPersonalLibs(value)
                }}
              ></Select>
            </div>
          </Space>
        </div> */}

        {/* 文件上传区域 */}
        <div style={{ marginBottom: 24 }}>
          <Text strong style={{ marginRight: 8 }}>
            <UploadOutlined style={{ marginRight: 8 }} />
            选择音频文件
          </Text>
          <Text type="danger">上传本地的录音文件，最大不超过50M</Text>
          <div
            style={{
              border: '2px dashed #667eea',
              borderRadius: 10,
              padding: 24,
              textAlign: 'center',
              marginTop: 8,
              cursor: 'pointer',
            }}
          >
            <Upload {...uploadProps} disabled={personalLibs.length > 0}>
              <div>
                <CloudUploadOutlined
                  style={{ fontSize: 32, color: '#888', marginBottom: 16 }}
                />
                <p>点击选择文件或拖拽文件到此处</p>
                <Text type="secondary">
                  支持 MP3, M4A, WAV, AMR, AAC, FLAC, OGG 格式
                </Text>
              </div>
            </Upload>
          </div>
          {selectedFile && (
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                <Text>{selectedFile.name}</Text> (
                {formatFileSize(selectedFile.size)})
                <Link>
                  <DeleteOutlined
                    onClick={() => {
                      clearResults()
                      setSelectedFile(null)
                    }}
                    style={{ marginLeft: 8 }}
                  />
                </Link>
              </Text>
            </div>
          )}
        </div>

        {/* 控制按钮 */}
        <Space style={{ marginBottom: 24 }}>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={() => {
              clearResults()
              startRecognition()
            }}
            disabled={!selectedFile || isProcessing}
          >
            开始识别
          </Button>
          <Button
            danger
            icon={<StopOutlined />}
            onClick={stopRecognition}
            disabled={!isProcessing}
          >
            停止识别
          </Button>
          <Button icon={<DeleteOutlined />} onClick={clearResults}>
            清空结果
          </Button>
          <Button
            type="primary"
            onClick={() => {
              handleStart('数据预处理', resultText.slice(9))
            }}
            disabled={
              generating ||
              !(
                personalLibs.length > 0 ||
                (resultText !== '等待开始识别...' && !isProcessing)
              )
            }
          >
            数据清洗
          </Button>
        </Space>

        {selectedFile && (
          <>
            {/* 进度条 */}
            {isProcessing && (
              <div style={{ marginBottom: 24 }}>
                <Text strong>上传进度</Text>
                <Progress
                  percent={progress}
                  status={status === 'error' ? 'exception' : 'normal'}
                  strokeColor={status === 'completed' ? '#52c41a' : '#1890ff'}
                  style={{ marginTop: 8 }}
                />
              </div>
            )}
            <div style={{ marginBottom: 8 }}>
              <Text strong style={{ marginRight: 8 }}>
                ASR语音转文字内容
              </Text>
            </div>
            {/* 识别结果 */}
            <div
              style={{
                background: '#f8f9fa',
                borderRadius: 10,
                padding: 24,
                marginBottom: 24,
                border: '2px dashed #dee2e6',
                position: 'relative',
              }}
            >
              {/* <div
                style={{
                  position: 'absolute',
                  top: 10,
                  left: 10,
                  padding: '4px 12px',
                  borderRadius: 20,
                  background:
                    statusMap[status]?.color === 'default'
                      ? '#888'
                      : statusMap[status]?.color === 'processing'
                      ? '#1890ff'
                      : statusMap[status]?.color === 'success'
                      ? '#52c41a'
                      : statusMap[status]?.color === 'error'
                      ? '#ff4d4f'
                      : '#888',
                  color: 'white',
                  fontSize: 12,
                  fontWeight: 600,
                }}
              >
                {statusMap[status]?.text}
              </div> */}

              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: 16,
                }}
              >
                <Text strong>
                  <CommentOutlined style={{ marginRight: 8 }} />
                  识别结果
                </Text>
                <Button
                  icon={<CopyOutlined />}
                  onClick={copyResult}
                  disabled={!resultText || resultText === '等待开始识别...'}
                >
                  一键复制
                </Button>
              </div>

              <div
                className="scroll-container"
                style={{
                  height: 200,
                  overflowY: 'auto',
                  paddingRight: 10,
                  whiteSpace: 'pre-wrap',
                  wordWrap: 'break-word',
                }}
              >
                {purgeData ? purgeData : resultText}
              </div>
            </div>
            <Flex vertical gap="middle">
              <Form layout="vertical">
                <Form.Item
                  label={
                    <>
                      <span style={{ color: '#000', fontWeight: 'bold' }}>
                        质检规则
                      </span>
                      <span style={{ color: '#ff4d4f', marginLeft: 10 }}>
                        注：可在下方输入质检规则，如不修改，则按下面默认规则质检
                      </span>
                    </>
                  }
                >
                  <TextArea
                    showCount
                    maxLength={20000}
                    value={keyPoints}
                    placeholder="请输入提取要点"
                    style={{ height: 120, resize: 'none' }}
                    onChange={onChange}
                  />
                </Form.Item>
              </Form>
              <Button
                size="large"
                type="primary"
                onClick={() => {
                  handleStart('语音内容质检', resultText.slice(9), keyPoints)
                }}
                disabled={
                  generating ||
                  resultText === '等待开始识别...' ||
                  isProcessing ||
                  !purgeData
                }
              >
                开 始 质 检
              </Button>
            </Flex>
            {/* 日志区域 */}
            {/* <div>
              <Text strong>
                <ConsoleSqlOutlined style={{ marginRight: 8 }} />
                运行日志
              </Text>
              <div
                className="scroll-container"
                style={{
                  background: '#2d3748',
                  color: '#e2e8f0',
                  borderRadius: 10,
                  padding: 16,
                  maxHeight: 200,
                  overflowY: 'auto',
                  fontFamily: 'monospace',
                  fontSize: 14,
                  marginTop: 8,
                }}
              >
                {logs.map((log, index) => (
                  <div key={index} style={{ marginBottom: 8 }}>
                    {log.type ? (
                      <Text type={log.type}>{log.value}</Text>
                    ) : (
                      log.value
                    )}
                  </div>
                ))}
              </div>
            </div> */}
          </>
        )}
      </Card>
    </div>
  )
}

export default FunASRStreamTester
