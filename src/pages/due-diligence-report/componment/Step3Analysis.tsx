import { Card, message } from 'antd'
import { useEffect, useState } from 'react'
import { extractAllTagContents, mergeObjectsByKeyPro } from '@/utils/common'
import { extractAllJSONFromString } from '@/utils/json-extractor'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import RemarkBreaks from 'remark-breaks'
import RemarkMath from 'remark-math'
import './Step3Analysis.less'

export const Step3Analysis: React.FC<{
  data: string
  isEnd: boolean
  onContentChange: (data: { [key: string]: string }[]) => void
}> = ({ data, isEnd, onContentChange }) => {
  const [content, setContent] = useState<{ [key: string]: string }[]>([])
  useEffect(() => {
    if (isEnd) {
      const content = extractAllJSONFromString(data)
      const tagJsonList = extractAllTagContents(data, 'json')
      const contentPro = mergeObjectsByKeyPro(
        content.concat(tagJsonList).map(item => {
          try {
            return JSON.parse(item)
          } catch (error) {
            message.error('JSON解析失败')
            return {}
          }
        })
      )
      setContent(contentPro)
      onContentChange(contentPro)
    }
  }, [isEnd, data])

  return (
    <Card title='报告内容' className='due-step-card'>
      {isEnd ? (
        <>
          {content.map((item, index) => {
            return (
              <div key={index}>
                {Object.keys(item).map((key: string, i: number) => {
                  return (
                    <>
                      <div
                        key={index + '-' + i}
                        style={{ fontSize: '16px', fontWeight: 'bold' }}
                        dangerouslySetInnerHTML={{ __html: key }}
                      />
                      <ReactMarkdown
                        key={index + '--' + i}
                        className='markdown-body due-analysis-container'
                        remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}
                      >
                        {item[key]}
                      </ReactMarkdown>
                    </>
                  )
                })}
              </div>
            )
          })}
        </>
      ) : (
        <div className='analysis-result'>{data}</div>
      )}
    </Card>
  )
}

export default Step3Analysis
