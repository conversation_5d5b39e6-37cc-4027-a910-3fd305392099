import { useEffect, useState } from 'react'
import { But<PERSON>, Card, Flex, Upload } from 'antd'
import { UploadOutlined } from '@ant-design/icons'
import { uploadFile } from '@/api/template'
import StreamTypewriter from '@/component/StreamTypewriter'
import './Step2BasicInfo.less'

const DUE_DILIGENCE_REPORT_TOKEN = import.meta.env['VITE_DUE_DILIGENCE_REPORT_TOKEN'] || ''

export const Step2BasicInfo: React.FC<{
  data: any
  onUpload: (fileList: { id: string; name: string }[]) => void
  generating: boolean
}> = ({ data, onUpload, generating }) => {
  const [uploadedFiles, setUploadedFiles] = useState<{ id: string; name: string }[]>([])
  const beforeUpload = (file: File) => {
    uploadFile(file, DUE_DILIGENCE_REPORT_TOKEN).then(async response => {
      if (response.id) {
        setUploadedFiles(prevFiles => [...prevFiles, response])
      }
    })
    return false
  }

  useEffect(() => {
    if (uploadedFiles.length > 0) {
      onUpload(uploadedFiles)
    }
  }, [uploadedFiles, onUpload])

  return (
    <Card title='模板解析' className='due-step2-card'>
      <Flex vertical style={{ padding: '0 20px 0 20px' }} align='center'>
        <StreamTypewriter text={data} end={!generating} />

        <Upload multiple beforeUpload={beforeUpload}>
          <Button style={{ marginTop: '20px' }} type='primary' icon={<UploadOutlined />}>
            上传多个文档
          </Button>
        </Upload>
      </Flex>
    </Card>
  )
}

export default Step2BasicInfo
