import React, { useState, useCallback, useRef } from 'react'
import { message, Flex, Card, Upload, Button, Input } from 'antd'
import { uploadFile } from '@/api/template.ts'
import { getDiligenceReport } from '@/api/dueDiligenceReport'
import { CheckCircleFilled, InboxOutlined } from '@ant-design/icons'
import './template.less'

const DUE_DILIGENCE_REPORT_TOKEN = import.meta.env['VITE_DUE_DILIGENCE_REPORT_TOKEN'] || ''
interface TemplateProps {
  openKey: string
  openTip: () => void
  onUpload: (data: { res1: string; res2: string }) => void
  onUploadFile: (data: { name: string; id: string }) => void
  setCurrent: (current: number) => void
  generating: boolean
  setGenerating: (generating: boolean) => void
  managerName: string
  setManagerName: (managerName: string) => void
  onRes1Change: (res: string) => void
}
export const Template: React.FC<TemplateProps> = ({
  openKey,
  openTip,
  onUpload,
  onUploadFile,
  setCurrent,
  generating,
  setGenerating,
  managerName,
  setManagerName,
  onRes1Change
}) => {
  const [messageApi, contextHolder] = message.useMessage()
  const [uploadedFile, setUploadedFile] = useState<{ name: string; id: string }>()
  const resValue = useRef({
    res1: '',
    res2: ''
  })
  const beforeUpload = (file: File) => {
    if (!openKey) {
      openTip()
      return false
    }
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)
    resValue.current = {
      res1: '',
      res2: ''
    }
    if (['docx'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中'
      })
      setGenerating(true)
      uploadFile(file, DUE_DILIGENCE_REPORT_TOKEN).then(async response => {
        setGenerating(false)
        if (response.id) {
          setUploadedFile(response)
          onUploadFile(response)
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
        } else {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件上传失败',
            duration: 1
          })
        }
      })
    } else {
      messageApi.error('目前仅支持.docx类型的文件，请重新上传')
    }
    return false
  }

  const onFinished = () => {
    if (resValue.current.res1 && resValue.current.res2) {
      onUpload({
        res1: resValue.current.res1,
        res2: resValue.current.res2
      })
      setGenerating(false)
    }
  }

  const handleGenerationStart = useCallback(async () => {
    if (!openKey) {
      openTip()
      return
    }
    if (resValue.current.res1 && resValue.current.res2) {
      setCurrent(1)
      return
    }
    if (!managerName) {
      messageApi.error('请输入管理人名称')
      return
    }
    resValue.current = {
      res1: '',
      res2: ''
    }
    setCurrent(1)
    handleGenerationStart1()
    setGenerating(true)
    let res = ''
    try {
      await getDiligenceReport(
        {
          type: '模版解析',
          templateType: '页面结构',
          key: openKey || '',
          managerName,
          files: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: uploadedFile?.id
            }
          ]
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              onRes1Change(message || '')
            }
            if (finished) {
              resValue.current.res1 = res
              onFinished()
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }, [uploadedFile, managerName])

  const handleGenerationStart1 = useCallback(async () => {
    let res = ''
    try {
      await getDiligenceReport(
        {
          type: '模版解析',
          templateType: '模版结构',
          key: openKey || '',
          managerName,
          files: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: uploadedFile?.id
            }
          ]
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              resValue.current.res2 = res
              onFinished()
            }
          },
          onError: () => {},
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }, [uploadedFile, managerName])

  return (
    <>
      {contextHolder}
      <Flex gap='large'>
        <Card className='due-diligence-report-form'>
          <Flex vertical gap='middle'>
            <Input
              addonBefore='管理人'
              suffix=''
              placeholder='请输入管理人名称'
              disabled={!!(resValue.current.res1 && resValue.current.res2)}
              value={managerName}
              onChange={e => setManagerName(e.target.value)}
              maxLength={256}
            />
            <Upload.Dragger showUploadList={false} multiple={false} beforeUpload={beforeUpload}>
              <p className='ant-upload-drag-icon'>{uploadedFile ? <CheckCircleFilled /> : <InboxOutlined />}</p>
              <p className='ant-upload-text'>{uploadedFile ? uploadedFile.name : '点击或者将文件拖拽到这里上传'}</p>
              <p className='ant-upload-hint'>
                {uploadedFile ? (
                  '点击或者将文件拖拽到这里重新上传'
                ) : (
                  <>
                    <span>在这里上传您的尽调报告模版</span> <br />
                    <span>目前仅支持上传一个文件，支持.docx类型</span>
                  </>
                )}
              </p>
            </Upload.Dragger>
            <Button size='large' type='primary' disabled={!uploadedFile || generating} onClick={handleGenerationStart}>
              {resValue.current.res1 && resValue.current.res2 ? '下 一 步' : '开 始 解 析'}
            </Button>
          </Flex>
        </Card>
      </Flex>
    </>
  )
}

export default Template
