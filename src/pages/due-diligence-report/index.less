.due-diligence-report-container {
  width: 100vw;
  overflow: hidden;

  .custom-steps {
    padding: 20px 20vw 40px;
  }
  .steps-action {
    text-align: center;
    margin-top: 20px;
    margin-bottom: 20px;
  }
  /* 样式控制 */
  .steps-content-wrapper {
    position: relative;
    min-height: 400px;
  }

  .step-content {
    width: 100%;
    transition: opacity 0.3s, transform 0.3s;
  }

  .step-content.hidden {
    opacity: 0;
    width: 0;
    height: 0;
    pointer-events: none;
    transform: translateX(100%);
  }

  .step-content.active {
    opacity: 1;
    transform: translateX(0);
  }
}
