import React, { useState } from 'react'
import { Spin, message, Flex, Typography, <PERSON>, Button } from 'antd'
import Get<PERSON><PERSON> from '@/component/getKey'
import { Template } from './componment/template'
import Step2BasicInfo from './componment/Step2BasicInfo'
import Step3Analysis from './componment/Step3Analysis'
import { getDiligenceReport } from '@/api/dueDiligenceReport'
import { extractJSONFromString } from '@/utils/json-extractor'
import { extractContent } from '@/utils/common'
import './index.less'

const { Step } = Steps

export const DueDiligenceReport: React.FC = () => {
  const [key, setKey] = useState('')
  const [open, setOpen] = useState(false)
  const [managerName, setManagerName] = useState<string>('')
  const [messageApi, contextHolder] = message.useMessage()
  const [step3message, setStep3message] = useState<string>('')
  const [generating, setGenerating] = useState<boolean>(false)
  const [isEnd, setIsEnd] = useState<boolean>(false)
  const [res1, setRes1] = useState<string>('')
  const [templateData, setTemplateData] = useState<{ res1: string; res2: string }>({
    res1: '',
    res2: ''
  })
  const [fileList, setFileList] = useState<{ id: string; name: string }[]>([])
  const [current, setCurrent] = useState(0)
  const [countKey, setCountKey] = useState(0)
  const [uploadedFile, setUploadedFile] = useState<{ name: string; id: string }>()
  const [content, setContent] = useState<{ [key: string]: string }[]>([])

  const steps = [
    {
      title: '上传模板',
      content: (
        <Template
          openKey={key}
          openTip={() => setOpen(true)}
          managerName={managerName}
          setManagerName={setManagerName}
          onUpload={setTemplateData}
          setCurrent={setCurrent}
          generating={generating}
          setGenerating={setGenerating}
          onUploadFile={setUploadedFile}
          onRes1Change={(str: string) => {
            setRes1(p => p + str.replace(/^```markdown\s*|```|markdown$/g, ''))
          }}
          key={countKey}
        />
      )
    },
    {
      title: '模板解析',
      content: <Step2BasicInfo data={res1} onUpload={setFileList} generating={generating} key={countKey} />
    },
    {
      title: '生成报告',
      content: <Step3Analysis data={step3message} isEnd={isEnd} onContentChange={setContent} key={countKey} />
    }
  ]

  const handleGenerationStart = async () => {
    setStep3message('')
    setIsEnd(false)
    setGenerating(true)
    const res2 = extractJSONFromString(templateData.res2)
    let res = ''
    try {
      await getDiligenceReport(
        {
          type: '内容解析',
          info: res2 || '{}',
          key: key || '',
          managerName,
          files: fileList.map(item => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: item.id
          }))
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              setStep3message(p => p + message)
            }
            if (finished) {
              setGenerating(false)
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                setKey('')
                setOpen(true)
                return
              }
              setGenerating(false)
              setIsEnd(true)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  const nextStep = () => {
    if (!key) {
      setOpen(true)
      return
    }
    if (current === 1) {
      setCurrent(current + 1)
      handleGenerationStart()
    } else {
      setCurrent(current + 1)
    }
  }

  const downloadReport = async () => {
    if (!key) {
      setOpen(true)
      return
    }
    setGenerating(true)
    const res2 = extractJSONFromString(templateData.res2)
    let res = ''
    let obj1 = JSON.parse(res2 || '{}')
    let obj2 = Object.assign({}, ...content)
    const updatedObj = Object.keys(obj1).reduce<{ [key: string]: string }>((acc, key) => {
      if (obj2.hasOwnProperty(key)) {
        acc[key] = obj2[key]
      } else {
        acc[key] = obj1[key]
      }
      return acc
    }, {})

    try {
      await getDiligenceReport(
        {
          type: '模版下载',
          info: res2 || '{}',
          key: key || '',
          managerName,
          files: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: uploadedFile?.id
            }
          ],
          query: JSON.stringify(updatedObj)
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
              // 提取()中的内容
              const parenthesesContent = res.match(/\((.*?)\)/)
              const parenthesesResult = parenthesesContent ? parenthesesContent[1] : null

              // 提取[]中的内容
              const squareBracketsContent = res.match(/\[(.*?)\]/)
              const squareBracketsResult = squareBracketsContent ? squareBracketsContent[1] : null

              if (parenthesesResult && squareBracketsResult) {
                const link = document.createElement('a')
                link.href = parenthesesResult
                link.download = `尽调报告${squareBracketsResult}`
                document.body.appendChild(link)
                link.click()
                link.remove()
              }

              setGenerating(false)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  return (
    <div className='due-diligence-report-container'>
      {contextHolder}
      <GetKey open={open} onClose={setOpen} onChange={setKey} />
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      <Flex className='toolbar' justify='center'>
        <Typography.Text className='title-text'>尽调报告生成</Typography.Text>
      </Flex>
      <div className='steps-container'>
        <Steps current={current} className='custom-steps'>
          {steps.map(item => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <div className='steps-content'>
          {steps.map((step, index) => (
            <div key={index} className={`step-content ${index === current ? 'active' : 'hidden'}`}>
              {step.content}
            </div>
          ))}
        </div>

        <div className='steps-action'>
          {/* {current > 0 && (
            <Button style={{ marginRight: 8 }} onClick={() => setCurrent(current - 1)}>
              上一步
            </Button>
          )} */}
          {current < steps.length - 1 && current !== 0 && (
            <Button type='primary' onClick={nextStep}>
              下一步
            </Button>
          )}
          {current === steps.length - 1 && (
            <>
              <Button
                style={{ marginRight: 8 }}
                onClick={() => {
                  setCurrent(0)
                  setStep3message('')
                  setRes1('')
                  setCountKey(prev => prev + 1)
                }}
              >
                重新生成
              </Button>
              <Button type='primary' onClick={() => downloadReport()}>
                下载报告
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default DueDiligenceReport
