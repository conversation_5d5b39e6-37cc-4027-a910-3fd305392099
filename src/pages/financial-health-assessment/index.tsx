import React, { useState } from 'react'
import { Upload, Button, message, Card, Typography, Spin, Layout, Flex } from 'antd'
import { CheckCircleFilled, InboxOutlined } from '@ant-design/icons'
import { financialHealthAssessment } from '@/api/financialHealthAssessment'
import { uploadFile } from '@/api/template'
import { extractContent } from '@/utils/common'
import StreamTypewriter from '@/component/StreamTypewriter'
import './index.less'

const { Title } = Typography
const token = import.meta.env['VITE_FINANCIALHEALTHASSESSMENT_TOKEN'] || ''

export const FinancialHealthAssessment: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState(false)
  const [mk, setMk] = useState('')
  const [isShowMk, setIsShowMk] = useState(false)
  const [streamTypewriterKey, setStreamTypewriterKey] = useState(1)
  const [uploadedFile, setUploadedFile] = useState<{ id: string; name: string }>({ id: '', name: '' })
  const scrollRef = React.useRef<HTMLDivElement>(null)

  const beforeUpload = (file: File) => {
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)?.toLowerCase()
    if (!['txt', 'docx', 'mdx', 'pdf', 'html', 'xlsx', 'xls', 'csv', 'md', 'html'].includes(originalFileExt)) {
      return false
    }
    messageApi.open({
      key: 'uploading',
      type: 'loading',
      content: '文件上传中'
    })
    setGenerating(true)
    uploadFile(file, token)
      .then(response => {
        setGenerating(false)
        if (response && response.id) {
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
          setUploadedFile(response)
          return false // 阻止自动上传
        } else {
          throw new Error('上传失败：未收到有效的响应')
        }
      })
      .catch(error => {
        setGenerating(false)
        messageApi.open({
          key: 'uploading',
          type: 'error',
          content: `文件上传失败: ${error.message}`,
          duration: 2
        })
      })
    return false // 阻止自动上传
  }

  const handleDownload = async () => {
    if (!uploadedFile.id) {
      messageApi.error('文件未上传')
      return
    }
    setStreamTypewriterKey(streamTypewriterKey + 1)
    setMk('')
    setIsShowMk(true)
    setGenerating(true)
    messageApi.success('开始校验，请稍候...')
    let res = ''
    try {
      await financialHealthAssessment(
        {
          files: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: uploadedFile.id
            }
          ]
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              setMk(p => p + message || '')
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {
            setGenerating(false)
          }
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  return (
    <div className='review-material-container'>
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      {contextHolder}
      <Layout style={{ height: '100%' }}>
        <Layout.Sider width={isShowMk ? '600px' : '100%'} style={{ backgroundColor: '#f0f2f5', padding: '0 20px' }}>
          <Card className='review-material-card'>
            <Title level={2} className='page-title'>
              财务健康度评估助手
            </Title>
            <Upload.Dragger showUploadList={false} multiple={false} beforeUpload={beforeUpload}>
              <p className='ant-upload-drag-icon'>{uploadedFile.id ? <CheckCircleFilled /> : <InboxOutlined />}</p>
              <p className='ant-upload-text'>
                {uploadedFile.id ? uploadedFile.name : '点击或者将文件拖拽到这里进行上传'}
              </p>
              <p className='ant-upload-hint'>
                {uploadedFile.id ? (
                  '点击或者将文件拖拽到这里重新上传'
                ) : (
                  <>
                    <span>支持 txt、 docx、 mdx、 pdf、 html、 xlsx、 xls、 csv、 md、 htm格式</span>
                    <br />
                    <span>注：建议上传docx格式文件，系统仅支持docx文件标注输出。</span>
                  </>
                )}
              </p>
            </Upload.Dragger>

            <Button
              type='primary'
              onClick={handleDownload}
              size='large'
              loading={generating}
              block
              style={{ marginTop: '20px' }}
            >
              开始校验
            </Button>
          </Card>
        </Layout.Sider>
        {isShowMk && (
          <Layout.Content style={{ padding: 24, background: '#fff' }}>
            <Flex align='center' justify='center' style={{ height: '50px' }}>
              <Title level={3}>评估结果</Title>
            </Flex>

            <div
              className='scroll-container'
              ref={scrollRef}
              style={{ height: 'calc(100vh - 98px)', overflowY: 'auto' }}
            >
              <StreamTypewriter
                key={streamTypewriterKey}
                text={mk}
                end={!generating}
                onchange={() => {
                  scrollRef.current?.scrollTo({ top: scrollRef.current.scrollHeight, behavior: 'smooth' })
                }}
                components={{
                  p: ({ node, ...props }: any) => <p style={{ whiteSpace: 'pre-line', marginTop: 10 }} {...props} />
                }}
              />
            </div>
          </Layout.Content>
        )}
      </Layout>
    </div>
  )
}

export default FinancialHealthAssessment
