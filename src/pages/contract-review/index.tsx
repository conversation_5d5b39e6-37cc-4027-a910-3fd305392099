import { useState, useEffect, useCallback } from 'react'
import { Upload, Button, Typography, Card, Spin, message, Form, Flex } from 'antd'
import { CheckCircleFilled, InboxOutlined, DeleteOutlined } from '@ant-design/icons'
import { uploadFile } from '@/api/template'
import { reviewContract } from '@/api/contractReview'
import StreamTypewriter from '@/component/StreamTypewriter'
import { NoData } from '@/component/NoData'
import { extractContent } from '@/utils/common'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import RemarkBreaks from 'remark-breaks'
import RemarkMath from 'remark-math'
import rehypeRaw from 'rehype-raw'
import './index.less'

const REVIEW_CONTRACT_TOKEN = import.meta.env['VITE_REVIEW_CONTRACT_TOKEN'] || ''
export const ContractReview = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [uploadedFiles, setUploadedFiles] = useState<{ id: string; name: string }[]>([])
  const [startSending, setStartSending] = useState<boolean>(false)
  const [generating, setGenerating] = useState<boolean>(false)
  const [markdownTable, setMarkdownTable] = useState('')
  const [messagesEnd, setMessagesEnd] = useState<boolean>(false)
  const [messages] = useState<string>('')
  const components = {
    pre: ({ node, ...props }: any) => <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }} {...props} />,
    think: ({ node, ...props }: any) => (
      <span>
        <strong style={{ color: '#007bff' }}>💡 思考：</strong>
        {props.children}
      </span>
    ),
    article: ({ node, ...props }: any) => {
      let parsedChildren = []
      let obj = {}
      try {
        let arr = JSON.parse(props.children)
        if (Array.isArray(arr)) {
          parsedChildren = arr
        } else {
          obj = arr
        }
      } catch (error) {
        parsedChildren = []
      }

      return (
        <>
          <br></br>
          {parsedChildren.map((item: any, index: number) => (
            <>
              <span key={index} style={{ color: 'red' }}>
                {item}
              </span>
              <br></br>
            </>
          ))}
          <span>{JSON.stringify(obj) === '{}' ? '' : JSON.stringify(obj)}</span>
        </>
      )
    }
  }

  useEffect(() => {
    if (uploadedFiles.length > 0) {
      messageApi.open({
        key: 'uploading',
        type: 'success',
        content: '文件上传成功',
        duration: 1
      })
    }
  }, [uploadedFiles, messageApi])

  const beforeUpload = (file: File) => {
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)?.toLowerCase()
    if (['docx'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中'
      })

      uploadFile(file, REVIEW_CONTRACT_TOKEN).then(async response => {
        if (response.id) {
          setUploadedFiles(prevFiles => [...prevFiles, response])
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
        } else {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件上传失败',
            duration: 1
          })
        }
      })
    } else {
      messageApi.error('目前仅支持.docx类型的文件，请您将文件转成这些格式后再次进行上传')
    }
    return false
  }

  const handleDelete = (fileId: string) => {
    setUploadedFiles(prevFiles => prevFiles.filter(file => file.id !== fileId))
  }

  const handleGenerationStart = () => {
    if (uploadedFiles.length === 0) {
      messageApi.error('请上传至少一个文件')
      return
    }
    setStartSending(true)
    const fileIds = uploadedFiles.map(file => file.id)
    handleGeneration(fileIds)
    setMarkdownTable('')
  }
  const handleGeneration = useCallback(async (fileIds: string[]) => {
    setMessagesEnd(false)
    setGenerating(true)
    let accumulatedMessages = ''

    try {
      await reviewContract(
        {
          query: '1',
          files: fileIds.map(x => ({
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: x
          }))
        },
        {
          onMessage: (text: string | null, finished: boolean) => {
            if (text) {
              setGenerating(false)
              accumulatedMessages += text || ''
              setMarkdownTable(accumulatedMessages)
            }
            if (finished) {
              setMessagesEnd(true)
              const errorStr = extractContent(accumulatedMessages, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
              console.log(accumulatedMessages)
              accumulatedMessages = accumulatedMessages.replaceAll('```json', '<article>')
              accumulatedMessages = accumulatedMessages.replaceAll('```', '</article>')
              setMarkdownTable(accumulatedMessages)
            }
          },
          onError: () => {
            setGenerating(false)
            setMessagesEnd(true)
          },
          onFinish: () => {
            setGenerating(false)
            setMessagesEnd(true)
          }
        }
      )
    } catch (err) {
      setGenerating(false)
      setMessagesEnd(true)
    }
  }, [])

  return (
    <>
      {contextHolder}
      <Spin tip='处理中...' spinning={generating} fullscreen size='large' />
      <div className='contract-review-container'>
        {/* 左侧面板 */}
        <div className='contract-left-panel'>
          <Typography.Text className='title-text'>中证-合同审查</Typography.Text>
          <Form layout='vertical'>
            <Form.Item label='上传文件'>
              <Upload.Dragger multiple showUploadList={false} beforeUpload={beforeUpload}>
                <div className='ant-upload-drag-icon'>
                  {uploadedFiles.length > 0 ? <CheckCircleFilled /> : <InboxOutlined />}
                </div>
                <div className='ant-upload-hint'>
                  <span>拖拽文件到此处上传</span>
                  <br />
                  <span style={{ fontSize: '12px', color: '#999' }}>或点击选择文件</span>
                </div>
              </Upload.Dragger>

              {uploadedFiles.length > 0 && (
                <div className='file-list-contract'>
                  {uploadedFiles.map(file => (
                    <div key={file.id} className='file-item'>
                      <span>{file.name}</span>
                      <DeleteOutlined onClick={() => handleDelete(file.id)} style={{ cursor: 'pointer' }} />
                    </div>
                  ))}
                </div>
              )}
            </Form.Item>
            <Form.Item>
              <Button
                type='primary'
                disabled={uploadedFiles.length === 0 || generating}
                onClick={handleGenerationStart}
                style={{ width: '100%' }}
              >
                点击生成
              </Button>
            </Form.Item>
          </Form>
        </div>

        {/* 右侧面板 */}
        <div className='contract-right-panel'>
          <div className='contract-action-buttons'>
            <Typography.Text className='contract-section-title'>合同分析</Typography.Text>
          </div>

          {startSending && (
            <Flex
              justify='center'
              style={{
                marginTop: 15,
                overflow: 'auto',
                width: '100%'
              }}
            >
              {markdownTable ? (
                <Card style={{ width: '100%', overflow: 'auto' }}>
                  {!messagesEnd ? (
                    <StreamTypewriter text={markdownTable} end={messagesEnd} components={components} />
                  ) : (
                    <>
                      <ReactMarkdown
                        className='markdown-body step3Analysis-container'
                        remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}
                        rehypePlugins={[rehypeRaw]}
                        components={components}
                      >
                        {markdownTable}
                      </ReactMarkdown>
                    </>
                  )}
                </Card>
              ) : (
                <Flex justify='justify' align='center' vertical>
                  <Typography.Text>{messages || '正在提取文件信息，请不要关闭或刷新页面'}</Typography.Text>
                </Flex>
              )}
            </Flex>
          )}
          {!startSending && <NoData description=' ' text='暂无内容' />}
        </div>
      </div>
    </>
  )
}

export default ContractReview
