import React, { useState, useCallback, useRef, useEffect } from 'react'
import { message, Flex, Card, Upload, Button, Typography, Tag } from 'antd'
import { uploadFile } from '@/api/template'
import { trustMonthlyReport } from '@/api/trustMonthlyReport'
import { InboxOutlined } from '@ant-design/icons'
import './template.less'

const token = import.meta.env['VITE_TRUSTMONTHLYREPORT_TOKEN'] || ''

interface TemplateProps {
  openKey: string
  openTip: () => void
  onUpload: (data: { res1: string; res2: string; uploadedFile: { id: string; name: string } }) => void
  onFileListChange: (uploadedFileList: { id: string; name: string }[]) => void
  setCurrent: (current: number) => void
  generating: boolean
  setGenerating: (generating: boolean) => void
  onRes1Change: (res: string) => void
}
export const Template: React.FC<TemplateProps> = ({
  openKey,
  openTip,
  onFileListChange,
  onUpload,
  setCurrent,
  generating,
  setGenerating,
  onRes1Change
}) => {
  const [messageApi, contextHolder] = message.useMessage()
  const resValue = useRef({
    res1: '',
    res2: ''
  })

  const [uploadedFile, setUploadedFile] = useState<{ id: string; name: string }>({ id: '', name: '' })
  const [uploadedFileList, setUploadedFileList] = useState<{ id: string; name: string }[]>([])
  const allowedFileExtensions = (type: string): string[] => {
    if (type === '1') {
      return ['docx', 'pdf']
    }
    return ['txt', 'docx', 'mdx', 'pdf', 'html', 'xlsx', 'xls', 'csv', 'md', 'htm'] // 允许全部格式
  }
  const beforeUpload = (file: File, type: string) => {
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)?.toLowerCase()
    if (!allowedFileExtensions(type).includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'error',
        content: `文件格式不正确，请上传${allowedFileExtensions(type).join(',')}格式的文件`,
        duration: 2
      })
      return false
    }
    resValue.current = {
      res1: '',
      res2: ''
    }
    messageApi.open({
      key: 'uploading',
      type: 'loading',
      content: '文件上传中'
    })
    setGenerating(true)
    uploadFile(file, token)
      .then(response => {
        setGenerating(false)
        if (response && response.id) {
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
          if (type === '1') {
            setUploadedFile(response)
          } else if (type === '2') {
            setUploadedFileList(prevList => [...prevList, response])
          }
          return false // 阻止自动上传
        } else {
          throw new Error('上传失败：未收到有效的响应')
        }
      })
      .catch(error => {
        setGenerating(false)
        messageApi.open({
          key: 'uploading',
          type: 'error',
          content: `文件上传失败: ${error.message}`,
          duration: 2
        })
      })
    return false // 阻止自动上传
  }

  useEffect(() => {
    onFileListChange(uploadedFileList)
  }, [uploadedFileList])

  const onFinished = () => {
    if (resValue.current.res1 && resValue.current.res2) {
      onUpload({
        res1: resValue.current.res1,
        res2: resValue.current.res2,
        uploadedFile
      })
      setGenerating(false)
    }
  }

  const handleGenerationStart = useCallback(async () => {
    // if (!openKey) {
    //   openTip()
    //   return
    // }
    setCurrent(1)
    if (resValue.current.res1 && resValue.current.res2) {
      return
    }
    resValue.current = {
      res1: '',
      res2: ''
    }
    handleGenerationStart1()
    setGenerating(true)
    let res = ''
    try {
      await trustMonthlyReport(
        {
          type: '模板文件',
          template_type: '页面结构',
          key: openKey || '',
          template: {
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: uploadedFile.id
          }
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              onRes1Change(message || '')
            }
            if (finished) {
              resValue.current.res1 = res
              onFinished()
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }, [uploadedFile])

  const handleGenerationStart1 = useCallback(async () => {
    let res = ''
    try {
      await trustMonthlyReport(
        {
          type: '模板文件',
          template_type: '模板结构',
          key: openKey || '',
          template: {
            type: 'document',
            transfer_method: 'local_file',
            upload_file_id: uploadedFile.id
          }
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              resValue.current.res2 = res
              onFinished()
            }
          },
          onError: () => {},
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }, [uploadedFile])

  return (
    <>
      {contextHolder}
      <Flex gap='large'>
        <Card className='trust-monthly-report-form'>
          <Typography.Title level={2} className='page-title'>
            信托项目运营报告生成
          </Typography.Title>
          <Typography.Text type='secondary' className='page-description'>
            你好，作为资深信托公司产品经理，我可以结合您上传的资料和模板，为您撰写信托运营月度报告。
          </Typography.Text>
          <Typography.Title level={5} style={{ padding: '10px 0', margin: 0 }}>
            上传一份模板文件
          </Typography.Title>
          <Upload.Dragger
            showUploadList={false}
            multiple={false}
            beforeUpload={(file: File) => beforeUpload(file, '1')}
          >
            <p className='reporting-upload-drag-icon'>
              <InboxOutlined />
            </p>
            <p>请拖拽文件到此处或点击上传文件按钮</p>
            <p>支持 pdf、docx 格式</p>
          </Upload.Dragger>
          <Typography.Title level={5} style={{ padding: '10px 0', margin: 0 }}>
            上传一份或多份资料文件
          </Typography.Title>
          <Upload.Dragger showUploadList={false} multiple beforeUpload={(file: File) => beforeUpload(file, '2')}>
            <p className='reporting-upload-drag-icon'>
              <InboxOutlined />
            </p>
            <p>请拖拽文件到此处或点击上传文件按钮</p>
            <p>支持 docx, xlsx, txt, mdx, pdf, html, xls, csv, md, htm 格式</p>
          </Upload.Dragger>
          <div>
            {uploadedFile.id && (
              <>
                <p style={{ padding: '4px 0' }}>模板文件：</p>
                <Tag
                  closeIcon
                  onClose={() => {
                    setUploadedFile({ id: '', name: '' })
                    return false
                  }}
                >
                  {uploadedFile.name}
                </Tag>
              </>
            )}
          </div>
          {uploadedFileList.length > 0 && (
            <>
              <p style={{ padding: '4px 0' }}>资料文件：</p>
              {uploadedFileList.map(x => (
                <p key={x.id}>
                  <Tag
                    closeIcon
                    style={{ marginTop: 4 }}
                    onClose={() => {
                      setUploadedFileList(prevList => prevList.filter(y => y.id !== x.id))
                      return false
                    }}
                  >
                    {x.name}
                  </Tag>
                </p>
              ))}
            </>
          )}
          <Button
            size='large'
            type='primary'
            style={{ width: '100%', marginTop: 20 }}
            disabled={!uploadedFile || generating}
            onClick={handleGenerationStart}
          >
            {resValue.current.res1 && resValue.current.res2 ? '下 一 步' : '开 始 解 析'}
          </Button>
        </Card>
      </Flex>
    </>
  )
}

export default Template
