import React, { useState } from 'react'
import { Spin, message, Flex, Typography, <PERSON>, Button } from 'antd'
// import GetKey from '@/component/getKey'
import { Template } from './componment/template'
import Step2BasicInfo from './componment/Step2BasicInfo'
import Step3Analysis from './componment/Step3Analysis'
import { trustMonthlyReport } from '@/api/trustMonthlyReport'
import { extractJSONFromString } from '@/utils/json-extractor'
import { extractContent } from '@/utils/common'
import './index.less'

const { Step } = Steps

export const TrustMonthlyReport: React.FC = () => {
  const [key, setKey] = useState('')
  const [open, setOpen] = useState(false)
  const [messageApi, contextHolder] = message.useMessage()
  const [step3message, setStep3message] = useState<string>('')
  const [generating, setGenerating] = useState<boolean>(false)
  const [isEnd, setIsEnd] = useState<boolean>(false)
  const [res1, setRes1] = useState<string>('')
  const [templateData, setTemplateData] = useState<{ res1: string; res2: string }>({
    res1: '',
    res2: ''
  })
  const [uploadedFile, setUploadedFile] = useState<{ id: string; name: string }>({ id: '', name: '' })
  const [uploadedFileList, setUploadedFileList] = useState<{ id: string; name: string }[]>([])
  const [current, setCurrent] = useState(0)
  const [countKey, setCountKey] = useState(0)

  const steps = [
    {
      title: '上传资料',
      content: (
        <Template
          openKey={key}
          openTip={() => setOpen(true)}
          onUpload={data => {
            setTemplateData({
              res1: data.res1,
              res2: data.res2
            })
            setUploadedFile(data.uploadedFile)
          }}
          onFileListChange={(list: any[]) => {
            setUploadedFileList(list)
          }}
          setCurrent={setCurrent}
          generating={generating}
          setGenerating={setGenerating}
          onRes1Change={(str: string) => {
            setRes1(p => p + str.replace(/^```markdown\s*|```|markdown$/g, ''))
          }}
          key={countKey}
        />
      )
    },
    {
      title: '审批材料解析',
      content: <Step2BasicInfo data={res1} generating={generating} key={countKey} />
    },
    { title: '报告下载', content: <Step3Analysis data={step3message} isEnd={isEnd} key={countKey} /> }
  ]

  const handleGenerationStart = async () => {
    setStep3message('')
    setIsEnd(false)
    setGenerating(true)
    const res2 = extractJSONFromString(templateData.res2)
    let res = ''
    try {
      await trustMonthlyReport(
        {
          type: '相关文件',
          Relevant_documents: uploadedFileList.map(item => ({
            upload_file_id: item.id,
            type: 'document',
            transfer_method: 'local_file'
          })),
          info: res2 || '{}'
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              setStep3message(p => p + message)
            }
            if (finished) {
              setIsEnd(true)
              setGenerating(false)
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                setKey('')
                setOpen(true)
                return
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  const nextStep = () => {
    // if (!key) {
    //   setOpen(true)
    //   return
    // }
    if (current === 1) {
      setCurrent(current + 1)
      if (step3message) return
      handleGenerationStart()
    } else {
      setCurrent(current + 1)
    }
  }

  const downloadReport = async () => {
    // if (!key) {
    //   setOpen(true)
    //   return
    // }
    setGenerating(true)
    const res2 = extractJSONFromString(templateData.res2)
    let res = ''

    try {
      await trustMonthlyReport(
        {
          type: '报告下载',
          info: res2 || '{}',
          key: key || '',
          files: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: uploadedFile?.id
            }
          ],
          query: step3message
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
              // 提取()中的内容
              const parenthesesContent = res.match(/\((.*?)\)/)
              const parenthesesResult = parenthesesContent ? parenthesesContent[1] : null

              // 提取[]中的内容
              const squareBracketsContent = res.match(/\[(.*?)\]/)
              const squareBracketsResult = squareBracketsContent ? squareBracketsContent[1] : null

              if (parenthesesResult && squareBracketsResult) {
                const link = document.createElement('a')
                link.href = `${parenthesesResult}`
                link.download = `信托项目运营报告${squareBracketsResult}`
                document.body.appendChild(link)
                link.click()
                link.remove()
              }
              setGenerating(false)
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {}
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  return (
    <div className='trust-monthly-report-container'>
      {contextHolder}
      {/* <GetKey open={open} onClose={setOpen} onChange={setKey} /> */}
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      <Flex className='toolbar' justify='center'>
        <Typography.Text className='title-text'>信托项目运营报告生成</Typography.Text>
      </Flex>
      <div className='steps-container'>
        <Steps current={current} className='custom-steps'>
          {steps.map(item => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <div className='steps-content'>
          {steps.map((step, index) => (
            <div key={index} className={`step-content ${index === current ? 'active' : 'hidden'}`}>
              {step.content}
            </div>
          ))}
        </div>

        <div className='steps-action'>
          {current > 0 && (
            <Button style={{ marginRight: 8 }} onClick={() => setCurrent(current - 1)}>
              上一步
            </Button>
          )}
          {current < steps.length - 1 && current !== 0 && (
            <Button type='primary' onClick={nextStep}>
              下一步
            </Button>
          )}
          {current === steps.length - 1 && (
            <>
              <Button
                style={{ marginRight: 8 }}
                onClick={() => {
                  setCurrent(0)
                  setRes1('')
                  setStep3message('')
                  setCountKey(prev => prev + 1)
                }}
              >
                重新生成
              </Button>
              <Button type='primary' onClick={() => downloadReport()}>
                下载报告
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default TrustMonthlyReport
