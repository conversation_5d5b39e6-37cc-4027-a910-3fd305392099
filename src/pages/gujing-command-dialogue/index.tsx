import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Card, Select, Button, Radio, Tag, Space, Layout, Modal, Avatar, Typography } from 'antd'
import { wordPronunciation, getEvaluatePronunciation } from '@/api/voiceFetch'
import scenariosData from './termData.json'
import { useAudioRecorder } from '@/hooks/useAudioRecorder'
import './index.less'

const { Meta } = Card
const { Header } = Layout

// 添加类型定义
interface ScenarioDetailModalProps {
  visible: boolean
  onClose: () => void
  scenario: any // 根据实际类型调整
  isBookmarked: boolean
  onToggleBookmark: () => void
  isRecording: boolean
  recordingSeconds: number
  audioUrl: string
  toggleRecording: () => void
  playCommand: () => void
  formatTime: (seconds: number) => string
  currentScenarioIndex: number
  scenariosLength: number
  onPrevious: () => void
  onNext: () => void
  isEvaluating: boolean
  evaluationResult: any
}

// 新建 ScenarioDetailModal 组件
const ScenarioDetailModal = ({
  visible,
  onClose,
  scenario,
  isBookmarked,
  onToggleBookmark,
  isRecording,
  recordingSeconds,
  audioUrl,
  toggleRecording,
  playCommand,
  formatTime,
  currentScenarioIndex,
  scenariosLength,
  onPrevious,
  onNext,
  isEvaluating,
  evaluationResult
}: ScenarioDetailModalProps) => {
  return (
    <Modal open={visible} onCancel={onClose} width={1000} footer={null} destroyOnClose style={{ padding: '20px' }}>
      <div className='scenario-detail'>
        {/* 场景头部 */}
        <Card className='scenario-header'>
          <div className='header-content'>
            <div className='header-title'>
              <Button
                type='text'
                icon={
                  <svg
                    className='bookmark-icon'
                    viewBox='0 0 24 24'
                    width='25'
                    height='25'
                    fill={isBookmarked ? 'orange' : 'none'}
                    stroke={isBookmarked ? 'none' : 'currentColor'}
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z'
                    />
                  </svg>
                }
                onClick={onToggleBookmark}
                style={{
                  color: 'white' // 确保未选中时轮廓颜色为白色
                }}
              />
              <h2 style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '90%' }}>
                {scenario?.scenario}
              </h2>
            </div>
          </div>
        </Card>

        {/* 场景内容 */}
        <div className='scenario-content'>
          {/* 角色和情境 */}
          <div style={{ marginBottom: '24px', maxWidth: '800px' }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
              <div
                style={{
                  width: '32px',
                  height: '32px',
                  borderRadius: '50%',
                  backgroundColor: '#e6f4ff',
                  color: '#1677ff',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '12px'
                }}
              >
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  style={{ width: '16px', height: '16px' }}
                  fill='none'
                  viewBox='0 0 24 24'
                  stroke='currentColor'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z'
                  />
                </svg>
              </div>
              <div>
                <div style={{ fontSize: '12px', color: '#64748b' }}>角色扮演</div>
                <div style={{ fontWeight: 500 }}>{scenario?.roles.join(' → ')}</div>
              </div>
            </div>

            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div
                style={{
                  width: '32px',
                  height: '32px',
                  borderRadius: '50%',
                  backgroundColor: '#f0fdf4',
                  color: '#15803d',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '12px'
                }}
              >
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  style={{ width: '16px', height: '16px' }}
                  fill='none'
                  viewBox='0 0 24 24'
                  stroke='currentColor'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10'
                  />
                </svg>
              </div>
              <div>
                <div style={{ fontSize: '12px', color: '#64748b' }}>场景描述</div>
                <div style={{ fontWeight: 500 }}>{scenario?.scenario}</div>
              </div>
            </div>
          </div>

          {/* A角色部分 */}
          <div className='role-section'>
            <Space align='start' size={12}>
              <Avatar
                size={32}
                style={{
                  backgroundColor: '#e6f4ff',
                  color: '#1677ff',
                  fontSize: '14px',
                  fontWeight: 500
                }}
              >
                A
              </Avatar>
              <div className='flex-1'>
                <Typography.Text type='secondary' style={{ fontSize: '14px', display: 'block', marginBottom: '8px' }}>
                  {scenario?.roles[0]}
                </Typography.Text>
                <div className='command-box'>
                  <Typography.Text style={{ fontSize: '15px', display: 'block', marginBottom: '8px' }}>
                    {scenario?.command}
                  </Typography.Text>
                  <Typography.Text
                    type='secondary'
                    style={{ fontSize: '14px', display: 'block', marginBottom: '16px' }}
                  >
                    {scenario?.command_zh}
                  </Typography.Text>
                  <Button
                    type='text'
                    icon={
                      <svg viewBox='0 0 24 24' width='20' height='20' fill='none' stroke='currentColor' strokeWidth='2'>
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          d='M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z'
                        />
                        <path strokeLinecap='round' strokeLinejoin='round' d='M21 12a9 9 0 11-18 0 9 9 0 0118 0z' />
                      </svg>
                    }
                    onClick={playCommand}
                    style={{ color: '#1677ff' }}
                  >
                    播放指令
                  </Button>
                </div>
              </div>
            </Space>
          </div>

          {/* B角色部分 */}
          <div className='role-section'>
            <Space align='start' size={12} style={{ width: '100%' }}>
              <Avatar
                size={32}
                style={{
                  backgroundColor: '#dcfce7',
                  color: '#16a34a',
                  fontSize: '14px',
                  fontWeight: 500,
                  flexShrink: 0
                }}
              >
                B
              </Avatar>
              <div style={{ flex: 1, width: 'calc(100% - 44px)' }}>
                <Typography.Text type='secondary' style={{ fontSize: '14px', display: 'block', marginBottom: '8px' }}>
                  {scenario?.roles[1]}
                </Typography.Text>
                <div className='record-box'>
                  <Typography.Text
                    italic
                    type='secondary'
                    style={{ fontSize: '14px', display: 'block', marginBottom: '12px' }}
                  >
                    请录音回复指令：
                  </Typography.Text>
                  <div className='record-controls'>
                    <Button
                      type='text'
                      className={`record-btn ${isRecording ? 'recording' : ''}`}
                      onClick={toggleRecording}
                      style={{
                        width: '48px',
                        height: '48px',
                        background: isRecording ? '#fee2e2' : '#ef4444',
                        color: isRecording ? '#ef4444' : '#ffffff',
                        borderRadius: '50%',
                        padding: 0,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        border: 'none',
                        margin: '0 auto'
                      }}
                      icon={
                        isRecording ? (
                          <svg viewBox='0 0 24 24' style={{ width: '24px', height: '24px' }}>
                            <rect x='7' y='7' width='10' height='10' rx='1' fill='currentColor' />
                          </svg>
                        ) : (
                          <svg viewBox='0 0 24 24' style={{ width: '24px', height: '24px' }}>
                            <path
                              d='M12 14a3 3 0 003-3V5a3 3 0 00-6 0v6a3 3 0 003 3zm0-14a5 5 0 015 5v6a5 5 0 01-10 0V5a5 5 0 015-5zm6 10h-1a1 1 0 000 2h1a1 1 0 100-2zM7 12H6a1 1 0 100 2h1a1 1 0 100-2zm5 6c2.67 0 8 1.33 8 4v1a1 1 0 01-1 1H5a1 1 0 01-1-1v-1c0-2.67 5.33-4 8-4z'
                              fill='currentColor'
                            />
                          </svg>
                        )
                      }
                    />
                    {isRecording && (
                      <Typography.Text
                        type='secondary'
                        style={{ textAlign: 'center', display: 'block', marginTop: '8px', fontSize: '14px' }}
                      >
                        正在录音... {formatTime(recordingSeconds)}
                      </Typography.Text>
                    )}
                    {audioUrl && (
                      <div className='audio-wrapper'>
                        <audio style={{ width: '100%' }} src={audioUrl} controls className='audio-player' />
                      </div>
                    )}

                    {/* 参考回复 */}
                    <div className='evaluation-result'>
                      <h3 className='evaluation-result__title'>参考回复</h3>
                      <p>{scenario?.expected}</p>
                    </div>

                    {/* 加载状态和评估结果移动到这里 */}
                    {isEvaluating && (
                      <div className='loading-indicator'>
                        <div className='spinner'></div>
                        <p>正在评估发音，请稍候...</p>
                      </div>
                    )}

                    {evaluationResult && (
                      <div className='evaluation-result'>
                        <h3 className='evaluation-result__title'>发音评估结果</h3>

                        <div className='scores'>
                          <div className='score-item'>
                            <span className='score score-overall'>{evaluationResult.overall_score}</span>
                            <span>综合评分</span>
                          </div>
                          <div className='score-item'>
                            <span className='score'>{evaluationResult.level}</span>
                            <span>评级</span>
                          </div>
                        </div>

                        <div className='scores'>
                          <div className='score-item'>
                            <span className='score'>{evaluationResult.word_accuracy}%</span>
                            <span>单词准确率</span>
                          </div>
                          <div className='score-item'>
                            <span className='score'>{evaluationResult.phoneme_similarity}%</span>
                            <span>音素相似度</span>
                          </div>
                          <div className='score-item'>
                            <span className='score'>{evaluationResult.fluency_score}%</span>
                            <span>流畅度</span>
                          </div>
                        </div>

                        <div className='feedback-details'>
                          <h5>对比结果:</h5>
                          <div className='overall-feedback'>
                            <p>参考文本: {evaluationResult.reference_text}</p>
                            <p>识别结果: {evaluationResult.transcription}</p>
                          </div>
                        </div>

                        {evaluationResult.feedback && (
                          <div className='feedback-details'>
                            <h5>详细反馈:</h5>
                            <div className='overall-feedback'>
                              <p>{evaluationResult.feedback}</p>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </Space>
          </div>

          {/* 关键词部分 */}
          <div className='keywords-section'>
            <h4 className='section-title'>关键词</h4>
            <div className='keywords-list'>
              {scenario?.keywords.map((keyword, index) => (
                <span key={index} className='keyword-tag'>
                  {keyword}
                </span>
              ))}
            </div>
          </div>

          {/* 参考资料 */}
          <div className='materials-section'>
            <h4 className='section-title'>参考资料</h4>
            <p className='materials-content'>{scenario?.reference}</p>
          </div>

          {/* 导航按钮 */}
          <div className='navigation-footer'>
            <Space style={{ width: '100%', justifyContent: 'space-between' }}>
              <Button type='link' icon={<span>←</span>} onClick={onPrevious} disabled={currentScenarioIndex === 0}>
                上一个场景
              </Button>
              <Button type='link' onClick={onNext} disabled={currentScenarioIndex === scenariosLength - 1}>
                下一个场景 →
              </Button>
            </Space>
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default function GujingCommandDialogue() {
  // State
  const [currentScenarioIndex, setCurrentScenarioIndex] = useState(0)
  const [bookmarkedScenarios, setBookmarkedScenarios] = useState<typeof scenariosData>([])
  const [filteredScenarios, setFilteredScenarios] = useState<typeof scenariosData>(scenariosData)
  const [viewMode, setViewMode] = useState<'all' | 'random' | 'bookmarked'>('all')
  const [currentDifficulty, setCurrentDifficulty] = useState<'all' | 'beginner' | 'intermediate' | 'advanced'>('all')
  const [modalVisible, setModalVisible] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [recordingSeconds, setRecordingSeconds] = useState(0)
  const [audioUrl, setAudioUrl] = useState('')
  const [evaluationResult, setEvaluationResult] = useState<any>(null)
  const [isEvaluating, setIsEvaluating] = useState(false)
  const [globalIsBookmarked, setGlobalIsBookmarked] = useState(false)

  // Refs
  // 使用自定义的录音钩子
  const { isRecording, recordingStatus, audioBlob, startRecording, stopRecording } = useAudioRecorder()

  // 添加录音计时器
  useEffect(() => {
    let timer: NodeJS.Timer
    if (isRecording) {
      setRecordingSeconds(0) // 重置计时器
      timer = setInterval(() => {
        setRecordingSeconds(prev => prev + 1)
      }, 1000)
    }
    return () => {
      if (timer) {
        clearInterval(timer)
        setRecordingSeconds(0) // 清除计时
      }
    }
  }, [isRecording])

  const toggleRecording = () => {
    if (isRecording) {
      stopRecording()
    } else {
      setRecordingSeconds(0) // 重置计时
      setAudioUrl('') // 清除之前的录音
      startRecording()
    }
  }

  // 监听录音结束并处理音频数据
  useEffect(() => {
    if (!isRecording && audioBlob) {
      const url = URL.createObjectURL(audioBlob)
      setAudioUrl(url)
      evaluatePronunciation() // 自动开始评估
      return () => {
        URL.revokeObjectURL(url)
      }
    }
  }, [isRecording, audioBlob])

  // 添加评估方法
  const evaluatePronunciation = async () => {
    if (!audioBlob) {
      console.error('没有可用的音频数据')
      return
    }

    try {
      setIsEvaluating(true)
      const formData = new FormData()
      formData.append('file', audioBlob, 'recording.wav')
      formData.append('reference_text', filteredScenarios[currentScenarioIndex].expected)
      formData.append('language', 'en')
      formData.append('detailed', 'true')

      const response = await getEvaluatePronunciation(formData)

      if (response.success) {
        console.log('发音评估成功:', response.data)
        setEvaluationResult(response.data)
      } else {
        throw new Error(response.message || '评估失败')
      }
    } catch (error) {
      console.error('发音评估过程中发生错误:', error)
      alert(error instanceof Error ? error.message : '发音评估失败')
    } finally {
      setIsEvaluating(false)
    }
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // 修改检查收藏状态的函数
  const isBookmarked = useCallback(
    (id: number) => {
      const exists = bookmarkedScenarios.some(s => s.id === id)
      setGlobalIsBookmarked(exists)
      return exists
    },
    [bookmarkedScenarios]
  )

  // 修改切换收藏的函数
  const toggleBookmark = () => {
    const currentScenario = filteredScenarios[currentScenarioIndex]
    if (!currentScenario) return

    setBookmarkedScenarios(prev => {
      const exists = prev.some(s => s.id === currentScenario.id)
      setGlobalIsBookmarked(!exists)
      if (exists) {
        return prev.filter(s => s.id !== currentScenario.id)
      }
      return [...prev, currentScenario]
    })
  }

  // 在场景改变时更新收藏状态
  useEffect(() => {
    if (filteredScenarios[currentScenarioIndex]) {
      const exists = bookmarkedScenarios.some(s => s.id === filteredScenarios[currentScenarioIndex].id)
      setGlobalIsBookmarked(exists)
    }
  }, [currentScenarioIndex, filteredScenarios, bookmarkedScenarios])

  // 修改难度筛选处理函数
  const handleDifficultyChange = (value: string) => {
    setCurrentDifficulty(value as typeof currentDifficulty)
    let newFilteredScenarios = [...scenariosData]

    if (value !== 'all') {
      newFilteredScenarios = scenariosData.filter(s => s.difficulty === value)
    }

    setFilteredScenarios(newFilteredScenarios)
    setCurrentScenarioIndex(0) // 重置当前场景索引
  }

  const playCommand = async () => {
    const command = filteredScenarios[currentScenarioIndex].command
    console.log('Playing command:', command)

    const keyword = {
      text: command,
      accent: 'us',
      speed: 1,
      voice_type: 'female'
    }

    try {
      setIsPlaying(true)
      const res = await wordPronunciation(keyword)

      if (res.ok) {
        const audioBlob = await res.blob()
        const audioUrl = URL.createObjectURL(audioBlob)

        const audio = new Audio(audioUrl)
        await audio.play()

        audio.onended = () => {
          URL.revokeObjectURL(audioUrl)
          setIsPlaying(false)
        }
      } else {
        throw new Error('音频生成失败')
      }
    } catch (error) {
      console.error('播放指令时出错:', error)
      alert(error instanceof Error ? error.message : '播放指令失败')
      setIsPlaying(false)
    }
  }

  // 清除所有录音相关状态的函数
  const clearRecordingStates = () => {
    setAudioUrl('')
    setRecordingSeconds(0)
    setEvaluationResult(null)
    setIsEvaluating(false)
  }

  // 修改关闭弹窗处理函数
  const handleCloseModal = () => {
    setModalVisible(false)
    clearRecordingStates()
  }

  // 修改切换场景函数
  const handlePrevious = () => {
    if (currentScenarioIndex > 0) {
      setCurrentScenarioIndex(prev => prev - 1)
      clearRecordingStates()
    }
  }

  const handleNext = () => {
    if (currentScenarioIndex < filteredScenarios.length - 1) {
      setCurrentScenarioIndex(prev => prev + 1)
      clearRecordingStates()
    }
  }

  // 修改场景展示函数
  const showScenarioDetail = (index: number) => {
    setCurrentScenarioIndex(index)
    setModalVisible(true)
    clearRecordingStates()
  }

  const renderScenarioCard = (scenario: (typeof scenariosData)[0], index: number) => {
    return (
      <Card
        key={scenario.id}
        hoverable
        className={currentScenarioIndex === index ? 'ant-card-selected' : ''}
        onClick={() => showScenarioDetail(index)}
        extra={
          <Tag
            color={
              scenario.difficulty === 'beginner'
                ? 'success'
                : scenario.difficulty === 'intermediate'
                ? 'warning'
                : 'error'
            }
          >
            {scenario.difficulty === 'beginner' ? '初级' : scenario.difficulty === 'intermediate' ? '中级' : '高级'}
          </Tag>
        }
      >
        <Meta
          title={scenario.scenario}
          description={
            <>
              <div style={{ color: '#64748b', marginBottom: '12px' }}>{scenario.roles.join(' → ')}</div>
              <Card className='command-card' bordered={false} style={{ backgroundColor: '#f8fafc' }}>
                <p style={{ marginBottom: '8px', fontSize: '14px' }}>{scenario.command}</p>
                <p style={{ color: '#64748b', fontSize: '12px', margin: 0 }}>{scenario.command_zh}</p>
              </Card>
            </>
          }
        />
      </Card>
    )
  }

  return (
    <div className='gujing-command-dialogue'>
      <Layout.Header className='header'>
        <div className='header-container'>
          <div className='header-content'>
            <div className='title-wrapper'>
              <h1 className='title'>钻井指令英语口语训练</h1>
              <div className='subtitle'>提高钻井工作场景专业英语沟通能力</div>
            </div>
            <div className='header-right'>
              <div className='scene-count'>
                {currentScenarioIndex + 1} / {filteredScenarios.length} 场景
              </div>
              <div className='desktop-links'>
                <a href='#/gujing-english-training'>固井基础术语</a>
                <a href='#/gujing-command-dialogue'>设备操作指令对话</a>
              </div>
              <div className='user-avatar'>张</div>
            </div>
          </div>
        </div>
      </Layout.Header>

      <main className='main'>
        <div className='mode-selector'>
          <Radio.Group value={viewMode} onChange={e => setViewMode(e.target.value)} buttonStyle='solid'>
            <Radio.Button value='all'>所有场景</Radio.Button>
            <Radio.Button value='random'>随机场景</Radio.Button>
            <Radio.Button value='bookmarked'>收藏场景 ({bookmarkedScenarios.length})</Radio.Button>
          </Radio.Group>
          <Select
            value={currentDifficulty}
            style={{ width: 120 }}
            onChange={handleDifficultyChange}
            options={[
              { value: 'all', label: '所有难度' },
              { value: 'beginner', label: '初级' },
              { value: 'intermediate', label: '中级' },
              { value: 'advanced', label: '高级' }
            ]}
          />
        </div>

        <div className='scenarios-section'>
          {/* <h2 className="text-xl font-bold text-gray-800 mb-4">
            {viewMode === 'all' ? '所有场景' : 
             viewMode === 'random' ? '随机场景' : '收藏场景'}
          </h2> */}
          <div className='scenarios-grid'>
            {(viewMode === 'bookmarked' ? bookmarkedScenarios : filteredScenarios).map((scenario, index) =>
              renderScenarioCard(scenario, index)
            )}
          </div>
        </div>

        <ScenarioDetailModal
          visible={modalVisible}
          onClose={handleCloseModal} /* 修改为新的关闭处理函数 */
          scenario={filteredScenarios[currentScenarioIndex]}
          isBookmarked={globalIsBookmarked}
          onToggleBookmark={toggleBookmark}
          isRecording={isRecording}
          recordingSeconds={recordingSeconds}
          audioUrl={audioUrl}
          toggleRecording={toggleRecording}
          playCommand={playCommand}
          formatTime={formatTime}
          currentScenarioIndex={currentScenarioIndex}
          scenariosLength={filteredScenarios.length}
          onPrevious={handlePrevious}
          onNext={handleNext}
          isEvaluating={isEvaluating}
          evaluationResult={evaluationResult}
        />
      </main>
    </div>
  )
}
