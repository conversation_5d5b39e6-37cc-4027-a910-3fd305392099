.gujing-command-dialogue {
  min-height: 100vh;
  background-color: #f5f5f5;

  .header {
    background-color: #3949ab;
    padding: 0;
    height: auto;
    line-height: 1.5;
    box-shadow: none;

    .header-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 10px 20px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center; // 修改为垂直居中

      .title-wrapper {
        padding: 4px 0; // 添加少许内边距保持视觉平衡
        .title {
          font-size: 20px;
          font-weight: 600;
          color: #fff;
        }
        .subtitle {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
          margin-top: 4px; // 添加少许上边距
        }
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      height: 100%; // 确保容器高度充满
      gap: 16px;

      .desktop-links {
        display: none;
        gap: 1rem;

        @media (min-width: 768px) {
          display: flex;
        }

        a {
          color: white;
          text-decoration: none;
          &:hover {
            color: #bfdbfe;
          }
        }
      }

      .scene-count {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center; // 确保文字垂直居中
      }

      .user-avatar {
        height: 1.5rem;
        width: 1.5rem;
        background-color: white;
        color: #1e40af;
        border-radius: 9999px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
      }
    }
  }

  .main {
    max-width: 1200px;
    margin: 24px auto;
    padding: 0 24px;

    .mode-selector {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .button-group {
        display: flex;
        gap: 8px;

        .mode-btn {
          padding: 8px 16px;
          border-radius: 4px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s;
          border: 1px solid #3949ab;

          &.active {
            background: #3949ab;
            color: #fff;
          }

          &.inactive {
            background: #fff;
            color: #3949ab;

            &:hover {
              background: #e6f7ff;
            }
          }
        }
      }

      .difficulty-select {
        padding: 6px 12px;
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        outline: none;

        &:focus {
          border-color: #3949ab;
        }
      }
    }

    .scenarios-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 16px;
      margin-bottom: 24px;
    }

    .scenario-card {
      background: #fff;
      border-radius: 8px;
      border: 1px solid #e8e8e8;
      padding: 16px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;

        .difficulty-tag {
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 12px;

          &.beginner {
            background: #e6f7ff;
            color: #3949ab;
          }

          &.intermediate {
            background: #fff7e6;
            color: #fa8c16;
          }

          &.advanced {
            background: #fff1f0;
            color: #f5222d;
          }
        }
      }
    }

    .recording-section {
      text-align: center;
      margin: 24px 0;

      .record-btn {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: #f5222d;
        border: none;
        color: #fff;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          transform: scale(1.1);
        }

        &.recording {
          animation: pulse 1.5s infinite;
        }
      }
    }

    .scenario-detail {
      background: white;
      border-radius: 0.5rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .scenario-header {
        background-color: #3949ab;
        border-radius: 10px 10px 0 0;
        border: none;

        .ant-card-body {
          padding: 10px 24px !important;
          color: #fff;
        }

        .header-content {
          .header-title {
            display: flex;
            align-items: center;
            justify-content: space-between;

            h2 {
              color: #fff;
              margin: 0;
              font-size: 18px;
              font-weight: 500;
            }

            .bookmark-btn {
              background: none;
              border: none;
              cursor: pointer;
              padding: 4px;
              display: flex;
              align-items: center;
              color: rgba(255, 255, 255, 0.65);
              transition: color 0.3s;

              &.active {
                color: #fff;
              }

              .bookmark-icon {
                width: 20px;
                height: 20px;
              }
            }
          }
        }
      }

      .scenario-content {
        padding: 1.25rem 1.5rem;

        .info-section {
          margin-bottom: 24px;
          max-width: 800px;

          .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }

            .icon-wrapper {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 12px;

              &.role-icon {
                background-color: #e6f4ff;
                color: #3949ab;
              }

              &.scene-icon {
                background-color: #f0fdf4;
                color: #15803d;
              }

              svg {
                width: 16px;
                height: 16px;
              }
            }

            .info-content {
              .label {
                font-size: 12px;
                color: #64748b;
              }

              .value {
                font-weight: 500;
                color: #1f2937;
              }
            }
          }
        }

        .role-section {
          margin-bottom: 24px;
          width: 100%;

          .ant-space {
            width: 100%;

            .ant-space-item:nth-child(2) {
              flex: 1;
              min-width: 0;
              width: calc(100% - 44px) !important;
            }
          }

          .record-box {
            width: 100%;
            background-color: #f9fafb;
            border-radius: 8px;

            .record-controls {
              width: 100%;
              display: flex;
              flex-direction: column;
              align-items: center;

              .audio-wrapper {
                width: 100%;
                margin-top: 16px;

                .audio-player {
                  width: 100% !important;
                  height: 36px;
                }
              }
            }
          }

          &.ant-card {
            border-radius: 8px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          }

          .ant-typography {
            line-height: 1.6;
          }
        }

        .record-box {
          background-color: #f9fafb;
          border-radius: 8px;
          padding: 16px;

          .record-controls {
            display: flex;
            flex-direction: column;
            align-items: center;

            .record-btn {
              width: 48px;
              height: 48px;
              background-color: #ef4444;
              color: white;
              display: flex;
              align-items: center;
              justify-content: center;
              transition: all 0.2s;

              &:hover {
                background-color: #dc2626;
              }

              &.recording {
                background-color: #fee2e2;
                color: #ef4444;
                animation: pulse 2s infinite;
              }
            }

            .audio-wrapper {
              width: 100%;

              .audio-player {
                width: 100%;
                height: 36px;
              }
            }
          }
        }

        .reference-section,
        .keywords-section,
        .materials-section {
          margin-bottom: 1.5rem;

          .section-title {
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.75rem;
          }
        }

        .reference-content {
          background-color: #f9fafb;
          border-radius: 0.375rem;
          padding: 0.75rem;
          color: #4b5563;
        }

        .keywords-list {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;

          .keyword-tag {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            background-color: #fef3c7;
            color: #92400e;
            font-size: 0.75rem;
            border-radius: 0.25rem;
          }
        }

        .materials-content {
          font-size: 0.875rem;
          color: #6b7280;
        }

        .navigation-footer {
          display: flex;
          justify-content: space-between;
          padding-top: 1rem;
          border-top: 1px solid #f3f4f6;

          .nav-btn {
            font-size: 0.875rem;
            color: #6b7280;

            &:hover:not(:disabled) {
              color: #374151;
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }
        }
      }
    }
  }
}

// Modal styles
.ant-modal {
  .scenario-detail {
    background: white;

    .scenario-header {
      background-color: #3949ab;
      border-radius: 8px 8px 0 0;
      border: none;

      .ant-card-body {
        padding: 10px 24px !important;
        color: #fff;
      }

      .header-content {
        .header-title {
          display: flex;
          align-items: center;

          h2 {
            color: #fff;
            margin: 0;
            font-size: 18px;
            font-weight: 500;
          }

          .bookmark-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 4px;
            color: rgba(255, 255, 255, 0.65);
            transition: color 0.3s;

            &.active {
              color: #fff;
            }

            .bookmark-icon {
              width: 20px;
              height: 20px;
            }
          }
        }
      }
    }

    .scenario-content {
      padding: 24px;
      max-height: calc(100vh - 200px);
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #d1d5db;
        border-radius: 3px;
      }

      .role-section {
        margin-bottom: 1.5rem;
        .ant-space-item:last-child {
          flex: 1;
        }
        .role-header {
          display: flex;
          align-items: center;
          margin-bottom: 0.75rem;

          .role-avatar {
            width: 1.5rem;
            height: 1.5rem;
            border-radius: 9999px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;

            &.role-a {
              background-color: #eff6ff;
              color: #2563eb;
            }

            &.role-b {
              background-color: #f0fdf4;
              color: #16a34a;
            }
          }
        }

        .command-box,
        .record-box {
          background-color: #f9fafb;
          border-radius: 0.375rem;
          padding: 1rem;
        }

        .record-controls {
          .record-btn {
            &.recording {
              animation: pulse 2s infinite;
            }
          }
        }
      }

      .reference-section,
      .keywords-section,
      .materials-section {
        margin-bottom: 1.5rem;
      }

      .keywords-list {
        .keyword-tag {
          display: inline-block;
          padding: 0.25rem 0.5rem;
          background-color: #fef3c7;
          color: #92400e;
          font-size: 0.75rem;
          border-radius: 0.25rem;
          margin-right: 0.5rem;
          margin-bottom: 0.5rem;
        }
      }

      .navigation-footer {
        margin-top: 24px;
        padding-top: 16px;
        border-top: 1px solid #e5e7eb;
      }
    }
  }

  // 继承原有组件样式
  .role-section,
  .reference-section,
  .keywords-section,
  .materials-section {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .command-box,
  .record-box {
    background: #f8fafc;
    padding: 16px;
    border-radius: 8px;
  }

  .navigation-footer {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;
  }
}

// 调整弹窗大小和位置
.ant-modal-wrap {
  .ant-modal {
    top: 50px;

    .ant-modal-content {
      max-height: calc(100vh - 100px);
      overflow: hidden;
      padding: 0;
    }
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .gujing-command-dialogue {
    .main {
      padding: 16px;

      .scenarios-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}

.record-controls {
  .record-btn {
    &:not(.ant-btn-loading) {
      .anticon {
        margin-right: 0;
      }
    }

    svg {
      width: 24px !important;
      height: 24px !important;
      display: block;
    }
  }
}

// 添加评估结果相关样式
.evaluation-result {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  &__title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
  }

  .scores {
    display: flex;
    justify-content: space-around;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 16px;
  }

  .score-item {
    text-align: center;
    font-size: 0.9rem;
    color: #999;
  }

  .score {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #1890ff;
    margin-bottom: 4px;
  }

  .score-overall {
    color: #ff4d4f;
    font-size: 2rem;
  }

  .feedback-details {
    border-radius: 4px;
    background-color: #f8f9fa;
    margin-top: 1rem;
    padding: 1rem;

    h5 {
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
    }
  }

  .overall-feedback {
    font-size: 14px;
    color: #666;
    line-height: 1.6;

    p {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.loading-indicator {
  text-align: center;
  margin: 1rem 0;
  padding: 1rem;

  .spinner {
    width: 24px;
    height: 24px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3949ab;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
    margin-bottom: 8px;
  }

  p {
    color: #666;
    font-size: 14px;
  }
}

// 修改场景详情中的导航按钮样式
.scenario-detail {
  position: relative;
  // padding-bottom: 60px; // 为固定按钮留出空间

  .navigation-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    padding: 12px 24px;
    border-top: 1px solid #f0f0f0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;

    .ant-btn {
      padding: 6px 12px;

      &[disabled] {
        color: rgba(0, 0, 0, 0.25);
      }
    }
  }
}
