.profile-page {
  background: #f3f5f7;
  min-height: 100vh;
  padding: 10px;
}
.profile-page .profile-card .ant-card-head {
  color: #fff;
  background-color: #2986d9;
  font-size: 16px;
  min-height: 44px;
}
.profile-title {
  background: #2986d9;
  color: #fff;
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  border-radius: 12px 12px 0 0;
  padding: 10px 0;
  margin-bottom: 24px;
  letter-spacing: 2px;
}
.profile-grid {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.profile-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  min-width: 320px;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 220px;
  width: 100%;
}
.profile-card-title {
  background: #2986d9;
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  border-radius: 8px 8px 0 0;
  padding: 8px 0 8px 12px;
  margin: -20px -24px 16px -24px;
}
.profile-list {
  margin: 0;
  padding-left: 18px;
}
