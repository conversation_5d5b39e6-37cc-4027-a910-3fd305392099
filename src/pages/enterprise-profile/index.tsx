import React, { useState } from 'react'
import {
  Card,
  Row,
  Col,
  Typography,
  Input,
  Button,
  Space,
  Spin,
  message,
} from 'antd'
import { enterpriseProfile } from '@/api/enterpriseProfile'
import StreamTypewriter from '@/component/StreamTypewriter'
import './index.less'

const { Title } = Typography

// 递归渲染任意 JSON 数据
const renderValue = (value: any) => {
  if (Array.isArray(value)) {
    return (
      <ul className="profile-list">
        {value.map((item, idx) => (
          <li key={idx}>{renderValue(item)}</li>
        ))}
      </ul>
    )
  } else if (typeof value === 'object' && value !== null) {
    return (
      <ul className="profile-list">
        {Object.entries(value).map(([k, v]) => (
          <li key={k} style={{ marginBottom: 4 }}>
            <strong>{k}：</strong>
            {typeof v === 'object' ? renderValue(v) : String(v)}
          </li>
        ))}
      </ul>
    )
  } else {
    return <span>{String(value)}</span>
  }
}

const sampleData = {
  基础信息: {
    注册与法律信息: {
      企业名称: '山东金晶科技股份有限公司',
      成立时间: '2000-01-01',
      注册资本: '142877万人民币',
      统一社会信用代码: '913700002651272421',
      工商登记状态: '开业',
      法定代表人: '王刚',
      注册地址: '淄博市高新技术开发区宝石镇王庄',
      行业分类: '非金属矿物制品业',
      企业类型: '股份有限公司',
      登记机关: '淄博市市场监督管理局',
    },
    组织架构: {
      股东构成: {
        无限售流通股: {
          认缴金额: '142877万人民币',
          认缴时间: '2015-11-12',
          实缴金额: '142877万人民币',
          实缴时间: '2015-11-12',
        },
      },
      分支机构及子公司: [
        '内蒙古金晶科技有限公司',
        '山东海天生物化工有限公司',
        '北京金晶智慧有限公司',
        '临沂亚虹经贸有限公司',
        '宁夏金晶科技有限公司',
        '山东金晶圣戈班玻璃有限公司',
        '廊坊金彪玻璃有限公司',
        '山东金晶匹兹堡汽车玻璃有限公司',
      ],
    },
    地理位置: {
      总部地址: '淄博市高新技术开发区宝石镇王庄',
      分支机构分布: ['内蒙古', '北京', '临沂', '宁夏', '廊坊'],
    },
  },
  经营状况: {
    业务类型: {
      主营业务范围:
        '玻璃制造；技术玻璃制品制造；制镜及类似品加工；专用设备制造；建筑材料销售；化工产品销售；货物进出口；技术进出口',
      '产品/服务类别': '玻璃及相关制品',
      行业分类: '制造业 - 非金属矿物制品业 - 石墨及其他非金属矿物制品制造',
    },
    经营规模: {
      员工人数: '1000-4999人',
      社保缴纳人数: 1491,
      市场份额: '未公示',
      客户类型: 'B端',
    },
    合作伙伴: {
      战略合作方: [
        '山东金晶圣戈班玻璃有限公司',
        '山东金晶匹兹堡汽车玻璃有限公司',
      ],
    },
  },
  财务与融资: {
    财务数据: {
      总资产: '未公示',
      总负债: '未公示',
      总利润: '未公示',
      总税收: '未公示',
      研发投入: '未公示',
    },
    融资信息: {
      融资轮次: 'IPO上市',
      股票代码: '600586',
      股票简称: '金晶科技',
      债券类型: 'A股',
    },
  },
  风险与合规: {
    风险事件: {
      经营异常记录: '无',
      法律纠纷: '存在司法案件记录',
      严重违法失信: '无',
    },
    合规性: {
      社保缴纳: '正常',
      环保合规: '未公示',
      税务合规: '未公示',
    },
  },
  市场与客户: {
    市场定位: {
      目标客户群体: '建筑、汽车、化工等行业',
      品牌影响力: '高新技术企业，A股上市公司',
      市场策略: '技术驱动，多元化产品线',
    },
    竞争分析: {
      差异化优势: '技术玻璃制品制造能力，进出口业务',
    },
  },
  运营与技术能力: {
    技术资源: {
      专利数量: {
        发明专利: 7,
        实用新型专利: 38,
        外观设计专利: 0,
        软件著作权: 0,
      },
      技术团队规模: '未公示',
      生产设备水平: '未公示',
    },
    供应链管理: {
      供应商稳定性: '未公示',
      库存周转率: '未公示',
      物流效率: '未公示',
    },
  },
  动态数据支撑: {
    政策匹配: {
      适配政策: '高新技术企业',
      政策级别: '国家级',
    },
    行为数据: {
      线上平台使用: '未公示',
      客户反馈: '未公示',
    },
  },
  企业评分: {
    科技资质得分: 15,
    成长得分: 18,
    科技与创新得分: 79,
    研发能力得分: 18,
    行业潜力得分: 10,
    创新能力得分: 12,
    科技等级: '优秀',
    得分排名: '86%',
  },
}

const EnterpriseProfile: React.FC<{ data?: any }> = ({ data: propData }) => {
  const [company, setCompany] = useState('')
  const [profileData, setProfileData] = useState<any>(propData || {})
  const [profileJsonData, setProfileJsonData] = useState<any>(propData || '')
  const [loading, setLoading] = useState(false)

  const handleSearch = async () => {
    if (!company) {
      message.warning('请输入公司名称')
      return
    }
    setLoading(true)
    let accumulated = ''
    setProfileJsonData('')
    try {
      await enterpriseProfile(
        { query: company },
        {
          onMessage: (text, finished) => {
            if (text) {
              accumulated += text
              console.log(accumulated, 'accumulated')
              setProfileJsonData(accumulated)
            }
            if (finished) {
              setLoading(false)
              try {
                // 先去掉 markdown 代码块包裹
                let cleaned = accumulated
                  .replace(/^```json\s*/, '')
                  .replace(/^```json\n/, '') // 兼容换行
                  .replace(/^```json/, '') // 兼容无换行
                  .replace(/```\s*$/, '')
                  .replace(/```$/, '')
                  .trim()
                console.log(cleaned, 'cleaned')
                // 解析 JSON
                const json = JSON.parse(cleaned)
                // console.log(accumulated, 'accumulated')
                setProfileData(json)
              } catch (e) {
                setProfileData({ 结果: accumulated })
              }
            }
          },
          onError: () => {
            setLoading(false)
            message.error('查询失败，请重试')
          },
          onFinish: () => {
            setLoading(false)
          },
        }
      )
    } catch (err) {
      setLoading(false)
      message.error('查询失败，请重试')
    }
  }

  // 新增：分离基础信息和其他部分
  const baseInfo = profileData['基础信息'] || null
  const otherKeys = Object.keys(profileData).filter((k) => k !== '基础信息')
  const baseInfoKeys = baseInfo ? Object.keys(baseInfo) : []

  return (
    <div className="profile-page" style={{ position: 'relative' }}>
      {loading && <div className="custom-mask" />}
      <Spin tip="加载中" spinning={loading} size="large">
        <div style={{ height: '100%', overflow: 'auto' }}>
          <div className="profile-title">企业画像</div>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              marginBottom: 24,
            }}
          >
            <Space>
              <Input
                placeholder="请输入公司名称"
                value={company}
                onChange={(e) => setCompany(e.target.value)}
                style={{ width: 280 }}
                onPressEnter={handleSearch}
              />
              <Button type="primary" onClick={handleSearch}>
                搜索
              </Button>
            </Space>
          </div>
          {loading ? (
            <StreamTypewriter
              text={profileJsonData}
              // components={markdownComponents}
              end={!loading} // 添加这一行，当 loading 为 false 时表示输出结束
            />
          ) : (
            <Row gutter={[24, 24]} className="profile-grid">
              {/* 基础信息下的每个一级 key 单独卡片显示 */}
              {baseInfo &&
                baseInfoKeys.map((section) => (
                  <Col
                    xs={24}
                    sm={24}
                    md={12}
                    lg={8}
                    xl={8}
                    key={section}
                    style={{ display: 'flex' }}
                  >
                    <Card
                      className="profile-card"
                      bordered={false}
                      title={`基础信息 - ${section}`}
                      style={{ width: '100%' }}
                    >
                      {renderValue(baseInfo[section])}
                    </Card>
                  </Col>
                ))}
              {/* 其他一级 key 按原来方式显示 */}
              {otherKeys.map((section) => (
                <Col
                  xs={24}
                  sm={24}
                  md={12}
                  lg={8}
                  xl={8}
                  key={section}
                  style={{ display: 'flex' }}
                >
                  <Card
                    className="profile-card"
                    bordered={false}
                    title={section}
                    style={{ width: '100%' }}
                  >
                    {renderValue(profileData[section])}
                  </Card>
                </Col>
              ))}
            </Row>
          )}
        </div>
      </Spin>
    </div>
  )
}

export default EnterpriseProfile
