.profile-page {
  background: #f3f5f7;
  min-height: 100vh;
  overflow: auto;
  padding: 10px;
  .profile-card {
    .ant-card-head {
      color: #fff;
      background-color: #2986d9;
      font-size: 16px;
      min-height: 44px;
    }

  }
  .custom-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100%;
    background: rgba(0, 0, 0, 0.45);
    z-index: 2000;
  }
  // 去掉默认白色蒙层
  .ant-spin-blur {
    filter: none !important;
    opacity: 1 !important;
  } 
  .ant-spin-blur::after {
    opacity: 0!important;
  }
  .ant-spin {
    min-height: 545px!important;
    z-index: 9999!important;
  }
  .ant-spin-dot-spin {
    color: #fff!important;
  }
  .ant-spin-text {
    color: #fff!important;
    text-shadow:none!important;
  }
}
.profile-title {
  background: #2986d9;
  color: #fff;
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  border-radius: 12px 12px 0 0;
  padding: 10px 0;
  margin-bottom: 24px;
  letter-spacing: 2px;
}
.profile-grid {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  // gap: 24px;
}
.profile-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06);
  min-width: 320px;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 220px;
  width: 100%;
}
.profile-card-title {
  background: #2986d9;
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  border-radius: 8px 8px 0 0;
  padding: 8px 0 8px 12px;
  margin: -20px -24px 16px -24px;
}
.profile-list {
  margin: 0;
  padding-left: 18px;
} 