import { useCallback, useState, useEffect, useMemo } from "react";
import { useLocation } from "react-router-dom";
import {
  Button,
  Typography,
  Card,
  Spin,
  Flex,
  Input,
  message,
  Steps,
  Space,
} from "antd";
import "./index.less";
import { DeleteOutlined, RightOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom'


export const PolicyMatching = () => {

  const navigate = useNavigate()

  const { search } = useLocation();
  const searchParams = useMemo(() => new URLSearchParams(search), [search])
  const key = searchParams.get('key')

  const [inputValue, setInputValue] = useState('')

  const [messageApi, contextHolder] = message.useMessage();

  const [generating, setGenerating] = useState<boolean>(false);

  const [current, setCurrent] = useState(0);

  const [listdata, setListdata] = useState<any>([1,2,3,5,6,7,4,
    
    54,45,23,124])
  // const [listdata, setListdata] = useState<any>([])

  // 初始化时
  useEffect(() => {

    setGenerating(false)
  }, [])

  // 进度条变化
  const next = () => {
    setCurrent(current + 1);
  };

  // input输入框信息变化，调接口时时查看之前的信息
  const handleInputChange = (event: any) => {
    setInputValue(event.target.value.trim())
    // 查询匹配的企业信息名称,还没有做。。。。


  }

  // 匹配项目，查询企业信息，跳转页面
  const handleSubmit = (companyName:string) => {
    if (!inputValue) {
      messageApi.error('请输入企业名称')
      return
    }
    console.log(companyName)
    next()
    navigate(`/policy-match-details?id=${companyName}`)
  }

  // 删除历史
  const delHistory = () => {
    setListdata([])
    // 调接口，删除数据库存储的搜索历史， 还没有做。。。。
  }

  return (
    <>
    {contextHolder}
      <Spin tip="加载中" spinning={generating} fullscreen size="large" />
      <Flex className="toolbar" justify="center">
        <Typography.Text className="title-text">政策匹配</Typography.Text>
      </Flex>
      <div className="main-container">
        <div>
          <Steps
            current={current}
            labelPlacement='vertical'
            items={[
              {
                // description: "第一步请输入企业名称",
                description: (<div className="step-description">
                  <div>第一步</div>
                  <div>请输入企业名称</div>
                </div>)
              },
              {
                // title: '2',
                // description: "第二步完成政策匹配",
                description: (<div>
                  <div>第二步</div>
                  <div>完成政策匹配</div>
                </div>)
              },
            ]}
          />
        </div>
        <div className='down-input'>
          <Space.Compact style={{ width: "100%" }}>
            <Input onChange={handleInputChange} value={inputValue} placeholder="请输入企业名称，匹配可申领补贴" />
            <Button type="primary" onClick={() => {handleSubmit(inputValue)}}>匹配</Button>
          </Space.Compact>
          <div className={`search-history ${(inputValue && listdata.length > 0) &&  'show-search-history'} `}>
            <Flex justify="space-between" className="card-header">
                <div>匹配历史</div>
                <div><DeleteOutlined onClick={delHistory} /></div>
            </Flex>
            <div className="card-contain"> 
            {listdata.map((item) => {
              return(
                <Flex justify="space-between" key={item} >
                  <div>公司名称</div>
                  <div style={{color: '#1677ff', marginBottom:'16px', cursor:"pointer"}} onClick={() => {handleSubmit(item)}}>立即匹配 <RightOutlined /></div>
                </Flex>
                
              )
            })}
            </div>


          </div>
        </div>
      </div>
    </>
  );
};

export default PolicyMatching;
