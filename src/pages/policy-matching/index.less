.main-container { 
  height: calc(100vh - 87px);
  flex-direction: column;
  padding: 10% 15%;



  .step-description {
    display: flex;
    flex-direction: column;
    justify-content: start;
  }

  .down-input {
    margin-top: 20px;
    position: relative;
    .search-history {
      display: none;
    }
    .show-search-history{
      display: flex;
      flex-direction: column;
      position: absolute;
      top: 40px;
      left: 0;
      width: calc(100% - 64px);
      background-color: #fff;
      height: 300px;
      box-shadow: 0 0 10px #ccc;
      padding: 16px 20px;

      .card-header {
        color: #ccc;
      }
      .card-contain {
        margin-top: 20px;
        overflow: auto;
      }
    }
  }
}