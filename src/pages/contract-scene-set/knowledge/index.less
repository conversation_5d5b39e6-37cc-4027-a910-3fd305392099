.modal-know-wcl {
  max-height: 50vh;
  min-height: 30vh;
  overflow-y: auto;
  width: 100%;
  gap: 12px;
  .cardBox-wcl {
    padding: 8px;
    width: 100%;
    box-sizing: border-box;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.04);
    .top {
      width: 100%;
      gap: 4px;
      .icon-card {
        font-size: 12px;
      }
    }
    .left-gas {
      cursor: pointer;
      width: 100%;
    }
    .first-title {
      color: #000;
      font-size: 14px;
      font-weight: bold;
    }
    .two-title {
      color: rgba(0, 0, 0, 0.25);
      font-size: 14px;
    }
    .first-title,
    .two-title {
      display: inline-block;
      white-space: nowrap;
      width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .acitve {
    border: 1px solid #91d1ff;
    background: #e6f6ff;
  }
}

.chat-search-input {
  width: 100%;
  height: 100%;
  position: relative;
  margin-bottom: 10px;
}
