import { But<PERSON>, Col, Row, Tabs } from 'antd'
import { LeftOutlined } from '@ant-design/icons'
import { FileItem } from './Step1'
import { Step2Right } from './Step2Right'
import { useState } from 'react'

interface props {
  messages: string
  messagesEnd: boolean
  fileList: FileItem[]
  back: () => void
  setGlobalLoading: (type: boolean) => void
  currentSection: number | undefined
}

export const Step2: React.FC<props> = ({ messages, messagesEnd, back, fileList, currentSection, setGlobalLoading }) => {
  const [currentActiveKey, setCurrentActiveKey] = useState('0')
  return (
    <div style={{ padding: '0 20px 20px 20px' }}>
      {currentSection === 1 && (
        <Button icon={<LeftOutlined />} style={{ position: 'fixed', left: 0, top: 15 }} type='link' onClick={back}>
          返回
        </Button>
      )}
      <Row gutter={48}>
        <Col xs={24} md={10}>
          <Tabs
            defaultActiveKey='0'
            activeKey={currentActiveKey}
            onChange={setCurrentActiveKey}
            items={fileList.map((x, index) => ({
              key: index + '',
              label: x.name,
              children: (
                <div
                  style={{
                    minHeight: 'calc(100vh - 160px)'
                  }}
                >
                  <embed
                    style={{ width: '100%', height: '100%', minHeight: 'calc(100vh - 160px)' }}
                    type='application/pdf'
                    src={x.url + '#toolbar=0&navpanes=0&scrollbar=0'}
                  ></embed>
                </div>
              )
            }))}
          />
        </Col>
        <Col xs={24} md={14} style={{ paddingTop: '20px' }}>
          <Step2Right
            messages={messages}
            messagesEnd={messagesEnd}
            fileList={[fileList[currentActiveKey ? parseInt(currentActiveKey) : 0]]}
            setGlobalLoading={setGlobalLoading}
          />
        </Col>
      </Row>
    </div>
  )
}

export default Step2
