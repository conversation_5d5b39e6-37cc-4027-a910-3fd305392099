import { useState, useEffect, useCallback } from 'react'
import { Upload, Button, Typography, Card, Spin, message, Form, Flex, Layout } from 'antd'
import { CheckCircleFilled, InboxOutlined, DeleteOutlined } from '@ant-design/icons'
import ReactMarkdown from 'react-markdown'
import StreamTypewriter from '@/component/StreamTypewriter'
import GetKey from '@/component/getKey'
import { uploadFile } from '@/api/template'
import { calibrationContractV2 } from '@/api/contract'
import { extractContent } from '@/utils/common'
import './index.less'

const CONTRACT_TOOLSV2_TOKEN = import.meta.env['VITE_CONTRACT_TOOLSV2_TOKEN'] || ''

export const ContractToolsV2 = () => {
  const [key, setKey] = useState('')
  const [open, setOpen] = useState(false)
  const [messageApi, contextHolder] = message.useMessage()
  const [uploadedFiles, setUploadedFiles] = useState<{ id: string; name: string }[]>([])
  const [startSending, setStartSending] = useState<boolean>(false)
  const [generating, setGenerating] = useState<boolean>(false)
  const [markdownTable1, setMarkdownTable1] = useState('')
  const [markdownTable2, setMarkdownTable2] = useState('')
  const [markdownList1, setMarkdownList1] = useState<string[]>([])
  const [markdownList2, setMarkdownList2] = useState<string[]>([])
  const [messagesEnd, setMessagesEnd] = useState<boolean>(false)
  const [isShowMk, setIsShowMk] = useState(false)
  const components = {
    pre: ({ node, ...props }: any) => <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }} {...props} />
  }

  useEffect(() => {
    if (uploadedFiles.length > 0) {
      messageApi.open({
        key: 'uploading',
        type: 'success',
        content: '文件上传成功',
        duration: 1
      })
    }
  }, [uploadedFiles, messageApi])

  const beforeUpload = (file: File) => {
    if (!key) {
      setOpen(true)
      return false
    }
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)?.toLowerCase()
    if (['docx'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中'
      })

      uploadFile(file, CONTRACT_TOOLSV2_TOKEN).then(async response => {
        if (response.id) {
          setUploadedFiles(prevFiles => [...prevFiles, response])
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
        } else {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件上传失败',
            duration: 1
          })
        }
      })
    } else {
      messageApi.error('目前仅支持.docx类型的文件，请您将文件转成这些格式后再次进行上传')
    }
    return false
  }

  const handleDelete = (fileId: string) => {
    setUploadedFiles(prevFiles => prevFiles.filter(file => file.id !== fileId))
  }

  const handleGenerationStart = (type: string) => {
    if (!key) {
      setOpen(true)
      return
    }
    if (uploadedFiles.length === 0) {
      messageApi.error('请上传至少一个文件')
      return
    }
    setStartSending(true)
    const fileIds = uploadedFiles.map(file => file.id)
    handleGeneration(fileIds, type)
    if (type === '案例库') {
      setMarkdownTable1('')
      setMarkdownList1([])
    } else if (type === '民法典') {
      setMarkdownTable2('')
      setMarkdownList2([])
    }
    setIsShowMk(true)
  }
  const handleGeneration = useCallback(
    async (fileIds: string[], type: string) => {
      setGenerating(true)
      let accumulatedMessages = ''
      try {
        await calibrationContractV2(
          {
            key,
            type: type,
            files: fileIds.map(x => ({
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: x
            }))
          },
          {
            onMessage: (text: string | null, finished: boolean) => {
              if (text) {
                setGenerating(false)
                accumulatedMessages += text
                const cleanedData = accumulatedMessages.replace(/^```markdown\s*|```$/g, '')
                if (type === '案例库') {
                  setMarkdownTable1(cleanedData)
                } else if (type === '民法典') {
                  setMarkdownTable2(cleanedData)
                }
              }
              if (finished) {
                setMessagesEnd(true)
                const errorStr = extractContent(accumulatedMessages, 'error')
                if (errorStr) {
                  messageApi.error(errorStr)
                  setKey('')
                  setOpen(true)
                  return
                }
                if (type === '案例库') {
                  setMarkdownList1(accumulatedMessages.split('\n---\n'))
                } else if (type === '民法典') {
                  setMarkdownList2(accumulatedMessages.split('\n---\n'))
                }
              }
            },
            onError: () => {
              setGenerating(false)
              setMessagesEnd(true)
            },
            onFinish: () => {
              setGenerating(false)
              setMessagesEnd(true)
            }
          }
        )
      } catch (err) {
        setGenerating(false)
        setMessagesEnd(true)
      }
    },
    [key]
  )

  return (
    <>
      {contextHolder}
      <Spin tip='处理中...' spinning={generating} fullscreen size='large' />
      <GetKey open={open} onClose={setOpen} onChange={setKey} />
      <Layout className='contract-v2-tools-container'>
        <Layout.Sider width={isShowMk ? '500px' : '100%'} style={{ backgroundColor: '#f5f5f5', overflow: 'auto' }}>
          <Card className='contract-v2-left-panel'>
            <Typography.Text className='title-text'>合同批注</Typography.Text>
            <Form layout='vertical'>
              <Form.Item label='上传文件'>
                <Upload.Dragger multiple showUploadList={false} beforeUpload={beforeUpload}>
                  <div className='ant-upload-drag-icon'>
                    {uploadedFiles.length > 0 ? <CheckCircleFilled /> : <InboxOutlined />}
                  </div>
                  <div className='ant-upload-hint'>
                    <span>拖拽文件到此处上传</span>
                    <br />
                    <span style={{ fontSize: '12px', color: '#999' }}>或点击选择文件</span>
                  </div>
                </Upload.Dragger>

                {uploadedFiles.length > 0 && (
                  <div className='contract-v2-file-list'>
                    {uploadedFiles.map(file => (
                      <div key={file.id} className='file-item'>
                        <span style={{ flex: 1 }}>{file.name}</span>
                        <DeleteOutlined
                          onClick={() => handleDelete(file.id)}
                          style={{ cursor: 'pointer', flex: '0 0 20px' }}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </Form.Item>
              <Form.Item>
                <Button
                  type='primary'
                  disabled={uploadedFiles.length === 0 || generating}
                  onClick={() => {
                    handleGenerationStart('案例库')
                  }}
                  style={{ width: '100%', marginBottom: '12px' }}
                >
                  案例库比对
                </Button>
                <Button
                  type='primary'
                  disabled={uploadedFiles.length === 0 || generating}
                  onClick={() => {
                    handleGenerationStart('民法典')
                  }}
                  style={{ width: '100%' }}
                >
                  《民法典》检验
                </Button>
              </Form.Item>
            </Form>
            <Typography.Text>
              您好，作为资深法律专家和合同审批师，我可以结合案例知识库里优秀案例或根据《民法典》及相关法律法规，对您的合同进行详细审核，找出合同条款中需要修改的地方，并识别可能缺失的条款。
              请提供您需要审核的合同内容。
            </Typography.Text>
          </Card>
        </Layout.Sider>
        {isShowMk && (
          <Layout.Content style={{ padding: 24, background: '#fff' }}>
            <Flex align='center' justify='center' style={{ height: '50px' }}>
              <Typography.Title level={3}>合同分析</Typography.Title>
            </Flex>
            {startSending && (
              <div
                className='scroll-container'
                style={{
                  height: 'calc(100vh - 135px)',
                  marginTop: 15,
                  overflow: 'auto',
                  width: '100%'
                }}
              >
                <Flex gap={48}>
                  {markdownTable1 && (
                    <div
                      style={{
                        maxWidth: 'min(50%, 800px)',
                        minWidth: '448px'
                      }}
                    >
                      <Typography.Title level={4}>案例库比对</Typography.Title>
                      {markdownTable1 ? (
                        markdownList1.length > 0 ? (
                          markdownList1
                            .filter(ele => ele !== '')
                            .map((item, index) => (
                              <Card
                                key={index}
                                style={{
                                  width: '100%',
                                  maxWidth: 'min(100%, 800px)',
                                  minWidth: '448px',
                                  marginBottom: '24px'
                                }}
                              >
                                <div style={{ maxWidth: 'min(cala(100% - 24px), 776px)', minWidth: '400px' }}>
                                  <ReactMarkdown className='markdown-body' components={components}>
                                    {item}
                                  </ReactMarkdown>
                                </div>
                              </Card>
                            ))
                        ) : (
                          <Card style={{ width: '100%', maxWidth: 'min(100%, 800px)', minWidth: '448px' }}>
                            <div style={{ maxWidth: 'min(cala(100% - 24px), 776px)', minWidth: '400px' }}>
                              <StreamTypewriter text={markdownTable1} end={messagesEnd} components={components} />
                            </div>
                          </Card>
                        )
                      ) : (
                        <Flex justify='justify' align='center' vertical>
                          <Typography.Text>{'正在提取文件信息，请不要关闭或刷新页面'}</Typography.Text>
                        </Flex>
                      )}
                    </div>
                  )}
                  {markdownTable2 && (
                    <div
                      style={{
                        maxWidth: '50%',
                        minWidth: '448px'
                      }}
                    >
                      <Typography.Title level={4}>《民法典》检验</Typography.Title>
                      {markdownTable2 ? (
                        markdownList2.length > 0 ? (
                          markdownList2
                            .filter(ele => ele !== '')
                            .map((item, index) => (
                              <Card
                                key={index}
                                style={{
                                  width: '100%',
                                  maxWidth: 'min(100%, 800px)',
                                  minWidth: '448px',
                                  marginBottom: '24px'
                                }}
                              >
                                <div style={{ maxWidth: 'min(cala(100% - 24px), 776px)', minWidth: '400px' }}>
                                  <ReactMarkdown className='markdown-body' components={components}>
                                    {item}
                                  </ReactMarkdown>
                                </div>
                              </Card>
                            ))
                        ) : (
                          <Card style={{ width: '100%', maxWidth: 'min(50%, 800px)', minWidth: '448px' }}>
                            <div style={{ maxWidth: 'min(cala(100% - 24px), 776px)', minWidth: '400px' }}>
                              <StreamTypewriter text={markdownTable2} end={messagesEnd} />
                            </div>
                          </Card>
                        )
                      ) : (
                        <Flex justify='justify' align='center' vertical>
                          <Typography.Text>{'正在提取文件信息，请不要关闭或刷新页面'}</Typography.Text>
                        </Flex>
                      )}
                    </div>
                  )}
                </Flex>
              </div>
            )}
          </Layout.Content>
        )}
      </Layout>
    </>
  )
}

export default ContractToolsV2
