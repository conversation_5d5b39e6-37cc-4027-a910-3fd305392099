.contract-v2-tools-container {
  background: #f5f5f5;
  display: flex;
  gap: 24px;
  // max-width: 1600px;
  margin: 0 auto;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  // 左侧面板样式
  .contract-v2-left-panel {
    background: #ffffff;
    padding: 24px;
    max-width: 500px;
    margin: 50px auto 0;

    .title-text {
      font-size: 24px;
      font-weight: 600;
      text-align: center;
      color: #1a1a1a;
      padding-bottom: 8px;
      display: block;
    }
  }

  // 上传区域样式
  .ant-upload-drag {
    padding: 24px;
    border: 2px dashed #e8e8e8;
    border-radius: 12px;
    background: #fafafa;
    text-align: center;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1890ff;
      background: #f0f7ff;
    }

    .ant-upload-drag-icon {
      margin-bottom: 16px;
      font-size: 48px;
      color: #1890ff;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  // 文件列表样式
  .contract-v2-file-list {
    margin-top: 16px;
    max-height: 200px;
    overflow-y: auto;
    padding-right: 4px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f5f5;
      border-radius: 3px;
    }

    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: #f8f9fa;
      border-radius: 6px;
      margin-bottom: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: #f0f2f5;
      }

      span {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 12px;
      }
    }
  }

  // 输入框样式
  .ant-input {
    border-radius: 8px;

    &:hover,
    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }
  }
}
