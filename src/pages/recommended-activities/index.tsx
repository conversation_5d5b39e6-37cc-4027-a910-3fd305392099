import React, { useState } from 'react'
import { Button, message, Card, Typography, Spin, Layout, Flex, Form, Input } from 'antd'
import { recommendedActivities } from '@/api/recommendedActivities'
import { extractContent } from '@/utils/common'
import StreamTypewriter from '@/component/StreamTypewriter'
import './index.less'

const { Title } = Typography

export const RecommendedActivities: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState(false)
  const [mk, setMk] = useState('')
  const [isShowMk, setIsShowMk] = useState(false)
  const [streamTypewriterKey, setStreamTypewriterKey] = useState(1)
  const scrollRef = React.useRef<HTMLDivElement>(null)
  const [form] = Form.useForm()

  const handleDownload = async () => {
    setStreamTypewriterKey(streamTypewriterKey + 1)
    setMk('')
    setIsShowMk(true)
    setGenerating(true)
    messageApi.success('开始校验，请稍候...')
    let res = ''
    try {
      await recommendedActivities(
        {
          ...form.getFieldsValue()
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              setMk(p => p + message || '')
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {
            setGenerating(false)
          }
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  return (
    <div className='recommended-activities-container'>
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      {contextHolder}
      <Layout style={{ height: '100%' }}>
        <Layout.Sider width={isShowMk ? '600px' : '100%'} style={{ backgroundColor: '#f0f2f5', padding: '0 20px' }}>
          <Card className='recommended-activities-card'>
            <Title level={2} className='page-title'>
              党建活动推荐
            </Title>
            <Form layout='vertical' className='activity-form' form={form}>
              <Form.Item name='type' label='党组织类型'>
                <Input placeholder='请输入党组织类型' className='activity-input' />
              </Form.Item>

              <Form.Item name='scale' label='活动规模'>
                <Input
                  type='number'
                  min={0}
                  placeholder='请输入参与人数'
                  onInput={(e: any) => {
                    e.target.value = e.target.value.replace(/\-/g, '').replace(/\./g, '')
                  }}
                  className='activity-input'
                />
              </Form.Item>

              <Form.Item name='budget' label='活动预算'>
                <Input
                  type='number'
                  min={0}
                  placeholder='请输入预算金额'
                  onInput={(e: any) => {
                    e.target.value = e.target.value.replace(/\-/g, '')
                  }}
                  className='activity-input'
                />
              </Form.Item>

              <Form.Item name='query' label='活动主题'>
                <Input placeholder='请输入活动主题' className='activity-input' />
              </Form.Item>
            </Form>

            <Button
              type='primary'
              onClick={handleDownload}
              size='large'
              loading={generating}
              block
              style={{ marginTop: '20px' }}
            >
              开始推荐
            </Button>
          </Card>
        </Layout.Sider>
        {isShowMk && (
          <Layout.Content style={{ padding: 24, background: '#fff' }}>
            <Flex align='center' justify='center' style={{ height: '50px' }}>
              <Title level={3}>推荐结果</Title>
            </Flex>

            <div
              className='scroll-container'
              ref={scrollRef}
              style={{ height: 'calc(100vh - 98px)', overflowY: 'auto' }}
            >
              <StreamTypewriter
                key={streamTypewriterKey}
                text={mk}
                end={!generating}
                onchange={() => {
                  scrollRef.current?.scrollTo({ top: scrollRef.current.scrollHeight, behavior: 'smooth' })
                }}
                components={{
                  p: ({ node, ...props }: any) => <p style={{ whiteSpace: 'pre-line', marginTop: 10 }} {...props} />
                }}
              />
            </div>
          </Layout.Content>
        )}
      </Layout>
    </div>
  )
}

export default RecommendedActivities
