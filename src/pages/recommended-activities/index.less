.recommended-activities-container {
  height: 100vh;
  background-color: #f0f2f5;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  .activity-container {
    padding: 40px;
    max-width: 600px;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .activity-title {
    text-align: center;
    color: #ff4d4f;
    margin-bottom: 20px;
    font-size: 24px;
  }

  .activity-form {
    width: 100%;
  }

  .activity-input {
    border-radius: 4px;
    border: 1px solid #d9d9d9;
  }

  .activity-input:hover,
  .activity-input:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 5px rgba(64, 169, 255, 0.5);
  }

  .activity-button {
    width: 100%;
    background-color: #ff4d4f;
    border-color: #ff4d4f;
  }

  .activity-button:hover {
    background-color: #c9302c;
    border-color: #d9534f;
  }

  .markdown-body ol,
  .markdown-body ul {
    padding-left: 20px;
  }
}

.recommended-activities-card {
  width: 100%;
  max-width: 600px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  transition: all 0.3s ease;
  margin: 20vh auto;
  flex: 1;

  .page-title {
    margin-bottom: 16px;
    color: #1f1f1f;
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      font-size: 24px;
      color: #1890ff;
    }
  }

  .page-description {
    display: block;
    margin: 16px 0;
    font-size: 14px;
  }
}
