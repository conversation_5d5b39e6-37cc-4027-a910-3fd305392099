import React, { useState,useEffect } from 'react'
import { Upload, Button, message, Card, Typography, Spin, Layout, Flex } from 'antd'
import { CheckCircleFilled, InboxOutlined } from '@ant-design/icons'
import { verificationDocument } from '@/api/documentVerification'
import { uploadFile } from '@/api/template'
import { extractContent } from '@/utils/common'
import StreamTypewriter from '@/component/StreamTypewriter'
import './index.less'

const { Title, Text } = Typography
const token = import.meta.env['VITE_DOCUMENT_VERIFICATION_TOKEN'] || ''

export const DocumentVerification: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [generating, setGenerating] = useState(false)
  const [mk, setMk] = useState('')
  const [isShowMk, setIsShowMk] = useState(false)
  const [streamTypewriterKey, setStreamTypewriterKey] = useState(1)
  const [uploadedFile, setUploadedFile] = useState<{ id: string; name: string }>({ id: '', name: '' })
  const scrollRef = React.useRef<HTMLDivElement>(null)

  useEffect(() => {
    // 获取完整的 URL 哈希部分
    const hash = window.location.hash

    // 在哈希中查找查询字符串的起始位置（第一个 '?'）
    const queryStringIndex = hash.indexOf('?')

    let fileParamsValue = null

    if (queryStringIndex !== -1) {
      // 提取查询字符串部分
      const queryString = hash.substring(queryStringIndex + 1)

      // 使用 URLSearchParams 对象来方便解析参数
      const params = new URLSearchParams(queryString)

      // 使用 get() 方法获取 'fileParams' 参数的原始编码值
      const rawFileParamsValue = params.get('fileParams')

      // 如果获取到了值，则进行解码
      if (rawFileParamsValue !== null) {
        try {
          fileParamsValue = decodeURIComponent(rawFileParamsValue)
        } catch (error) {
          console.error('解码 fileParams 值失败:', error)
          // 如果解码失败，可以使用原始值或者设置为 null
          // fileParamsValue = rawFileParamsValue // 或者 null
        }
      }
    }

    // 检查是否获取到了值
    if (fileParamsValue) {
      console.log('从地址栏哈希中获取到并解码 fileParams:', fileParamsValue)
      processMarkdownLinkAndUpload(fileParamsValue)
    } else {
      console.log('地址栏哈希中没有找到 fileParams 参数')
    }
  }, []) // [] 作为依赖项表示只在组件挂载时运行一次

  const processMarkdownLinkAndUpload = async (markdownLink: string) => {
    console.log('正在处理文件链接:', markdownLink)
    // 解析字符串，提取文件名和 URL
    const match = markdownLink.match(/\[(.*?)\]\((.*?)\)/)

    if (!match || match.length < 3) {
      console.error('无效的 markdown 链接格式')
      messageApi.error('无效的文件链接格式')
      return
    }

    const fileName = match[1]
    const fileUrl = match[2] // 提取到的 URL
    // const fileUrl = `https://copilot.sino-bridge.com${match[2]}` // 本地调试 提取到的 URL

    // 下载文件内容
    try {
      const response = await fetch(fileUrl)
      console.log(response, 'response')

      if (!response.ok) {
        console.error(
          `HTTP error! status: ${response.status} for URL: ${fileUrl}`
        )
        const errorText = await response.text()
        console.error('响应内容:', errorText)
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const blob = await response.blob()
      console.log('下载到的 Blob 类型:', blob.type, '大小:', blob.size)
      // 检查下载的文件类型是否是预期的 .docx 或 .pdf
      const expectedTypes = [
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
        'application/pdf', // .pdf
      ]

      if (!expectedTypes.includes(blob.type)) {
        console.warn('下载的文件类型与预期不符:', blob.type)
        messageApi.warning(
          `下载的文件类型 ${blob.type} 与预期 (.docx/.pdf) 不符.`
        )
        // 决定是否继续处理非预期类型的文件
        return; // 如果不想处理非预期类型的文件，可以 uncomment 这行
      }

      // 创建 File 对象
      // 使用提取的文件名和下载到的 blob type
      const file = new File([blob], fileName, { type: blob.type })

      console.log('创建的 File 对象:', file)

      // 调用 beforeUpload 方法
      // 注意: beforeUpload 需要处理异步上传逻辑，并且返回 false 阻止默认行为
      beforeUpload(file)
    } catch (error: any) {
      console.error('下载或处理文件失败:', error)
      messageApi.open({
        key: 'downloading',
        type: 'error',
        content: `下载或处理文件 "${fileName}" 失败: ${error.message}`,
        duration: 3,
      })
    }
  }
  const beforeUpload = (file: File) => {
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)?.toLowerCase()
    if (!['txt', 'docx', 'mdx', 'pdf', 'html', 'xlsx', 'xls', 'csv', 'md', 'html'].includes(originalFileExt)) {
      return false
    }
    messageApi.open({
      key: 'uploading',
      type: 'loading',
      content: '文件上传中'
    })
    setGenerating(true)
    uploadFile(file, token)
      .then(response => {
        setGenerating(false)
        if (response && response.id) {
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
          setUploadedFile(response)
          return false // 阻止自动上传
        } else {
          throw new Error('上传失败：未收到有效的响应')
        }
      })
      .catch(error => {
        setGenerating(false)
        messageApi.open({
          key: 'uploading',
          type: 'error',
          content: `文件上传失败: ${error.message}`,
          duration: 2
        })
      })
    return false // 阻止自动上传
  }

  const handleDownload = async () => {
    if (!uploadedFile.id) {
      messageApi.error('文件未上传')
      return
    }
    setStreamTypewriterKey(streamTypewriterKey + 1)
    setMk('')
    setIsShowMk(true)
    setGenerating(true)
    messageApi.success('开始校验，请稍候...')
    let res = ''
    try {
      await verificationDocument(
        {
          files: [
            {
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: uploadedFile.id
            }
          ]
        },
        {
          onMessage: (message: string | null, finished: boolean) => {
            if (message) {
              res += message || ''
              setMk(p => p + message || '')
            }
            if (finished) {
              const errorStr = extractContent(res, 'error')
              if (errorStr) {
                messageApi.error(errorStr)
                return
              }
            }
          },
          onError: () => {
            setGenerating(false)
          },
          onFinish: () => {
            setGenerating(false)
          }
        }
      )
    } catch (err) {
      setGenerating(false)
    }
  }

  return (
    <div className='document-verification-container'>
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      {contextHolder}
      <Layout style={{ height: '100%' }}>
        <Layout.Sider width={isShowMk ? '600px' : '100%'} style={{ backgroundColor: '#f0f2f5', padding: '0 20px' }}>
          <Card className='document-verification-card'>
            <Title level={2} className='page-title'>
              公文错别字校验
            </Title>
            <Text type='secondary' className='page-description'>
              可拆解长难句，进行语法结构分析，逻辑矛盾检测，识别语病，错字稽查。
            </Text>

            <Upload.Dragger showUploadList={false} multiple={false} beforeUpload={beforeUpload}>
              <p className='ant-upload-drag-icon'>{uploadedFile.id ? <CheckCircleFilled /> : <InboxOutlined />}</p>
              <p className='ant-upload-text'>
                {uploadedFile.id ? uploadedFile.name : '点击或者将文件拖拽到这里进行上传'}
              </p>
              <p className='ant-upload-hint'>
                {uploadedFile.id ? (
                  '点击或者将文件拖拽到这里重新上传'
                ) : (
                  <>
                    <span>支持 txt、 docx、 mdx、 pdf、 html、 xlsx、 xls、 csv、 md、 htm格式</span>
                    <br />
                    <span>注：建议上传docx格式文件，系统仅支持docx文件标注输出。</span>
                  </>
                )}
              </p>
            </Upload.Dragger>

            <Button
              type='primary'
              onClick={handleDownload}
              size='large'
              loading={generating}
              block
              style={{ marginTop: '20px' }}
            >
              开始校验
            </Button>
          </Card>
        </Layout.Sider>
        {isShowMk && (
          <Layout.Content style={{ padding: 24, background: '#fff' }}>
            <Flex align='center' justify='center' style={{ height: '50px' }}>
              <Title level={3}>分析结果</Title>
            </Flex>

            <div
              className='scroll-container'
              ref={scrollRef}
              style={{ height: 'calc(100vh - 98px)', overflowY: 'auto' }}
            >
              <StreamTypewriter
                key={streamTypewriterKey}
                text={mk}
                end={!generating}
                onchange={() => {
                  scrollRef.current?.scrollTo({ top: scrollRef.current.scrollHeight, behavior: 'smooth' })
                }}
                components={{
                  p: ({ node, ...props }: any) => <p style={{ whiteSpace: 'pre-line', marginTop: 10 }} {...props} />,
                  a: ({ href, children }: any) => {
                    return (
                      <>
                        公文批注文件：
                        <a href={href}>{children}</a>
                      </>
                    )
                  }
                }}
              />
            </div>
          </Layout.Content>
        )}
      </Layout>
    </div>
  )
}

export default DocumentVerification
