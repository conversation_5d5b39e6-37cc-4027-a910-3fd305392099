import { useCallback, useState, useEffect } from 'react'
import { Upload, Button, Typography, Card, Spin, Flex, Input, message, Table, Form } from 'antd'
import { CheckCircleFilled, InboxOutlined, DeleteOutlined } from '@ant-design/icons'
import { extractContent } from '@/utils/common'
import GetKey from '@/component/getKey'
import { materialContract } from '@/api/materialSummary'
import { uploadFile } from '@/api/template'
import './index.less'

const MATERIAL_SUMMARY_TOKEN = import.meta.env['VITE_MATERIAL_SUMMARY_TOKEN'] || ''

export const MaterialSummary = () => {
  const [key, setKey] = useState('')
  const [open, setOpen] = useState(false)
  const [messageApi, contextHolder] = message.useMessage()
  const [isToggle, setIsToggle] = useState<boolean>(false)
  const [materialContent, setMaterialContent] = useState<string>('')
  const [verInfo, setVerInfo] = useState<string>('')
  const [uploadedFiles, setUploadedFiles] = useState<{ id: string; name: string }[]>([])
  const [startSending, setStartSending] = useState<boolean>(false)
  const [generating, setGenerating] = useState<boolean>(false)
  const [messages, setMessages] = useState<string>('')
  const [tableList, setTableList] = useState<any[]>([])

  useEffect(() => {
    if (uploadedFiles.length > 0) {
      messageApi.open({
        key: 'uploading',
        type: 'success',
        content: '文件上传成功',
        duration: 1
      })
    }
  }, [uploadedFiles, messageApi])

  const beforeUpload = (file: File) => {
    if (!key) {
      setOpen(true)
      return false
    }
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)?.toLowerCase()
    if (['pdf', 'docx', 'xlsx'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中'
      })

      uploadFile(file, MATERIAL_SUMMARY_TOKEN).then(async response => {
        if (response.id) {
          setUploadedFiles(prevFiles => [...prevFiles, response])
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
        } else {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件上传失败',
            duration: 1
          })
        }
      })
    } else {
      messageApi.error('目前仅支持.docx, .pdf, .xlsx类型的文件，请您将文件转成这些格式后再次进行上传')
    }
    return false // 阻止自动上传
  }

  const handleDelete = (fileId: string) => {
    setUploadedFiles(prevFiles => prevFiles.filter(file => file.id !== fileId))
  }

  const handleGenerationStart = () => {
    if (!key) {
      setOpen(true)
      return false
    }
    setIsToggle(true)
    setStartSending(true)
    const fileIds = uploadedFiles.map(file => file.id)
    handleGeneration(fileIds)
  }

  const handleGeneration = useCallback(
    async (fileIds: string[]) => {
      setGenerating(true)
      let accumulatedMessages = ''

      try {
        await materialContract(
          {
            query: materialContent,
            verInfo: verInfo,
            key: key || '',
            files: fileIds.map(x => ({
              type: 'document',
              transfer_method: 'local_file',
              upload_file_id: x
            }))
          },
          {
            onMessage: (text: string | null, finished: boolean) => {
              if (text) {
                accumulatedMessages += text
              }
              if (finished) {
                setGenerating(false)
                const errorStr = extractContent(accumulatedMessages, 'error')
                if (errorStr) {
                  message.error(errorStr)
                  setKey('')
                  setOpen(true)
                  return
                }
                try {
                  const list = JSON.parse(accumulatedMessages)

                  list.forEach((item: { key: any }, index: any) => {
                    item.key = index
                  })
                  setTableList(list)
                } catch (e) {
                  setMessages(accumulatedMessages)
                }
              }
            },
            onError: () => {
              setGenerating(false)
            },
            onFinish: () => {
              setGenerating(false)
            }
          }
        )
      } catch (err) {
        setGenerating(false)
      }
    },
    [materialContent, verInfo]
  )

  // 动态生成表头和渲染数据的处理
  const generateColumns = (data: any[]) => {
    if (data.length === 0) return []
    return Object.keys(data[0])
      .filter(key => key !== 'key')
      .map(key => ({
        title: key, // 表头名称
        dataIndex: key, // 数据索引
        key,
        render: (value: any) => {
          return typeof value === 'object' ? (
            <div dangerouslySetInnerHTML={{ __html: formatNestedData(value) }} />
          ) : (
            value
          )
        }
      }))
  }

  const formatNestedData = (data: any) => {
    if (typeof data !== 'object' || data === null) {
      return data
    }

    const formattedItems: string[] = []

    Object.keys(data).forEach(key => {
      const value = data[key]
      if (typeof value === 'object' && value !== null) {
        formattedItems.push(`<b>${key}</b>: <div style="padding-left: 20px">${formatNestedData(value)}</div>`)
      } else {
        formattedItems.push(`<b>${key}</b>: ${value}`)
      }
    })

    return formattedItems.join('<br>')
  }
  // 展开与折叠
  const handleToggle = () => {
    setIsToggle(!isToggle)
  }
  return (
    <>
      {contextHolder}
      <Spin tip='加载中' spinning={generating} fullscreen size='large' />
      <GetKey open={open} onClose={setOpen} onChange={setKey} />

      <Flex className='toolbar' justify='center'>
        <Typography.Text className='title-text'>材料汇总查询</Typography.Text>
      </Flex>

      <div
        style={{
          width: '1200px',
          overflow: 'hidden',
          padding: '0 20px',
          margin: '0px auto'
        }}
      >
        <Card
          className='material-template-form'
          style={{
            height: isToggle ? '78px' : 'auto',
            overflow: isToggle ? 'hidden' : 'auto'
          }}
        >
          <Flex vertical gap='middle'>
            <Flex className='file-tit' justify='space-between' align='center'>
              <Flex
                style={{
                  color: '#1888ff',
                  fontWeight: 'bold'
                }}
              >
                材料内容
              </Flex>
              <Flex
                style={{
                  color: '#1888ff',
                  cursor: 'pointer',
                  userSelect: 'none'
                }}
                onClick={handleToggle}
              >
                {isToggle ? '展开' : '收起'}
              </Flex>
            </Flex>
            <Form labelCol={{ span: 2.5 }} wrapperCol={{ span: 21.5 }} initialValues={{ remember: true }}>
              <Form.Item label='上传文件' name='username'>
                <Upload.Dragger multiple showUploadList={false} beforeUpload={beforeUpload}>
                  <div className='ant-upload-drag-icon'>
                    {uploadedFiles.length > 0 ? <CheckCircleFilled /> : <InboxOutlined />}
                  </div>
                  <div className='ant-upload-hint'>
                    {uploadedFiles.length > 0 ? (
                      '点击或者将文件拖拽到这里重新上传'
                    ) : (
                      <span>在这里上传您的文件，让AI帮您进行解析</span>
                    )}
                  </div>
                  <div className='ant-upload-text'>
                    {uploadedFiles.length > 0
                      ? uploadedFiles.map((file, index) => (
                          <div
                            key={file.id}
                            style={{ display: 'flex', alignItems: 'center' }}
                            onClick={e => e.stopPropagation()}
                          >
                            <Button type='link'>
                              {index + 1 + '.' + file.name}
                              <DeleteOutlined onClick={() => handleDelete(file.id)} />
                            </Button>
                          </div>
                        ))
                      : '点击或者将文件拖拽到这里进行上传'}
                  </div>
                </Upload.Dragger>
              </Form.Item>
              <Form.Item label='汇总内容' name='query'>
                <Input
                  placeholder='请输入需要汇总的内容'
                  v-model='materialContent'
                  onChange={e => {
                    setMaterialContent(e.target.value.trim())
                  }}
                />
              </Form.Item>
              <Form.Item label='校验内容' name='verInfo'>
                <Input
                  placeholder='请输入校验内容'
                  v-model='verInfo'
                  onChange={e => {
                    setVerInfo(e.target.value.trim())
                  }}
                  maxLength={48}
                />
              </Form.Item>
            </Form>
            <Button
              size='large'
              type='primary'
              disabled={uploadedFiles.length === 0 || generating || materialContent == ''}
              onClick={handleGenerationStart}
            >
              开 始 汇 总
            </Button>
          </Flex>
        </Card>

        {startSending && (
          <Flex align='center' justify='center' style={{ marginTop: 15, width: '100%', overflow: 'auto' }}>
            {tableList.length > 0 ? (
              <Table
                columns={generateColumns(tableList)}
                dataSource={tableList}
                rowKey={record => record.key}
                pagination={false}
                bordered
              />
            ) : (
              <Flex justify='justify' align='center' vertical>
                <Typography.Text>{messages || '正在提取文件信息，请不要关闭或刷新页面'}</Typography.Text>
              </Flex>
            )}
          </Flex>
        )}
      </div>
    </>
  )
}

export default MaterialSummary
