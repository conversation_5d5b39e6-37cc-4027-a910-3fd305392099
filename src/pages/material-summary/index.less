
.toolbar {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;

  .title-text {
    font-size: 18px;
    font-weight: 600;
  }
}

.material-template-form {
  // max-width: 800px;
  margin: auto;
  padding: 20px;
  .ant-card-body{
    padding-top:10px;

  }
  .file-tit{
    width: 100%;
    font-size: 16px;
    margin-bottom:6px;
  }
}

.preview-content {
  max-width: 800px;
  margin: auto;
  padding: 20px;
}

.custom-scrollbar {
  max-height: 500px;
  overflow-y: auto;
}

.ant-upload-drag-icon {
  color: #1677ff;
  font-size: 48px;
}

@media (max-width: 768px) {
  .template-form,
  .preview-content {
    padding: 10px;
  }

  .toolbar .title-text {
    font-size: 16px;
  }

  .ant-upload-drag-icon {
    font-size: 24px;
  }

  .ant-upload-text,
  .ant-upload-hint {
    font-size: 14px;
  }

  .ant-btn {
    width: 100%;
  }
}
