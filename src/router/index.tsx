/** 配置管理路由主文件 */
import { lazy, Suspense } from "react";
import { createHashRouter, Navigate } from "react-router-dom";
import { Spin } from "antd";
import App from "@/App";

const Loading = () => (
  <div
    style={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "100vh",
    }}
  >
    <Spin size="large" />
  </div>
);

// 使用 React.lazy 懒加载组件
const Interpreter = lazy(() => import("@/pages/interpreter"));
const NotFound = lazy(() => import("@/pages/not-found"));
const TemplateRenderer = lazy(() => import("@/pages/template-renderer"));
const PolicyDaily = lazy(() => import("@/pages/policy-daily"));
const ContractVerification = lazy(
  () => import("@/pages/contract-verification")
);
const TableSummary = lazy(() => import("@/pages/table-summary"));
const ContractQuery = lazy(() => import("@/pages/contract-query"));
const BusinessQuery = lazy(() => import("@/pages/business-query"));
const WorkOrderQuery = lazy(() => import("@/pages/work-order-query"));
const PolicyAssistant = lazy(() => import("@/pages/policy-assistant"));
const DocumentSummary = lazy(() => import("@/pages/document-summary"));
const MaterialSummary = lazy(() => import("@/pages/material-summary"));
const RegulatoryVerification = lazy(
  () => import("@/pages/regulatory-verification")
);
const SystemVerification = lazy(() => import("@/pages/system-verification"));
const IndustryChainMap = lazy(() => import("@/pages/industry-chain-map"));
const ContractQueryAssistant = lazy(
  () => import("@/pages/contract-query-assistant")
);
const BidWritingAssistant = lazy(() => import("@/pages/bid-writing-assistant"));
const PolicyInsightSystem = lazy(() => import("@/pages/policy-insight-system"));
const EnterpriseCreditEvaluation = lazy(
  () => import("@/pages/enterprise-credit-assessment")
);
const CustomerChurnWarning = lazy(
  () => import("@/pages/customer-churn-warning")
);
const EmployeeKnowledgeAnswer = lazy(
  () => import("@/pages/employee-knowledge-answer")
);
const FinancialProducts = lazy(() => import("@/pages/financial-products"));
const DevelopProcessAIDS = lazy(
  () => import("@/pages/develop-process-AIDS-v2")
);
const EnterpriseProfile = lazy(() => import("@/pages/enterprise-profile"));
const PersonalProfile = lazy(() => import("@/pages/personal-profile"));
const DocumentAssistant = lazy(() => import("@/pages/document-assistant"));
const WorkReport = lazy(() => import("@/pages/document-assistant/work-report"));
const Experience = lazy(() => import("@/pages/document-assistant/experience"));
const RequestReport = lazy(
  () => import("@/pages/document-assistant/request-report")
);
const SpeakingScript = lazy(
  () => import("@/pages/document-assistant/speaking-script")
);
const Invitation = lazy(() => import("@/pages/document-assistant/invitation"));
const Email = lazy(() => import("@/pages/document-assistant/email"));
const Announcement = lazy(
  () => import("@/pages/document-assistant/announcement")
);
const Notice = lazy(() => import("@/pages/document-assistant/notice"));
const DocumentFormatVerification = lazy(
  () => import("@/pages/document-format-verification")
);
const DocumentSensitiveInspection = lazy(
  () => import("@/pages/document-sensitive-inspection")
);

const PolicyComplianceVerifier = lazy(
  () => import("@/pages/policy-compliance-verifier")
);
const PolicyInterpretation = lazy(
  () => import("@/pages/policy-interpretation")
);
const AnnouncementInquiry = lazy(() => import("@/pages/announcement-inquiry"));
const FinancialAuditAssistant = lazy(
  () => import("@/pages/financial-audit-assistant")
);
const ContractTools = lazy(() => import("@/pages/contract-tools"));
const ContractToolsV2 = lazy(() => import("@/pages/contract-tools-v2"));
const DueDiligenceReport = lazy(() => import("@/pages/due-diligence-report"));
const OrderSituation = lazy(() => import("@/pages/order-situation"));
const AuditMark = lazy(() => import("@/pages/audit-mark"));
const ContractReview = lazy(() => import("@/pages/contract-review"));
const FeasibilityReport = lazy(() => import("@/pages/feasibility-report"));
const DocumentVerification = lazy(
  () => import("@/pages/document-verification")
);
const Flowchart = lazy(() => import("@/pages/flowchart"));
const PolicyMatching = lazy(() => import("@/pages/policy-matching"));
const PolicyMatchDetails = lazy(() => import("@/pages/policy-match-details"));
const ReviewMaterial = lazy(() => import("@/pages/review-material"));
const ChartGeneration = lazy(() => import("@/pages/chart-generation"));
const PartyBuildingAssistant = lazy(
  () => import("@/pages/party-building-assistant")
);
const RecommendedActivities = lazy(
  () => import("@/pages/recommended-activities")
);
const FinancialHealthAssessment = lazy(
  () => import("@/pages/financial-health-assessment")
);
const ContratReviewV2 = lazy(() => import("@/pages/contract-review-v2"));
const ContratReviewV3 = lazy(() => import("@/pages/contract-review-v3"));
const ContratReviewV4 = lazy(() => import("@/pages/contract-review-v4"));
const Ragas = lazy(() => import("@/pages/ragas"));
const RegulatorySearch = lazy(() => import("@/pages/regulatory-search"));
const FinancialanAlysis = lazy(() => import("@/pages/financial-analysis"));
const RegulatoryReporting = lazy(() => import("@/pages/regulatory-reporting"));
const SecuritiesValuation = lazy(() => import("@/pages/securities-valuation"));
const ContractQueryTool = lazy(() => import("@/pages/contract-query-tool"));
const PromptOptimizer = lazy(() => import("@/pages/prompt-optimizer"));
const LoanApproval = lazy(() => import("@/pages/loan-approval"));
const TrustMonthlyReport = lazy(() => import("@/pages/trust-monthly-report"));
const LayoutRecognition = lazy(() => import("@/pages/layout-recognition"));
const PrdContractVer = lazy(() => import("@/pages/prd-contract-ver"));
const PrdPurcontractVer = lazy(() => import("@/pages/prd-purcontract-ver"));
const ResearchReport = lazy(() => import("@/pages/research-report"));
const ContractSceneSet = lazy(() => import("@/pages/contract-scene-set"));
const ContractSceneSetV2 = lazy(() => import("@/pages/contract-scene-set-v2"));
const ContractDataExtractor = lazy(
  () => import("@/pages/contract-data-extractor")
);
const LinguaDocBuilder = lazy(() => import("@/pages/lingua-doc-builder"));
const CashflowAnalysis = lazy(() => import("@/pages/cashflow-analysis"));
const MeetingAudioKnowledgeBase = lazy(
  () => import("@/pages/meeting-audio-knowledge-base")
);
const MeetingVideoKnowledgeBase = lazy(
  () => import("@/pages/meeting-video-knowledge-base")
);
const VoiceQualityAssurance = lazy(
  () => import("@/pages/voice-quality-assurance")
);

const GujingEnglishTraining = lazy(
  () => import("@/pages/gujing-english-training")
);
const GujingCommandDialogue = lazy(
  () => import("@/pages/gujing-command-dialogue")
);

export const routes = [
  {
    path: "/",
    element: <App />,
    children: [
      {
        path: "/interpreter",
        element: (
          <Suspense fallback={<Loading />}>
            <Interpreter />
          </Suspense>
        ),
        title: "代码解释器",
      },
      {
        path: "/flowchart",
        element: (
          <Suspense fallback={<Loading />}>
            <Flowchart />
          </Suspense>
        ),
        title: "关系图",
      },
      {
        path: "/template-render",
        element: (
          <Suspense fallback={<Loading />}>
            <TemplateRenderer />
          </Suspense>
        ),
        title: "模板填充工具",
      },
      {
        path: "/contract-query-tool",
        element: (
          <Suspense fallback={<Loading />}>
            <ContractQueryTool />
          </Suspense>
        ),
        title: "合同解析",
      },
      {
        path: "/policy-daily",
        element: (
          <Suspense fallback={<Loading />}>
            <PolicyDaily />
          </Suspense>
        ),
        title: "政策日报",
      },
      {
        path: "/contract-verification",
        element: (
          <Suspense fallback={<Loading />}>
            <ContractVerification />
          </Suspense>
        ),
        title: "合同智能校验工具",
      },
      {
        path: "/table-summary",
        element: (
          <Suspense fallback={<Loading />}>
            <TableSummary />
          </Suspense>
        ),
        title: "材料汇总工具",
      },
      {
        path: "/contract-query",
        element: (
          <Suspense fallback={<Loading />}>
            <ContractQuery />
          </Suspense>
        ),
        title: "智能合同查询",
      },
      {
        path: "/work-order-query",
        element: (
          <Suspense fallback={<Loading />}>
            <WorkOrderQuery />
          </Suspense>
        ),
        title: "工单查询",
      },
      {
        path: "/policy-assistant",
        element: (
          <Suspense fallback={<Loading />}>
            <PolicyAssistant />
          </Suspense>
        ),
        title: "政策查询助手",
      },
      {
        path: "/document-summary",
        element: (
          <Suspense fallback={<Loading />}>
            <DocumentSummary />
          </Suspense>
        ),
        title: "过程性文档归纳总结",
      },
      {
        path: "/material-summary",
        element: (
          <Suspense fallback={<Loading />}>
            <MaterialSummary />
          </Suspense>
        ),
        title: "材料汇总查询",
      },
      {
        path: "/regulatory-verification",
        element: (
          <Suspense fallback={<Loading />}>
            <RegulatoryVerification />
          </Suspense>
        ),
        title: "规章制度校验",
      },
      {
        path: "/system-verification",
        element: (
          <Suspense fallback={<Loading />}>
            <SystemVerification />
          </Suspense>
        ),
        title: "制度校验系统",
      },
      {
        path: "/bid-writing-assistant",
        element: (
          <Suspense fallback={<Loading />}>
            <BidWritingAssistant />
          </Suspense>
        ),
        title: "标书写作助手",
      },
      {
        path: "/industry-Chain-Map",
        element: (
          <Suspense fallback={<Loading />}>
            <IndustryChainMap />
          </Suspense>
        ),
        title: "产业链图谱",
      },
      {
        path: "/contract-query-assistant",
        element: (
          <Suspense fallback={<Loading />}>
            <ContractQueryAssistant />
          </Suspense>
        ),
        title: "智能合同查询助手",
      },
      {
        path: "/contract-tools",
        element: (
          <Suspense fallback={<Loading />}>
            <ContractTools />
          </Suspense>
        ),
        title: "合同工具",
      },
      {
        path: "/contract-tools-v2",
        element: (
          <Suspense fallback={<Loading />}>
            <ContractToolsV2 />
          </Suspense>
        ),
        title: "合同批注",
      },
      {
        path: "/audit-mark",
        element: (
          <Suspense fallback={<Loading />}>
            <AuditMark />
          </Suspense>
        ),
        title: "律所-合同审核标记",
      },
      {
        path: "/contract-review",
        element: (
          <Suspense fallback={<Loading />}>
            <ContractReview />
          </Suspense>
        ),
        title: "中证-合同审查",
      },
      {
        path: "/contract-review-v2",
        element: (
          <Suspense fallback={<Loading />}>
            <ContratReviewV2 />
          </Suspense>
        ),
        title: "合同审查",
      },
      {
        path: "/contract-review-v3",
        element: (
          <Suspense fallback={<Loading />}>
            <ContratReviewV3 />
          </Suspense>
        ),
        title: "合同审查",
      },
      {
        path: "/contract-review-v4",
        element: (
          <Suspense fallback={<Loading />}>
            <ContratReviewV4 />
          </Suspense>
        ),
        title: "合同审查",
      },
      {
        path: "/policy-insight-system",
        element: (
          <Suspense fallback={<Loading />}>
            <PolicyInsightSystem />
          </Suspense>
        ),
        title: "政策日报、解读、制度校验",
      },
      {
        path: "/enterprise-credit-assessment",
        element: (
          <Suspense fallback={<Loading />}>
            <EnterpriseCreditEvaluation />
          </Suspense>
        ),
        title: "企业信用评估",
      },
      {
        path: "/customer-churn-warning",
        element: (
          <Suspense fallback={<Loading />}>
            <CustomerChurnWarning />
          </Suspense>
        ),
        title: "客户流失挽留",
      },
      {
        path: "/employee-knowledge-answer",
        element: (
          <Suspense fallback={<Loading />}>
            <EmployeeKnowledgeAnswer />
          </Suspense>
        ),
        title: "员工知识回答",
      },
      {
        path: "/financial-products",
        element: (
          <Suspense fallback={<Loading />}>
            <FinancialProducts />
          </Suspense>
        ),
        title: "理财产品推荐",
      },
      {
        path: "/due-diligence-report",
        element: (
          <Suspense fallback={<Loading />}>
            <DueDiligenceReport />
          </Suspense>
        ),
        title: "尽调报告生成",
      },
      {
        path: "/order-situation",
        element: (
          <Suspense fallback={<Loading />}>
            <OrderSituation />
          </Suspense>
        ),
        title: "订单情况",
      },
      {
        path: "/feasibility-report",
        element: (
          <Suspense fallback={<Loading />}>
            <FeasibilityReport />
          </Suspense>
        ),
        title: "可行性报告",
      },
      {
        path: "/document-verification",
        element: (
          <Suspense fallback={<Loading />}>
            <DocumentVerification />
          </Suspense>
        ),
        title: "公文错别字校验",
      },
      {
        path: "/develop-process-AIDS",
        element: (
          <Suspense fallback={<Loading />}>
            <DevelopProcessAIDS />
          </Suspense>
        ),
        title: "开发流程辅助工具",
      },
      {
        path: "/enterprise-profile",
        element: (
          <Suspense fallback={<Loading />}>
            <EnterpriseProfile />
          </Suspense>
        ),
        title: "企业画像",
      },
      {
        path: "/personal-profile",
        element: (
          <Suspense fallback={<Loading />}>
            <PersonalProfile />
          </Suspense>
        ),
        title: "个人画像",
      },
      {
        path: "/document-assistant",
        element: (
          <Suspense fallback={<Loading />}>
            <DocumentAssistant />
          </Suspense>
        ),
        title: "公文助手2.0",
      },
      {
        path: "/work-report",
        element: (
          <Suspense fallback={<Loading />}>
            <WorkReport />
          </Suspense>
        ),
        title: "工作报告",
      },
      {
        path: "/speaking-script",
        element: (
          <Suspense fallback={<Loading />}>
            <SpeakingScript />
          </Suspense>
        ),
        title: "讲话稿",
      },
      {
        path: "/invitation",
        element: (
          <Suspense fallback={<Loading />}>
            <Invitation />
          </Suspense>
        ),
        title: "邀请函",
      },
      {
        path: "/experience",
        element: (
          <Suspense fallback={<Loading />}>
            <Experience />
          </Suspense>
        ),
        title: "心得体会",
      },
      {
        path: "/request-report",
        element: (
          <Suspense fallback={<Loading />}>
            <RequestReport />
          </Suspense>
        ),
        title: "请示",
      },
      {
        path: "/email",
        element: (
          <Suspense fallback={<Loading />}>
            <Email />
          </Suspense>
        ),
        title: "邮件",
      },
      {
        path: "/announcement",
        element: (
          <Suspense fallback={<Loading />}>
            <Announcement />
          </Suspense>
        ),
        title: "公告",
      },
      {
        path: "/notice",
        element: (
          <Suspense fallback={<Loading />}>
            <Notice />
          </Suspense>
        ),
        title: "通知",
      },
      {
        path: "/document-format-verification",
        element: (
          <Suspense fallback={<Loading />}>
            <DocumentFormatVerification />
          </Suspense>
        ),
        title: "公文格式校验",
      },
      {
        path: "/document-sensitive-inspection",
        element: (
          <Suspense fallback={<Loading />}>
            <DocumentSensitiveInspection />
          </Suspense>
        ),
        title: "公文敏感词稽查",
      },
      {
        path: "/policy-compliance-verifier",
        element: (
          <Suspense fallback={<Loading />}>
            <PolicyComplianceVerifier />
          </Suspense>
        ),
        title: "政策查询、解读、制度校验",
      },
      {
        path: "/policy-interpretation",
        element: (
          <Suspense fallback={<Loading />}>
            <PolicyInterpretation />
          </Suspense>
        ),
        title: "政策解读",
      },
      {
        path: "/announcement-inquiry",
        element: (
          <Suspense fallback={<Loading />}>
            <AnnouncementInquiry />
          </Suspense>
        ),
        title: "公告查询",
      },
      {
        path: "/financial-audit-assistant",
        element: (
          <Suspense fallback={<Loading />}>
            <FinancialAuditAssistant />
          </Suspense>
        ),
        title: "财务审计助手",
      },
      {
        path: "/policy-matching",
        element: (
          <Suspense fallback={<Loading />}>
            <PolicyMatching />
          </Suspense>
        ),
        title: "政策匹配",
      },
      {
        path: "/policy-match-details",
        element: (
          <Suspense fallback={<Loading />}>
            <PolicyMatchDetails />
          </Suspense>
        ),
        title: "政策匹配",
      },
      {
        path: "/review-material",
        element: (
          <Suspense fallback={<Loading />}>
            <ReviewMaterial />
          </Suspense>
        ),
        title: "入党材料审核",
      },
      {
        path: "/party-building-assistant",
        element: (
          <Suspense fallback={<Loading />}>
            <PartyBuildingAssistant />
          </Suspense>
        ),
        title: "党建心得助手",
      },
      {
        path: "/recommended-activities",
        element: (
          <Suspense fallback={<Loading />}>
            <RecommendedActivities />
          </Suspense>
        ),
        title: "党建活动推荐",
      },
      {
        path: "/financial-health-assessment",
        element: (
          <Suspense fallback={<Loading />}>
            <FinancialHealthAssessment />
          </Suspense>
        ),
        title: "财务健康度评估助手",
      },
      {
        path: "/business-query",
        element: (
          <Suspense fallback={<Loading />}>
            <BusinessQuery />
          </Suspense>
        ),
        title: "业务查询助手",
      },
      {
        path: "/ragas",
        element: (
          <Suspense fallback={<Loading />}>
            <Ragas />
          </Suspense>
        ),
        title: "RAG 数据评估",
      },
      {
        path: "/regulatory-search",
        element: (
          <Suspense fallback={<Loading />}>
            <RegulatorySearch />
          </Suspense>
        ),
        title: "制度检索助手",
      },
      {
        path: "/financial-analysis",
        element: (
          <Suspense fallback={<Loading />}>
            <FinancialanAlysis />
          </Suspense>
        ),
        title: "财报分析助手",
      },
      {
        path: "/regulatory-reporting",
        element: (
          <Suspense fallback={<Loading />}>
            <RegulatoryReporting />
          </Suspense>
        ),
        title: "监管报送",
      },
      {
        path: "/loan-approval",
        element: (
          <Suspense fallback={<Loading />}>
            <LoanApproval />
          </Suspense>
        ),
        title: "贷款审批报告",
      },
      {
        path: "/trust-monthly-report",
        element: (
          <Suspense fallback={<Loading />}>
            <TrustMonthlyReport />
          </Suspense>
        ),
        title: "信托项目运营报告",
      },
      {
        path: "/securities-valuation",
        element: (
          <Suspense fallback={<Loading />}>
            <SecuritiesValuation />
          </Suspense>
        ),
        title: "证券估值报告",
      },
      {
        path: "/prd-contract-ver",
        element: (
          <Suspense fallback={<Loading />}>
            <PrdContractVer />
          </Suspense>
        ),
        title: "合同校验系统",
      },
      {
        path: "/prompt-optimizer",
        element: (
          <Suspense fallback={<Loading />}>
            <PromptOptimizer />
          </Suspense>
        ),
        title: "提示词优化器",
      },
      {
        path: "/layout-recognition",
        element: (
          <Suspense fallback={<Loading />}>
            <LayoutRecognition />
          </Suspense>
        ),
        title: "版面识别",
      },
      {
        path: "/prd-purcontract-ver",
        element: (
          <Suspense fallback={<Loading />}>
            <PrdPurcontractVer />
          </Suspense>
        ),
        title: "合同对比系统",
      },
      {
        path: "research-report",
        element: (
          <Suspense fallback={<Loading />}>
            <ResearchReport />
          </Suspense>
        ),
        title: "行业研究报告生成",
      },
      {
        path: "/contract-scene-set",
        element: (
          <Suspense fallback={<Loading />}>
            <ContractSceneSet />
          </Suspense>
        ),
        title: "智能合同场景集",
      },
      {
        path: "/contract-scene-set-v2",
        element: (
          <Suspense fallback={<Loading />}>
            <ContractSceneSetV2 />
          </Suspense>
        ),
        title: "智能合同场景集",
      },
      {
        path: "/contract-data-extractor",
        element: (
          <Suspense fallback={<Loading />}>
            <ContractDataExtractor />
          </Suspense>
        ),
        title: "合同信息提取汇总助手",
      },

      {
        path: "/lingua-doc-builder",
        element: (
          <Suspense fallback={<Loading />}>
            <LinguaDocBuilder />
          </Suspense>
        ),
        title: "智能文档生成",
      },
      {
        path: "/cashflow-analysis",
        element: (
          <Suspense fallback={<Loading />}>
            <CashflowAnalysis />
          </Suspense>
        ),
        title: "企业现金流预测",
      },
      {
        path: "/meeting-audio-knowledge-base",
        element: (
          <Suspense fallback={<Loading />}>
            <MeetingAudioKnowledgeBase />
          </Suspense>
        ),
        title: "会议录音知识库",
      },
      {
        path: "/meeting-video-knowledge-base",
        element: (
          <Suspense fallback={<Loading />}>
            <MeetingVideoKnowledgeBase />
          </Suspense>
        ),
        title: "路演视频识别总结报告",
      },
      {
        path: "/voice-quality-assurance",
        element: (
          <Suspense fallback={<Loading />}>
            <VoiceQualityAssurance />
          </Suspense>
        ),
        title: "语音智能质检",
      },
      {
        path: "/gujing-english-training",
        element: (
          <Suspense fallback={<Loading />}>
            <GujingEnglishTraining />
          </Suspense>
        ),
        title: "会议录音知识库",
      },
      {
        path: "/chart-generation",
        element: (
          <Suspense fallback={<Loading />}>
            <ChartGeneration />
          </Suspense>
        ),
        title: "Excel图表生成",
      },
      {
        path: "/gujing-command-dialogue",
        element: (
          <Suspense fallback={<Loading />}>
            <GujingCommandDialogue />
          </Suspense>
        ),
        title: "设备操作指令对话",
      },
      {
        path: "/not-found",
        element: (
          <Suspense fallback={<Loading />}>
            <NotFound />
          </Suspense>
        ),
        errorElement: <></>,
        title: "404 未找到",
      },
    ],
  },
  {
    path: "*",
    element: <Navigate to="/not-found" replace />,
  },
];

export const routers = createHashRouter(routes);
