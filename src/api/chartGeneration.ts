import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_CHART_GENERATION_TOKEN = import.meta.env['VITE_CHART_GENERATION_TOKEN'] || ''

export async function ChartGeneration(
  inputs: any,
  { onMessage, onError, onFinish }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_CHART_GENERATION_TOKEN)
  return client.sendMessage({
    type: 'chat',
    query: inputs.query,
    user: 'resume-generation',
    inputs: {
      appKey: VITE_CHART_GENERATION_TOKEN,
      query: inputs.query,
      key: inputs.key,
      chartType: inputs.chartType,
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish
  })
}
