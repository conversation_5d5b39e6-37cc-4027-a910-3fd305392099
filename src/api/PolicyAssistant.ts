import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const VITE_POLICY_QUERY_TOKEN = import.meta.env['VITE_POLICY_QUERY_TOKEN'] || "";

export async function policyQuery(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_POLICY_QUERY_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: inputs.query,
    user: "resume-generation",
    inputs: {
      appKey: VITE_POLICY_QUERY_TOKEN,
      ...inputs
    },
    files: [],
    onMessage,
    onError,
    onFinish
  });
}