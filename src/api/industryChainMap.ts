import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
// const VITE_MATERIAL_SUMMARY_TOKEN = import.meta.env['VITE_MATERIAL_SUMMARY_TOKEN'] || "";

export async function industryChainMapContract(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(inputs.appData.id);
  return client.sendMessage({
    type: "chat",
    query: inputs.query,
    user: "resume-generation",
    inputs: {
      appKey: inputs.appData.id,
      personalLibs: "",
      query: inputs.query,
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish
  });
}