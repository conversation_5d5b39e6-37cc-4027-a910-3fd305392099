import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_CONTRACT_TOOLS_XHLH_TOKEN = import.meta.env['VITE_CONTRACT_TOOLS_XHLH_TOKEN'] || ''

export async function auditMark(
  inputs: any,
  { onMessage, onError, onFinish }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_CONTRACT_TOOLS_XHLH_TOKEN)
  return client.sendMessage({
    type: 'chat',
    query: inputs.query,
    user: 'resume-generation',
    inputs: {
      appKey: VITE_CONTRACT_TOOLS_XHLH_TOKEN,
      // personalLibs: "",
      query: inputs.query,
      key: inputs.key,
      type: inputs.type
      // verInfo: inputs.verInfo
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish
  })
}
