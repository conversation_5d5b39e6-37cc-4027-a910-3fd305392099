import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const REVIEW_MATERIAL_TOKEN = import.meta.env['VITE_REVIEW_MATERIAL_TOKEN'] || "";

export async function reviewMaterial(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(REVIEW_MATERIAL_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: "1",
    user: "resume-generation",
    inputs: {
      appKey: REVIEW_MATERIAL_TOKEN,
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish
  });
}