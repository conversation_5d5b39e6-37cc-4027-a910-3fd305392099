import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const appKey = import.meta.env['VITE_RECOMMENDED_ACTIVITIES_TOKEN'] || "";

export async function recommendedActivities(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(appKey);
  return client.sendMessage({
    type: "chat",
    query: inputs.query,
    user: "resume-generation",
    inputs: {
      appKey,
      ...inputs
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish
  });
}