import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_POLICY_COMPLIANCE_VERIFIER =
  import.meta.env['VITE_POLICY_COMPLIANCE_VERIFIER'] || ''

export async function policyComplianceVerifier(
  inputs: any,
  {
    onMessage,
    onError,
    onFinish,
  }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_POLICY_COMPLIANCE_VERIFIER)
  return client.sendMessage({
    type: 'chat',
    query: '1',
    user: 'resume-generation',
    inputs: {
      appKey: VITE_POLICY_COMPLIANCE_VERIFIER,
      ...inputs,
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish,
  })
}
