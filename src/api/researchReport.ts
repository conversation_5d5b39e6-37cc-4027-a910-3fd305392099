import { SSEClient, SSERequest } from '@/utils/sse-client'

export async function researchReport(
  { appKey, query = '1', files = [], ...inputs }: any,
  { onMessage, onError, onFinish }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(appKey)
  return client.sendMessage({
    type: 'chat',
    query: query,
    user: 'resume-generation',
    inputs: {
      appKey,
      ...inputs
    },
    files: files,
    onMessage,
    onError,
    onFinish
  })
}
