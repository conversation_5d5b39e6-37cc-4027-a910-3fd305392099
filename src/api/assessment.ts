import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const VITE_ENTERPRISE_CREDIT_ASSESSMENT_TOKEN = import.meta.env['VITE_ENTERPRISE_CREDIT_ASSESSMENT_TOKEN'] || "";

export async function getAssessment(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_ENTERPRISE_CREDIT_ASSESSMENT_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: inputs,
    user: "resume-generation",
    inputs: {
      appKey: VITE_ENTERPRISE_CREDIT_ASSESSMENT_TOKEN,
      query: inputs
    },
    files: [],
    onMessage,
    onError,
    onFinish
  });
}