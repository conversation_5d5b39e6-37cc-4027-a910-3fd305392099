import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_CONTRACT_QUERY_ASSISTANT_TOKEN = import.meta.env['VITE_CONTRACT_QUERY_ASSISTANT_TOKEN'] || ''

export async function contractQueryAssistant(
  inputs: any,
  { onMessage, onError, onFinish }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_CONTRACT_QUERY_ASSISTANT_TOKEN)
  return client.sendMessage({
    type: 'chat',
    query: inputs.query,
    user: 'resume-generation',
    inputs: {
      appKey: VITE_CONTRACT_QUERY_ASSISTANT_TOKEN,
      query: inputs.query,
      key: inputs.key
    },
    files: [],
    onMessage,
    onError,
    onFinish
  })
}
