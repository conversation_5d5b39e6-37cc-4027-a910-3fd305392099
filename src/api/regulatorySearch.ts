import {SSEClient, SSERequest} from "@/utils/sse-client.ts";

const VITE_HJCW_RAG_TOKEN = import.meta.env['VITE_HJCW_RAG_TOKEN'] || "";

export async function getKnowledgeAnswer(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_HJCW_RAG_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: inputs,
    user: "hjcw-user",
    inputs: {
      appKey: VITE_HJCW_RAG_TOKEN,
      query: inputs
    },
    files: [],
    onMessage,
    onError,
    onFinish
  });
}
