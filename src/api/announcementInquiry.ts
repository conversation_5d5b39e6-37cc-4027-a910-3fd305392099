import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_ANNOUNCEMENT_INQUIRY =
  import.meta.env['VITE_ANNOUNCEMENT_INQUIRY'] || ''

export async function documentSummary(
  inputs: any,
  {
    onMessage,
    onError,
    onFinish,
  }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_ANNOUNCEMENT_INQUIRY)
  return client.sendMessage({
    type: 'chat',
    query: inputs,
    user: 'resume-generation',
    inputs: {
      appKey: VITE_ANNOUNCEMENT_INQUIRY,
      query: inputs,
    },
    files: [],
    onMessage,
    onError,
    onFinish,
  })
}
