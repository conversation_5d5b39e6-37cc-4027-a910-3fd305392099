import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const VITE_WORK_ORDER_QUERY_TOKEN = import.meta.env['VITE_WORK_ORDER_QUERY_TOKEN'] || "";

export async function workOrderQuery(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_WORK_ORDER_QUERY_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: inputs,
    user: "resume-generation",
    inputs: {
      appKey: VITE_WORK_ORDER_QUERY_TOKEN,
      query: inputs
    },
    files: [],
    onMessage,
    onError,
    onFinish
  });
}