import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const VITE_FINANCIAL_PRODUCTS_TOKEN = import.meta.env['VITE_FINANCIAL_PRODUCTS_TOKEN'] || "";

export async function getFinancialProducts(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_FINANCIAL_PRODUCTS_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: inputs,
    user: "resume-generation",
    inputs: {
      appKey: VITE_FINANCIAL_PRODUCTS_TOKEN,
      query: inputs
    },
    files: [],
    onMessage,
    onError,
    onFinish
  });
}