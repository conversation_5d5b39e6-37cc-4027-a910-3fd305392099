import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_MEETING_VIDEO_TOKEN =
  import.meta.env['VITE_MEETING_VIDEO_TOKEN'] || ''

export async function meetingVideoKnowledge(
  { query = '1', files = [], ...inputs }: any,
  {
    onMessage,
    onError,
    onFinish,
  }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_MEETING_VIDEO_TOKEN)
  return client.sendMessage({
    type: 'chat',
    query: query,
    user: 'resume-generation',
    inputs: {
      appKey: VITE_MEETING_VIDEO_TOKEN,
      ...inputs,
    },
    files: files,
    onMessage,
    onError,
    onFinish,
  })
}
