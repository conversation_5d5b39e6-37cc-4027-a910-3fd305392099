import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const VITE_SYSTEM_VERIFICATION_TOKEN = import.meta.env['VITE_SYSTEM_VERIFICATION_TOKEN'] || "";

export async function systemContract(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_SYSTEM_VERIFICATION_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: inputs.query,
    user: "resume-generation",
    inputs: {
      appKey: VITE_SYSTEM_VERIFICATION_TOKEN,
      personalLibs: "",
      query: inputs.query,
      verInfo: inputs.verInfo
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish
  });
}