import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const VITE_POLICY_AGENT_TOKEN = import.meta.env['VITE_POLICY_AGENT_TOKEN'] || "";

export async function generateDailyReport(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_POLICY_AGENT_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: inputs.Question,
    user: "resume-generation",
    inputs,
    onMessage,
    onError,
    onFinish
  });
}