import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const VITE_MATERIAL_SUMMARY_TOKEN = import.meta.env['VITE_MATERIAL_SUMMARY_TOKEN'] || "";

export async function materialContract(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_MATERIAL_SUMMARY_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: inputs.query,
    user: "resume-generation",
    inputs: {
      appKey: VITE_MATERIAL_SUMMARY_TOKEN,
      personalLibs: "",
      query: inputs.query,
      key: inputs.key,
      verInfo: inputs.verInfo
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish
  });
}