import { SSEClient, SSERequest } from "@/utils/sse-client";
const appKey = import.meta.env['VITE_REVIEW_CONTRAT_V4_TOKEN'] || "";

export async function reviewContrat({ files, query, ...inputs }: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(appKey);
  return client.sendMessage({
    type: "chat",
    query: JSON.stringify(query) || "1",
    user: "resume-generation",
    inputs: {
      appKey,
      ...inputs
    },
    files: files || [],
    onMessage,
    onError,
    onFinish
  });
}
