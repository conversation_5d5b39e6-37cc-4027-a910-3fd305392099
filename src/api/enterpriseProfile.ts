import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_ENTERPRISE_PROFILE_TOKEN =
  import.meta.env['VITE_ENTERPRISE_PROFILE_TOKEN'] || ''

export async function enterpriseProfile(
  inputsobj: any,
  {
    onMessage,
    onError,
    onFinish,
  }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_ENTERPRISE_PROFILE_TOKEN)
  return client.sendMessage({
    type: 'chat',
    inputs: {
      appKey: VITE_ENTERPRISE_PROFILE_TOKEN,
      query: inputsobj.query,
      ent_name: inputsobj.query,
    },
    query: inputsobj.query,
    user: 'resume-generation',
    files: [],
    onMessage,
    onError,
    onFinish,
  })
}
