import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const VITE_SUMMARY_AGENT_TOKEN = import.meta.env['VITE_SUMMARY_AGENT_TOKEN'] || "";

export async function summaryContract(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_SUMMARY_AGENT_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: "11",
    user: "resume-generation",
    inputs: {
      appKey: VITE_SUMMARY_AGENT_TOKEN,
      personalLibs: "",
      query: "11"
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish
  });
}