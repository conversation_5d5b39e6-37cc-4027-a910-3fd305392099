import { SSEClient, SSERequest } from '@/utils/sse-client'
const VITE_API_BASE_DOC = import.meta.env['VITE_API_BASE_DOC'] || ''

export async function contractSceneSet(
  { query = '1', files = [], appKey, ...inputs }: any,
  { onMessage, onError, onFinish }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  new SSEClient(appKey).sendMessage({
    type: 'chat',
    query: query,
    user: 'resume-generation',
    inputs: {
      appKey,
      ...inputs
    },
    files,
    onMessage,
    onError,
    onFinish
  })
}

export async function getKnowledgeData(data: object, Tenantid: string, Token: string) {
  return fetch(`${VITE_API_BASE_DOC}/knowledge/base/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Tenantid,
      Token
    },
    body: JSON.stringify(data)
  }).then(res => res.json())
}

export function uploadKnowledgeFile(data: any, Tenantid: string, Token: string): Promise<any> {
  return fetch(`${VITE_API_BASE_DOC}/knowledge/doc/teamUpload`, {
    method: 'POST',
    headers: {
      Tenantid,
      Token
    },
    body: data
  }).then(res => res.json())
}
