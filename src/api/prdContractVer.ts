import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const appkey = import.meta.env['VITE_PRD_CONTRACT_VER_TOKEN'] || ''

export async function prdContractVer(
  { query, files, ...inputs }: any,
  { onMessage, onError, onFinish }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(appkey)
  return client.sendMessage({
    type: 'chat',
    query: query,
    user: 'resume-generation',
    inputs: {
      appKey: appkey,
      ...inputs
    },
    files: files,
    onMessage,
    onError,
    onFinish
  })
}
