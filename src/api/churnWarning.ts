import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const VITE_CUSTOMER_CHURN_WARNING_TOKEN = import.meta.env['VITE_CUSTOMER_CHURN_WARNING_TOKEN'] || "";

export async function getChurnWarning(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_CUSTOMER_CHURN_WARNING_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: inputs,
    user: "resume-generation",
    inputs: {
      appKey: VITE_CUSTOMER_CHURN_WARNING_TOKEN,
      query: inputs
    },
    files: [],
    onMessage,
    onError,  
    onFinish
  });
}