import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_DEVELOP_PROCESS_AIDS =
  import.meta.env['VITE_DEVELOP_PROCESS_AIDS'] || ''

export async function developProcess(
  { query = 1, files, ...inputs }: any,
  {
    onMessage,
    onError,
    onFinish,
  }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_DEVELOP_PROCESS_AIDS)
  return client.sendMessage({
    type: 'chat',
    inputs: {
      appKey: VITE_DEVELOP_PROCESS_AIDS,
      ...inputs,
    },
    query: query,
    responseMode: 'streaming',
    conversationId: '',
    user: 'web-user-42z2jwa5r',
    files: [],
    onMessage,
    onError,
    onFinish,
  })
}
