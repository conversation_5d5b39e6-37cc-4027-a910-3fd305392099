import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const appKey = import.meta.env['VITE_LAYOUT_RECOGNITION_TOKEN'] || ''

export async function layoutRecognition(
  { files, ...inputs }: any,
  { onMessage, onError, onFinish }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(appKey)
  return client.sendMessage({
    type: 'chat',
    query: '1',
    user: 'resume-generation',
    inputs: {
      appKey,
      ...inputs
    },
    files: files,
    onMessage,
    onError,
    onFinish
  })
}
