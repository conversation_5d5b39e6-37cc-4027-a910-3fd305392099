import { SSEClient, SSERequest } from '@/utils/sse-client.ts'

const VITE_API_BASE_DOC = import.meta.env['VITE_API_BASE_DOC'] || ''
const VITE_AI_API_BASE = import.meta.env['VITE_AI_API_BASE'] || ''
const VITE_TEMPLATE_AGENT_TOKEN = import.meta.env['VITE_TEMPLATE_AGENT_TOKEN'] || ''

export async function listAllTemplates(title = '') {
  const res = await fetch(`${VITE_API_BASE_DOC}/doc/resumeTemp/list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      title
    })
  })
  return await res.json()
}

export async function convertFileToPDF(file: File) {
  const formData = new FormData()
  formData.append('file', file)

  return fetch(`${VITE_API_BASE_DOC}/doc/resume/formatConvert`, {
    method: 'POST',
    body: formData
  })
}

export async function uploadFile(file: File, token: string) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('user', 'resume-generation')
  return fetch(`${VITE_AI_API_BASE}/files/upload`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`
    },
    body: formData
  }).then(res => res.json())
}

export async function templatePadding(templateId: string, data: object) {
  return fetch(`${VITE_API_BASE_DOC}/doc/resume/padding?templateId=${templateId}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      paddingParameter: data
    })
  }).then(res => res.json())
}

export async function structGeneration(
  id: string,
  inputs: any,
  { onMessage, onError, onFinish }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>,
  token = VITE_TEMPLATE_AGENT_TOKEN
) {
  const client = new SSEClient(token)
  return client.sendMessage({
    type: 'chat',
    query: '生成',
    user: 'resume-generation',
    inputs,
    files: [
      {
        type: 'document',
        transfer_method: 'local_file',
        upload_file_id: id
      }
    ],
    onMessage,
    onError,
    onFinish
  })
}

export function downloadPaddingResult(shortUrl: string) {
  const link = document.createElement('a')
  link.href = `https://copilot.sino-bridge.com/api/langwell-doc-server/doc/resume/download/${shortUrl}`
  link.download = ''
  document.body.appendChild(link)
  link.click()
  link.remove()
}

export async function getListByTextSql(sql: string) {
  return fetch(`${VITE_API_BASE_DOC}/knowledge/retrieval/textSql?sql=${encodeURIComponent(sql)}`, {
    method: 'GET'
  }).then(res => res.json())
}

/**
 * 根据GROUP BY字段和条件值拼接SQL语句
 *
 * @param whereSql 带WHERE条件的SQL语句
 * @param groupBySql 带GROUP BY的SQL语句
 * @param additionalCondition 附加条件的值
 * @return 拼接后的SQL语句
 */
export async function getCombineSqlWithCondition({
  whereSql,
  groupBySql,
  additionalCondition
}: {
  whereSql?: string
  groupBySql?: string
  additionalCondition?: string
}) {
  let query = ''
  if (whereSql) {
    query += `whereSql=${encodeURIComponent(whereSql)}`
  }
  if (groupBySql) {
    query += `&groupBySql=${encodeURIComponent(groupBySql)}`
  }
  if (additionalCondition) {
    query += `&additionalCondition=${encodeURIComponent(additionalCondition)}`
  }
  return fetch(`${VITE_API_BASE_DOC}/knowledge/retrieval/combineSqlWithCondition?${query}`, {
    method: 'GET'
  }).then(res => res.json())
}
