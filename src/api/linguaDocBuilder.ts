import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const appKey = import.meta.env['VITE_LINGUADOCBUILDER_TOKEN'] || ''

export async function linguaDocBuilder(
  { query = '1', files = [], ...inputs }: any,
  { onMessage, onError, onFinish }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(appKey)
  return client.sendMessage({
    type: 'chat',
    query,
    user: 'resume-generation',
    inputs: {
      appKey,
      ...inputs
    },
    files,
    onMessage,
    onError,
    onFinish
  })
}
