import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const appKey = import.meta.env['VITE_CASHFLOW_ANALYSIS_TOKEN'] || ''

export async function cashflowAnalysis(
  { query = '1', files = [], ...inputs }: any,
  { onMessage, onError, onFinish }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(appKey)
  return client.sendMessage({
    type: 'chat',
    query: query,
    user: 'resume-generation',
    inputs: {
      appKey: appKey,
      ...inputs
    },
    files: files,
    onMessage,
    onError,
    onFinish
  })
}
