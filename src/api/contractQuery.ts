import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const VITE_CONTRACT_QUERY_TOKEN = import.meta.env['VITE_CONTRACT_QUERY_TOKEN'] || "";

export async function contractQuery(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_CONTRACT_QUERY_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: inputs,
    user: "resume-generation",
    inputs: {
      appKey: VITE_CONTRACT_QUERY_TOKEN,
      query: inputs
    },
    files: [],
    onMessage,
    onError,
    onFinish
  });
}