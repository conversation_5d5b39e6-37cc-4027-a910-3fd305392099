import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const DOCUMENT_VERIFICATION_TOKEN = import.meta.env['VITE_DOCUMENT_VERIFICATION_TOKEN'] || "";

export async function verificationDocument(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(DOCUMENT_VERIFICATION_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: "1",
    user: "resume-generation",
    inputs: {
      appKey: DOCUMENT_VERIFICATION_TOKEN
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish
  });
}