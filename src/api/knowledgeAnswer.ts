import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const VITE_KNOWLEDGE_ANSWER_TOKEN = import.meta.env['VITE_KNOWLEDGE_ANSWER_TOKEN'] || "";

export async function getKnowledgeAnswer(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_KNOWLEDGE_ANSWER_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: inputs,
    user: "resume-generation",
    inputs: {
      appKey: VITE_KNOWLEDGE_ANSWER_TOKEN,
      query: inputs
    },
    files: [],
    onMessage,
    onError,
    onFinish
  });
}