const VITE_AI_API_BASE = import.meta.env['VITE_AI_API_BASE'] || ''
export async function feedbacks(messageId: string, token: string, rating: 'like' | 'dislike' | 'null') {
  return fetch(`${VITE_AI_API_BASE}/messages/${messageId}/feedbacks`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      rating,
      user: 'resume-generation',
      content: ''
    })
  }).then(res => res.json())
}
