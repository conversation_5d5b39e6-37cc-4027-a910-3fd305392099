import { SSEClient, SSERequest } from "@/utils/sse-client";
const appKey = import.meta.env['VITE_LOAN_APPROVAL_TOKEN'] || "";

export async function loanApproval({ query, files, ...inputs }: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(appKey);
  return client.sendMessage({
    type: "chat",
    query: query || '1',
    user: "resume-generation",
    inputs: {
      appKey,
      ...inputs
    },
    files: files || [],
    onMessage,
    onError,
    onFinish
  });
}