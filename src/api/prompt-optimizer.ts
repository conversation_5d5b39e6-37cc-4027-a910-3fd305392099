import {SSEClient, SSERequest} from "@/utils/sse-client.ts";

const VITE_PROMPT_OPTIMIZER_TOKEN = import.meta.env['VITE_PROMPT_OPTIMIZER_TOKEN'] || "";

export async function promptOptimizer(query: string, inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_PROMPT_OPTIMIZER_TOKEN);
  return client.sendMessage({
    type: "chat",
    query,
    user: "prompt-optimizer",
    inputs,
    files: [],
    onMessage,
    onError,
    onFinish
  });
}
