import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const VITE_REVIEW_CONTRACT_TOKEN = import.meta.env['VITE_REVIEW_CONTRACT_TOKEN'] || "";

export async function reviewContract(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_REVIEW_CONTRACT_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: inputs.query,
    user: "resume-generation",
    inputs: {
      appKey: VITE_REVIEW_CONTRACT_TOKEN,
      query: inputs.query,
      key: "46e648f6-826f-4da4-a3d2-2538b5582e88"
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish
  });
}