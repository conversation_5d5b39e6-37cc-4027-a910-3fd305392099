import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_DUE_DILIGENCE_REPORT_TOKEN = import.meta.env['VITE_DUE_DILIGENCE_REPORT_TOKEN'] || ''

export async function getDiligenceReport(
  {
    type,
    files,
    templateType,
    info,
    query,
    managerName,
    key
  }: {
    type: string
    files: any[]
    templateType?: string
    info?: string
    query?: string
    managerName?: string
    key?: string
  },
  { onMessage, onError, onFinish }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_DUE_DILIGENCE_REPORT_TOKEN)
  let inputs: any = {
    appKey: VITE_DUE_DILIGENCE_REPORT_TOKEN,
    type,
    key,
    managerName
  }
  if (templateType) {
    inputs.templateType = templateType
  }
  if (info) {
    inputs.info = info
  }
  return client.sendMessage({
    type: 'chat',
    query: query || type,
    user: 'resume-generation',
    inputs,
    files,
    onMessage,
    onError,
    onFinish
  })
}
