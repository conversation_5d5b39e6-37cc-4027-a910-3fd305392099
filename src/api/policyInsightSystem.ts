import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_POLICY_INSIGHT_SYSTEM_TOKEN = import.meta.env['VITE_POLICY_INSIGHT_SYSTEM_TOKEN'] || ''

export async function policyInsightSystem(
  inputs: any,
  { onMessage, onError, onFinish }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_POLICY_INSIGHT_SYSTEM_TOKEN)
  return client.sendMessage({
    type: 'chat',
    query: '1',
    user: 'resume-generation',
    inputs: {
      appKey: VITE_POLICY_INSIGHT_SYSTEM_TOKEN,
      question: inputs.query,
      node: inputs.node
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish
  })
}
