const VITE_API_BASE_VOICE = import.meta.env['VITE_API_BASE_VOICE'] || ''

// export async function wordPronunciation(data) {
//   const response = await fetch(`${VITE_API_BASE_VOICE}/api/v1/word-pronunciation/synthesize`, {
//     method: 'GET',
//   });
//   return response.json();
// }

// 播放音频
export async function wordPronunciation(data: object) {
  return fetch(`${VITE_API_BASE_VOICE}/api/v1/word-pronunciation/synthesize`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  }).then(res => res)
}

// 音频评估
export async function getEvaluatePronunciation(data: any) {
  return fetch(`${VITE_API_BASE_VOICE}/api/v1/pronunciation/evaluate`, {
    method: 'POST',
    // headers: {
    //   'Content-Type': 'multipart/form-data',
    // },
    body: data
  }).then(res => res.json())
}

// 语音转文字
export async function getVoiceRecognize(data: any) {
  return fetch(
    `${VITE_API_BASE_VOICE}/api/v1/stream/recognize/stream?enable_speaker_id=true&enable_timestamps=true&segment_length=10`,
    {
      method: 'POST',
      body: data
    }
  ).then(res => res)
}
