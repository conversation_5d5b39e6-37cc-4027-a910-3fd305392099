import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_ORDER_SITUATION_AGENT_TOKEN = import.meta.env['VITE_ORDER_SITUATION_AGENT_TOKEN'] || ''

export async function getOrderSituation(
  inputs: any,
  { onMessage, onError, onFinish }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_ORDER_SITUATION_AGENT_TOKEN)
  return client.sendMessage({
    type: 'chat',
    query: inputs.query,
    user: 'resume-generation',
    inputs: {
      appKey: VITE_ORDER_SITUATION_AGENT_TOKEN,
      query: inputs.query,
      key: inputs.key
    },
    files: [],
    onMessage,
    onError,
    onFinish
  })
}
