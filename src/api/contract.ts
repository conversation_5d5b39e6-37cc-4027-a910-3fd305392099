import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const VITE_CONTRACT_AGENT_TOKEN = import.meta.env['VITE_CONTRACT_AGENT_TOKEN'] || "";

export async function calibrationContract(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_CONTRACT_AGENT_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: "11",
    user: "resume-generation",
    inputs: {
      appKey: VITE_CONTRACT_AGENT_TOKEN,
      personalLibs: "",
      query: "11"
    },
    files: [
      {
        type: "document",
        transfer_method: "local_file",
        upload_file_id: inputs.id
      }
    ],
    onMessage,
    onError,
    onFinish
  });
}

const VITE_CONTRACT_TOOLSV2_TOKEN = import.meta.env['VITE_CONTRACT_TOOLSV2_TOKEN'] || "";

export async function calibrationContractV2(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_CONTRACT_TOOLSV2_TOKEN);
  return client.sendMessage({
    type: "chat",
    user: "resume-generation",
    query: "1",
    inputs: {
      key: inputs.key,
      appKey: VITE_CONTRACT_TOOLSV2_TOKEN,
      type_1: inputs.type,
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish
  });
}