import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const appKey = import.meta.env['VITE_PARTY_BUILDING_ASSISTANT_TOKEN'] || "";

export async function partyBuildingAssistant(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(appKey);
  return client.sendMessage({
    type: "chat",
    query: "1",
    user: "resume-generation",
    inputs: {
      appKey,
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish
  });
}