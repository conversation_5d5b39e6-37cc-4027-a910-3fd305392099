import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const apiKey =
  import.meta.env['VITE_DOCUMENT_SENSITIVE_INSPECTION'] || ''

export async function documentSensitiveInspection(
  inputs: any,
  {
    onMessage,
    onError,
    onFinish,
  }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(apiKey)
  return client.sendMessage({
    type: 'chat',
    query: '1',
    user: 'resume-generation',
    inputs: {
      appKey: apiKey,
      type: inputs.type || '',
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish,
  })
}
