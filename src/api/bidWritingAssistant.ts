import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_BID_WRITING_ASSISTANT_TOKEN =
  import.meta.env['VITE_BID_WRITING_ASSISTANT_TOKEN'] || ''

export async function bidWritingAssistantContract(
  inputs: any,
  {
    onMessage,
    onError,
    onFinish,
  }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_BID_WRITING_ASSISTANT_TOKEN)
  return {
    client,
    sendMessage: client.sendMessage({
      type: 'chat',
      query: inputs.query,
      user: 'resume-generation',
      inputs: {
        appKey: VITE_BID_WRITING_ASSISTANT_TOKEN,
        // personalLibs: '',
        is_long: inputs.is_long,
        query: inputs.query,
      },
      files: inputs.files,
      onMessage,
      onError,
      onFinish,
    }),
  }
}
