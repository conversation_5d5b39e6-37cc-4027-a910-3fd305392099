import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_FEASIBILITY_REPORT_TOKEN = import.meta.env['VITE_FEASIBILITY_REPORT_TOKEN'] || ''

export async function feasibilityReport(
  inputs: any,
  { onMessage, onError, onFinish }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_FEASIBILITY_REPORT_TOKEN)
  return client.sendMessage({
    type: 'chat',
    query: '1',
    user: 'resume-generation',
    inputs: {
      appKey: VITE_FEASIBILITY_REPORT_TOKEN,
      query: '1',
      key: inputs.key
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish
  })
}
