import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const VITE_DOCUMENT_SUMMARY_TOKEN = import.meta.env['VITE_DOCUMENT_SUMMARY_TOKEN'] || "";

export async function documentSummary(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_DOCUMENT_SUMMARY_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: inputs,
    user: "resume-generation",
    inputs: {
      appKey: VITE_DOCUMENT_SUMMARY_TOKEN,
      query: inputs
    },
    files: [],
    onMessage,
    onError,
    onFinish
  });
}