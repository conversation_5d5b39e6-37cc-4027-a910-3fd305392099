import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const appKey = import.meta.env['VITE_CONTRACT_QUERY_ZZ_TOKEN'] || "";

export async function businessQuery(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(appKey);
  return client.sendMessage({
    type: "chat",
    query: inputs,
    user: "resume-generation",
    inputs: {
      appKey,
      query: inputs
    },
    files: [],
    onMessage,
    onError,
    onFinish
  });
}