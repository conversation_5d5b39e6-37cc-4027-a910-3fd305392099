import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_CONTRACT_QUERY_TOKEN =
  import.meta.env['VITE_VOICE_QUALITY_ASSURANCE'] || ''

export async function voiceQualityAssurance(
  { query = '1', files = [], ...inputs }: any,
  {
    onMessage,
    onError,
    onFinish,
  }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_CONTRACT_QUERY_TOKEN)
  return client.sendMessage({
    type: 'chat',
    query: query,
    user: 'resume-generation',
    inputs: {
      appKey: VITE_CONTRACT_QUERY_TOKEN,
      ...inputs,
    },
    files: files,
    onMessage,
    onError,
    onFinish,
  })
}
