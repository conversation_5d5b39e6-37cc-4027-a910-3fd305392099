import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_CONTRACT_TOOLS_TOKEN =
  import.meta.env['VITE_CONTRACT_TOOLS_TOKEN'] || ''

export async function contractTools(
  inputs: any,
  {
    onMessage,
    onError,
    onFinish,
  }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_CONTRACT_TOOLS_TOKEN)
  return client.sendMessage({
    type: 'chat',
    query: inputs.query,
    user: 'resume-generation',
    inputs: {
      appKey: VITE_CONTRACT_TOOLS_TOKEN,
      // personalLibs: "",
      query: inputs.query,
      key: inputs.key,
      type: inputs.type,
      // verInfo: inputs.verInfo
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish,
  })
}
