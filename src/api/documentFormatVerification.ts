import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_DOCUMENT_FORMAT_VERIFICATION =
  import.meta.env['VITE_DOCUMENT_FORMAT_VERIFICATION'] || ''

export async function documentFormatVerification(
  inputs: any,
  {
    onMessage,
    onError,
    onFinish,
  }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_DOCUMENT_FORMAT_VERIFICATION)
  return client.sendMessage({
    type: 'chat',
    query: '1',
    user: 'resume-generation',
    inputs: {
      appKey: VITE_DOCUMENT_FORMAT_VERIFICATION,
    },
    files: inputs.files,
    onMessage,
    onError,
    onFinish,
  })
}
