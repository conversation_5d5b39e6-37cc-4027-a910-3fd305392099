import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_PERSONAL_PROFILE_TOKEN =
  import.meta.env['VITE_PERSONAL_PROFILE_TOKEN'] || ''

export async function personalProfile(
  inputsobj: any,
  {
    onMessage,
    onError,
    onFinish,
  }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_PERSONAL_PROFILE_TOKEN)
  return client.sendMessage({
    type: 'chat',
    inputs: {
      appKey: VITE_PERSONAL_PROFILE_TOKEN,
    },
    query: '1',
    user: 'resume-generation',
    files: inputsobj.files,
    onMessage,
    onError,
    onFinish,
  })
}
