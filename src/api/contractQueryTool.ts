import { SSEClient, SSERequest } from '@/utils/sse-client'
const appKey = import.meta.env['VITE_CONTRACT_QUERY_TOOL_TOKEN'] || ''
const API_BASE_NOTE = import.meta.env['VITE_API_BASE_NOTE'] || ''
const API_BASE_DOC = import.meta.env['VITE_API_BASE_DOC'] || ''

type AddNoteRequest = {
  title: string
  content: string
  type: 'PLAIN'
  url: string
  noteStyle?: {
    noteWidth?: number
    noteHeight?: number
    noteType?: 'fixed' | 'absolute'
    noteTop?: number
    noteLeft?: number
  }
}

export async function contractQueryTool(
  { type, ...inputs }: any,
  { onMessage, onError, onFinish }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(appKey)
  return client.sendMessage({
    type: 'chat',
    query: '1',
    user: 'resume-generation',
    inputs: {
      appKey,
      query: '1',
      type,
      ...inputs
    },
    files: [],
    onMessage,
    onError,
    onFinish
  })
}

export async function addByFile(
  { libName, file, dirId }: { libName: string; file: File; dirId?: string },
  Tenantid: string,
  Token: string
) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('libName', libName)
  if (dirId) {
    formData.append('dirId', dirId)
  }
  return fetch(`${API_BASE_DOC}/knowledge/base/enterprise/addByFile`, {
    method: 'POST',
    headers: {
      Tenantid,
      Token
    },
    body: formData
  }).then(res => res.json())
}

export async function addNote(data: AddNoteRequest, Tenantid: string, Token: string) {
  return fetch(`${API_BASE_NOTE}/notes/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Tenantid,
      Token
    },
    body: JSON.stringify(data)
  }).then(res => res.json())
}

export async function selectContract({
  contName,
  customerFullName,
  contNo,
  contAmt,
}: {
  contName?: string
  customerFullName?: string
  contNo?: string
  contAmt?: string | number
}) {
  const res = await fetch('/sino-contract-admin/eoss/contract/selectContract', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      contName,
      customerFullName,
      contNo,
      contAmt,
    })
  })
  return await res.json()
}
