import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const VITE_REGULATORY_VERIFICATION_TOKEN = import.meta.env['VITE_REGULATORY_VERIFICATION_TOKEN'] || "";

export async function regulatoryVerification(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(VITE_REGULATORY_VERIFICATION_TOKEN);
  return client.sendMessage({
    type: "chat",
    query: inputs,
    user: "resume-generation",
    inputs: {
      appKey: VITE_REGULATORY_VERIFICATION_TOKEN,
      query: inputs
    },
    files: [],
    onMessage,
    onError,
    onFinish
  });
}