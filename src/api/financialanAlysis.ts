import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const appKey = import.meta.env['VITE_FINANCIALAN_ALYSIS_TOKEN'] || "";

export async function financialanAlysis(inputs: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(appKey);
  return client.sendMessage({
    type: "chat",
    query: '1',
    user: "resume-generation",
    inputs: {
      appKey,
      files: inputs.files,
    },
    files: [],
    onMessage,
    onError,
    onFinish
  });
}