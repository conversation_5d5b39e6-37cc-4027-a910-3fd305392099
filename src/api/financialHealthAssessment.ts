import { SSEClient, SSERequest } from "@/utils/sse-client.ts";
const appKey = import.meta.env['VITE_FINANCIALHEALTHASSESSMENT_TOKEN'] || "";

export async function financialHealthAssessment({ query, ...inputs }: any, {
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onMessage" | "onError" | "onFinish">) {
  const client = new SSEClient(appKey);
  return client.sendMessage({
    type: "chat",
    query: query || '1',
    user: "resume-generation",
    inputs: {
      appKey,
      ...inputs
    },
    files: [],
    onMessage,
    onError,
    onFinish
  });
}