import { SSEClient, SSERequest } from '@/utils/sse-client.ts'
const VITE_DEVELOP_PROCESS_AIDS =
  import.meta.env['VITE_DEVELOP_PROCESS_AIDS'] || ''

export async function developProcess(
  { query, files, ...inputs }: any,
  {
    onMessage,
    onError,
    onFinish,
  }: Pick<SSERequest, 'onMessage' | 'onError' | 'onFinish'>
) {
  const client = new SSEClient(VITE_DEVELOP_PROCESS_AIDS)
  return client.sendMessage({
    // type: "chat",
    // query: inputs.query || "default-query",
    // user: "resume-generation",
    // inputs: {
    //   appKey: VITE_CONTRACT_AGENT_TOKEN,
    //   personalLibs: "",
    //   query: inputs.query || "default-query"
    // },
    // files: inputs.files || [],
    type: 'chat',
    inputs: {
      // type: inputsobj.type,
      // background: inputsobj.background,
      appKey: VITE_DEVELOP_PROCESS_AIDS,
      ...inputs,
      // dataModel: inputsobj.dataModel,
    },
    query: query,
    responseMode: 'streaming',
    conversationId: '',
    user: 'web-user-42z2jwa5r',
    files: [],
    onMessage,
    onError,
    onFinish,
  })
}
