/**
 * 提取指定标签内的内容 第一个
 * @param str 字符串
 * @param tag 标签
 * @returns 标签内的内容
 */
export const extractContent = (str: string, tag: string): string => {
  // 创建正则表达式以匹配指定标签内的内容
  const regex = new RegExp(`<${tag}>(.*?)</${tag}>`, 'gs')
  const matches: string[] = []
  let match: RegExpExecArray | null

  // 使用正则表达式提取内容
  while ((match = regex.exec(str)) !== null) {
    matches.push(match[1].trim()) // match[1] 是标签内的内容
  }

  return matches[0] || ''
}

/**
 * 提取指定标签的所有内容并放入数组（支持动态传入标签类型）
 * @param str 字符串
 * @param tag 要提取的标签名（如 "div"、"p"、"span"）
 * @returns 包含该标签所有内容的数组
 */
export const extractAllTagContents = (str: string, tag: string): string[] => {
  // 匹配指定标签的内容（支持自闭合标签）
  const regex = new RegExp(`<${tag}(?:\\s+[^>]*)?>(.*?)<\\/${tag}>|<${tag}(?:\\s+[^>]*)?\\/>`, 'gs')
  const matches: string[] = []
  let match: RegExpExecArray | null

  while ((match = regex.exec(str)) !== null) {
    // 如果是成对标签（如 <div>...</div>），提取内容
    if (match[1] !== undefined) {
      matches.push(match[1].trim())
    }
    // 如果是自闭合标签（如 <img />），可以返回空字符串或特殊标记
    else {
      matches.push('') // 或者 `<${tag}/>`，取决于需求
    }
  }

  return matches
}

// console.log(getNodeLevel("一、管理人基本情况"));  // 1
// console.log(getNodeLevel("（一）管理人概况"));    // 2
// console.log(getNodeLevel("1、管理人基本信息"));   // 3
// console.log(getNodeLevel("（1）资质信息"));       // 4
// console.log(getNodeLevel("a、补充说明"));        // 5
// console.log(getNodeLevel("其他内容"));           // 6
export const getNodeLevel = (title: string) => {
  // 匹配模式优先级从高到低
  const patterns = [
    { regex: /^([一二三四五六七八九十]+)、/, level: 1 }, // 第一层：一、二、
    { regex: /^（([一二三四五六七八九十]+)）/, level: 2 }, // 第二层：（一）（二）
    { regex: /^(\d+)、/, level: 3 }, // 第三层：1、2、
    { regex: /^（(\d+)）/, level: 4 }, // 第四层：（1）（2）
    { regex: /^([a-z])、/, level: 5 } // 第五层：a、b、
  ]

  // 去除首尾空格后匹配
  const cleanTitle = title.trim()

  for (const { regex, level } of patterns) {
    if (regex.test(cleanTitle)) {
      return level
    }
  }

  // 默认返回末级
  return 6
}

export function mergeObjectsByKeyPro(arr: object[]) {
  const resultMap = new Map()
  const order: string[] = []

  arr.forEach(obj => {
    Object.entries(obj).forEach(([key, value]) => {
      if (!resultMap.has(key)) {
        resultMap.set(key, [])
        order.push(key)
      }
      resultMap.get(key).push(value)
    })
  })

  return order.map(key => ({
    [key]: resultMap.get(key).join('\n')
  }))
}

/**
 * 合并对象数组
 * @param arr|object 对象 | 对象数组
 * @returns 合并后每个对象只包含一个键值对
 * @example
 * 测试用例
 * const input1 = [{ a: 1 }, { b: 2 }];
 * const input2 = { a: 1, b: 2 };
 *console.log(getAllKeyValuePairs(input1)); // 输出: [['a', 1], ['b', 2]]
 *console.log(getAllKeyValuePairs(input2)); // 输出: [['a', 1], ['b', 2]]
 */
export function getAllKeyValuePairs(data: object | object[]) {
  let result = []

  // 判断是否是数组
  if (Array.isArray(data)) {
    // 如果是数组，遍历每个对象
    data.forEach(item => {
      if (typeof item === 'object' && item !== null) {
        result.push(...Object.entries(item))
      }
    })
  } else if (typeof data === 'object' && data !== null) {
    // 如果是普通对象，直接获取键值对
    result.push(...Object.entries(data))
  }

  return result
}

export function updateSqlLimit(originalSql: string, page: number, pageSize: number) {
  // 计算偏移量
  const offset = (page - 1) * pageSize
  // 创建正则表达式，匹配 LIMIT 后的数字
  const limitRegex = /LIMIT\s*[^\n;]*/i
  // 如果查询中存在 LIMIT，替换其为新的 LIMIT 和 OFFSET
  const updatedSql = originalSql.replace(limitRegex, `LIMIT ${pageSize} OFFSET ${offset}`)
  // 返回更新后的 SQL 查询
  return updatedSql
}

export const getMdContent = (content: string) => {
  const regex = /```markdown([^```]*)```/
  const match = content.match(regex)
  if (match) return match[1].trim()
  return content.replace('```markdown', '').replace('```', '')
}

export const getJSONContent = (content: string) => {
  const regex = /```json([^```]*)```/
  const match = content.match(regex)
  if (match) return match[1].trim()
  return content.replace('```json', '').replace('```', '')
}

export function generateShortId(): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substring(2, 8)
  return `${timestamp}-${random}`
}

// 提取第一个完整JSON
export function extractFirstCompleteJSON(str: string): [string, string] | null {
  let stack = 0
  let startIndex = -1

  for (let i = 0; i < str.length; i++) {
    if (str[i] === '{') {
      if (stack === 0) startIndex = i
      stack++
    } else if (str[i] === '}') {
      stack--
      if (stack === 0 && startIndex !== -1) {
        const jsonStr = str.slice(startIndex, i + 1)
        const fullMatch = str.slice(0, i + 1)
        return [fullMatch, jsonStr]
      }
    }
  }
  return null
}

export function extractMarkdownFromString(text: string) {
  try {
    // 首先尝试匹配 ```markdown``` 格式
    const jsonBlockPattern = /```(?:markdown|MARKDOWN)\s*\n?(.*?)\s*```/s
    let match = text.trim().match(jsonBlockPattern)

    if (match) {
      return match[1].trim()
    } else {
      const pattern = /{.*}/s
      match = text.match(pattern)
      if (match) {
        return match[0].trim()
      }
    }
    return null
  } catch (e) {
    throw new Error(`发生错误：${e}`)
  }
}
