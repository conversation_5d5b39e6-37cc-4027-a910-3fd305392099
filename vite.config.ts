import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  console.log(`当前环境：${mode}`)

  return {
    base: './',
    plugins: [react()],
    resolve: {
      alias: [
        { find: '@', replacement: path.normalize(path.resolve(process.cwd(), 'src')) },
        { find: '@projects', replacement: path.normalize(path.resolve(process.cwd(), 'projects')) }
      ]
    },
    server: {
      headers: {
        'Cross-Origin-Embedder-Policy': 'require-corp',
        'Cross-Origin-Opener-Policy': 'same-origin'
      },
      proxy: {
        '/api': {
          // target: 'http://192.168.113.18:6001', // 测试
          target: 'https://copilot.sino-bridge.com', // 生产
          changeOrigin: true,
          ws: true,
          toProxy: true,
          rewrite: (path: string) => path.replace(new RegExp(`^/api`), 'api')
        },
        '/v1': {
          // target: 'http://192.168.113.90', // 测试
          target: 'https://copilot.sino-bridge.com', // 生产
          changeOrigin: true,
          ws: true,
          toProxy: true,
          rewrite: (path: string) => path.replace(new RegExp(`^/v1`), 'v1')
        },
        '/funasr': {
          target: 'http://1.94.230.254:8080', // 生产
          changeOrigin: true,
          ws: true,
          toProxy: true,
          rewrite: (path: string) => path.replace(new RegExp(`^/funasr`), '')
        },
        '/voice-api': {
          target: 'http://1.94.230.254:8000',
          changeOrigin: true,
          ws: true,
          toProxy: true,
          rewrite: (path: string) => path.replace(new RegExp(`^/voice-api`), '')
        }
      }
    }
  }
})
